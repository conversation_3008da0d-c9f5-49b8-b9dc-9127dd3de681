# 用户活动监控系统 - AI开发指令

## 🎯 项目目标
**项目名称**: User Activity Service/Collector/Table  
**背景**: 替代友盟，构建完全自主的B2B用户行为分析系统  
**核心目标**: 支持客户留存分析、商业价值评估、续约预警

## 📋 开发任务清单（按优先级执行）

### Phase 1: 数据基础设施 ⭐⭐⭐
1. **白名单表同步** - 飞书客户数据→ClickHouse
2. **事件收集API** - 前端事件上报接口
3. **事件存储表** - `datawarehouse.ods_user_activity_events` 
4. **后端事件收集器** - `UserActivityCollector` 服务

### Phase 2: Superset可视化配置 ⭐⭐
1. **使用粘性监控** - 日活、查询频次、留存率图表
2. **使用深度监控** - 会话质量、追问率图表
3. **内容价值监控** - 导出率、分享率、收藏率图表
4. **商业价值监控** - 团队渗透、工作时间使用率图表

## 🔧 核心技术任务

### 任务1: 白名单表设计与同步
**ClickHouse表**: `datawarehouse.users_whitelist`
```sql
CREATE TABLE datawarehouse.users_whitelist (
    phone String,                    -- 手机号（用户标识）
    customer_name String,            -- 客户名称（看板显示用）
    added_by String,                 -- 添加人
    is_internal_user UInt8,          -- 是否内部人员（1=是，统计时排除）
    additional String,               -- 扩展信息JSON: {"add_date": "2025-01-01", "ecommerce_category": "服装", "content_category": "美妆", "reading_category": "小说"}
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now()
) ENGINE = ReplacingMergeTree(updated_at)
ORDER BY phone;
```

**同步服务**: `app/tasks/sync_feishu_whitelist.py`
- 基于现有`app/client/feishu_client.py`读取飞书表格
- 将手机号作为用户标识（user_id = phone）
- 品类等扩展信息存储在additional JSON字段
- 定期同步（建议每小时同步）


### 任务2: 事件收集API设计
**接口**: `POST /api/v1/activity/events`

**请求示例**:
```json
{
    "event_name": "SEND_MESSAGE",
    "event_args": {
        "category": "chat",
        "action": "send:message",
        "label": "用户提问内容",
        "conversation_id": "conv_123",
        "assistant_type": "ecommerce"
    }
}
```

### 任务3: 事件存储表设计
**ClickHouse表**: `datawarehouse.ods_user_activity_events`
```sql
CREATE TABLE datawarehouse.ods_user_activity_events (
    event_time DateTime DEFAULT now(),  -- 事件发生时间，用于按日分区
    event_name String,           -- 事件名称（如：SEND_MESSAGE, EXPORT_PDF等），作为索引
    user_id String,              -- 用户ID
    source String,               -- 事件来源：web（前端） | backend（后端）
    event_args String,           -- 事件参数JSON，存储具体业务数据
    user_agent String,           -- 用户代理信息
    client_ip String             -- 客户端IP地址
) ENGINE = MergeTree()
PARTITION BY toDate(event_time)          -- 按日分区，便于查询和管理
ORDER BY (event_name, event_time, user_id)  -- event_name作为主要索引，提升运营分析查询效率
SETTINGS index_granularity = 8192;
```

**event_args参数示例**:
- 前端会话事件: `{"conversation_id": "conv_123", "assistant_type": "ecommerce"}`
- 后端查询事件: `{"assistant_type": "ecommerce", "message_content": "用户问题", "history_length": 3}`
- 文档导出事件: `{"document_id": "doc_456", "document_name": "销售报告"}`
- 登录事件: `{"login_method": "phone_code", "success": true}`

### 任务4: 事件类型注册与收集器

**设计方案**:
1. **事件注册机制** - `app/service/activity/events.py`
   - 使用Enum定义所有支持的事件类型
   - 包含前端25个埋点事件 + 后端业务事件
   - 提供事件类型验证功能

2. **统一事件收集器** - `app/service/activity/collector.py`
   - `UserActivityCollector`类，前后端复用
   - `record_event()` - 通用事件记录方法
   - `record_housekeeper_query()` - 后端助手查询专用方法
   - 事件类型验证，避免无效事件写入

3. **API集成方案** - `app/routers/activity.py`
   - 前端API自动从HTTP header获取user_agent和client_ip
   - 调用统一的UserActivityCollector服务
   - 支持单个事件和批量事件上报

**核心原则**:
- 事件类型集中管理，新增事件只需修改events.py
- 前后端使用相同的收集器，保证数据格式一致
- API层负责参数提取，业务层专注数据处理

## 📊 关键事件类型定义

### 后端事件（必须实现）
- `housekeeper:query` - 助手查询事件
  - 参数: assistant_type, message_content, conversation_id, history_length
- `housekeeper:content_module_params` - 内容模块参数事件
  - 参数: assistant_type, module_name, module_params, filters

### 前端事件（迁移现有埋点）

**登录相关**:
- `SEND_CODE` - 发送验证码
- `SUBMIT_LOGIN` - 提交登录

**点击相关**:
- `CLICK_VIDEO` - 点击视频
- `CLICK_BIAKE` - 点击流联百科

**搜索相关**:
- `SUBMIT_SEARCH` - 提交搜索

**会话相关**:
- `CREATE_CONVERSATION` - 创建会话
- `DELETE_CONVERSATION` - 删除会话
- `TOP_CONVERSATION` - 置顶会话

**会话记录相关**:
- `SEND_MESSAGE` - 发送消息（核心查询事件）
- `COMMAND_MESSAGE` - 发送命令行消息
- `CLEAR_CONTEXT` - 清除上下文
- `VISIT_HISTORY` - 访问会话历史

**文档相关**:
- `CREATE_DOCUMENT` - 创建文档
- `RENAME_DOCUMENT` - 重命名文档
- `TRASH_DOCUMENT` - 删除或恢复文档
- `DELETE_DOCUMENT` - 彻底删除文档
- `CLEAR_DOCUMENT` - 清除文档
- `REMOVE_DOCUMENT` - 彻底清除文档
- `COPYLINK_DOCUMENT` - 复制链接
- `VISIT_DOCUMENT` - 访问文档
- `LEAVE_DOCUMENT` - 离开文档
- `FAVORITE_DOCUMENT` - 收藏文档
- `EXPORT_PDF` - 导出PDF
- `EXPORT_WORD` - 导出Word
- `EXPORT_MARKDOWN` - 导出Markdown
- `PUSH_DOCUMENT` - 推送文档更新

**操作相关**:
- `TOGGLE_SIDEBAR` - 切换侧边栏
- `TOGGLE_SPLITEVIEW` - 切换子窗口

## 📊 Superset运营监控指标（参考SQL）

### 使用粘性指标
```sql
-- 日活跃度 = 每日使用用户 / 总开通用户（排除内部）
SELECT 
    toDate(event_time) as date,
    uniq(user_id) as daily_active_users,
    (SELECT count() FROM datawarehouse.users_whitelist WHERE is_internal_user = 0) as total_users,
    daily_active_users / total_users * 100 as daily_active_rate
FROM datawarehouse.ods_user_activity_events 
WHERE toDate(event_time) = today() 
  AND user_id NOT IN (SELECT phone FROM datawarehouse.users_whitelist WHERE is_internal_user = 1)

-- 平均日查询次数 = count(SEND_MESSAGE) / 活跃用户数
-- 高频用户占比 = 日查询≥15次用户 / 总用户
```

### 使用深度指标
```sql
-- 平均会话轮数：从housekeeper:query事件的history_length参数计算
-- 追问率：history_length > 0 的查询占比
SELECT 
    avg(JSONExtractInt(event_args, 'history_length')) as avg_rounds,
    countIf(JSONExtractInt(event_args, 'history_length') > 0) / count() * 100 as followup_rate
FROM datawarehouse.ods_user_activity_events 
WHERE event_name = 'housekeeper:query' AND toDate(event_time) = today()
```

### 内容价值指标
```sql
-- 导出率 = 导出事件数 / 查询事件数
SELECT 
    countIf(event_name IN ('EXPORT_PDF', 'EXPORT_WORD', 'EXPORT_MARKDOWN')) as export_count,
    countIf(event_name = 'SEND_MESSAGE') as query_count,
    export_count / query_count * 100 as export_rate
FROM datawarehouse.ods_user_activity_events 
WHERE toDate(event_time) = today()

-- 收藏率、分享率类似计算
```

**注意**: 以上SQL仅为参考，具体在Superset中配置图表时使用。后端系统只负责数据写入。

## 🏗️ 文件结构规划
```
app/
├── routers/
│   └── activity.py              # 事件收集API
├── service/
│   └── activity/
│       ├── events.py           # 事件类型注册（新增）
│       ├── collector.py        # 事件收集服务
│       ├── models.py          # 数据模型
│       └── repository.py      # ClickHouse数据访问
├── ch/
│   └── models/
│       ├── user_whitelist.py  # 白名单表模型
│       └── activity_events.py # 事件表模型
└── tasks/
    └── sync_whitelist.py      # 飞书数据同步任务
```

## ⚡ 技术要求
- **复用现有**: `app/client/feishu_client.py`, `app/ch/orm.py`
- **性能要求**: API响应<200ms，支持1000+并发
- **代码规范**: 使用Exception处理错误，Pydantic数据验证
- **异步处理**: 事件收集使用异步写入，避免阻塞主流程

## 🔄 开发顺序
1. **先建表** - ClickHouse表结构
2. **建API** - 事件收集接口
3. **建服务** - 后端事件记录器
4. **加埋点** - 关键业务流程添加事件记录
5. **做分析** - 运营指标计算和视图
6. **上面板** - Superset可视化

---
**开发原则**: 先实现核心数据收集，后完善分析功能。优先保证数据完整性和准确性。