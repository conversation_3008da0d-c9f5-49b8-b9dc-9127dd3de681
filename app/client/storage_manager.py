from app.client.redis_client import RedisClient
from app.client.s3client import S3Client
from app.routers.common import get_user_path
from app.db.common_type import SizeUnit

KEY_USER_TOTAL_STORAGE = "user_total_storage"
KEY_USER_USED_STORAGE = "user_used_storage"

DEFAULT_USER_STORAGE_LIMIT = SizeUnit.TB * 1  # 每个用户默认1T空间


class StorageManager:
    def __init__(self, user_id):
        self.redis_client = RedisClient().get_client()
        self.user_id = user_id

        # 如果redis中查询不到用户存储空间信息，则为其初始化一个存储空间
        if not self.check_user_exist():
            self.init_user_storage(DEFAULT_USER_STORAGE_LIMIT)

        # TODO total_storage后续可能需要存到数据库？
        self.total_storage = DEFAULT_USER_STORAGE_LIMIT
        self.used_storage = int(
            self.redis_client.get(f"{KEY_USER_USED_STORAGE}_{self.user_id}")
        )

    def check_user_exist(self):
        if not self.redis_client.exists(
            f"{KEY_USER_USED_STORAGE}_{self.user_id}"
        ):  # 如果用户不存在存储空间
            return False
        return True

    def init_user_storage(self, total_storage):
        self.total_storage = DEFAULT_USER_STORAGE_LIMIT

        # 从s3读取已占用空间
        s3 = S3Client()
        self.used_storage = s3.get_objs_total_size(get_user_path(self.user_id))
        self.redis_client.set(
            f"{KEY_USER_USED_STORAGE}_{self.user_id}", self.used_storage
        )

    def get_useage(self):
        if not self.check_user_exist():
            return None
        self.used_storage = int(
            self.redis_client.get(f"{KEY_USER_USED_STORAGE}_{self.user_id}")
        )
        return self.total_storage, self.used_storage

    def get_unused_storage(self):
        return self.total_storage - self.used_storage

    def check_remaining_space(self, file_size: int):
        if self.total_storage - self.used_storage < file_size:
            return False
        return True

    def increase_used_storage(self, file_size: int):
        self.used_storage += file_size
        self.redis_client.set(
            f"{KEY_USER_USED_STORAGE}_{self.user_id}", self.used_storage
        )

    def decrease_used_storage(self, file_size: int):
        self.used_storage -= file_size
        self.redis_client.set(
            f"{KEY_USER_USED_STORAGE}_{self.user_id}", self.used_storage
        )

    def delete_user_storage(self):
        self.redis_client.delete(f"{KEY_USER_USED_STORAGE}_{self.user_id}")
