from elasticsearch import Elasticsearch
from typing import List, Dict, Any

from app.client.es_data import EsClientSearchResult
from app.config import es_config
from app.logger import logger


class ESClient:
    def __init__(self):
        self.client = Elasticsearch(
            es_config["host"],
            request_timeout=60,
            retry_on_timeout=True,
            max_retries=3,
            verify_certs=False,
            ssl_show_warn=False,
            basic_auth=(es_config["username"], es_config["password"]),
        )

    def test_connection(self):
        """测试ES连接"""
        try:
            info = self.client.info()
            logger.info(f"Successfully connected to ES cluster: {info}")
            return True
        except Exception as e:
            logger.error(f"ES connection test failed: {str(e)}")
            logger.error(f"Error type: {type(e)}")
            return False

    def create_index(self, index_name: str, mapping: Dict):
        """创建索引"""
        try:
            response = self.client.indices.create(index=index_name, body=mapping)
            if response.get("acknowledged"):
                logger.info(f"成功创建索引 {index_name}")
                return True
            else:
                logger.error(f"创建索引 {index_name} 失败: {response}")
                return False
        except Exception as e:
            logger.error(f"创建索引时发生错误: {str(e)}")
            raise

    def delete_index(self, index_name: str):
        """删除索引"""
        try:
            response = self.client.indices.delete(index=index_name)
            if response.get("acknowledged"):
                logger.info(f"成功删除索引 {index_name}")
                return True
            else:
                logger.error(f"删除索引 {index_name} 失败: {response}")
                return False
        except Exception as e:
            logger.error(f"删除索引时发生错误: {str(e)}")
            raise

    def add_documents(self, index_name: str, documents: List[Dict[str, Any]]):
        """批量添加文档"""
        operations = []
        for doc in documents:
            operations.append({"index": {"_index": index_name}})
            operations.append(doc)
        try:
            response = self.client.bulk(operations=operations)
            if response.get("errors"):
                logger.error("批量操作包含错误:")
                for item in response["items"]:
                    if item["index"].get("error"):
                        logger.error(f"文档错误: {item['index']['error']}")
            else:
                logger.info(f"成功添加文档到索引 {index_name}")
            return response
        except Exception as e:
            logger.error(f"添加文档时发生错误: {str(e)}")
            raise

    def delete_documents(self, index_name: str, query: Dict):
        """根据查询条件删除文档"""
        try:
            response = self.client.delete_by_query(index=index_name, body=query)
            if response.get("deleted"):
                logger.info(
                    f"成功从索引 {index_name} 删除 {response['deleted']} 个文档"
                )
                return True
            else:
                logger.error(f"删除文档失败: {response}")
                return False
        except Exception as e:
            logger.error(f"删除文档时发生错误: {str(e)}")
            raise

    def search(self, index_name: str, body: Dict):
        """执行搜索查询"""
        try:
            results = self.client.search(index=index_name, body=body)
            if "hits" not in results:
                logger.error(f"搜索结果格式错误: {results}")
                return []
            return results["hits"]["hits"]
        except Exception as e:
            logger.error(f"执行搜索查询时发生错误: {str(e)}")
            raise

    def search_v2(self, index_name: str, body: Dict) -> EsClientSearchResult:
        """执行搜索查询，返回额外信息"""
        try:
            results = self.client.search(index=index_name, body=body)
            if "hits" not in results:
                raise ValueError(f"搜索结果格式错误: {results}")
            return EsClientSearchResult(
                hits=results["hits"]["hits"], total=results["hits"]["total"]
            )
        except Exception as e:
            logger.error(f"执行搜索查询时发生错误: {str(e)}")
            raise

    def index_exists(self, index_name: str) -> bool:
        """检查索引是否存在"""
        try:
            return self.client.indices.exists(index=index_name)
        except Exception as e:
            logger.error(f"检查索引存在时发生错误: {str(e)}")
            raise

    def close(self):
        """关闭ES客户端连接"""
        self.client.close()

    def update_index_settings(self, index_name: str, settings: Dict):
        """更新索引设置"""
        try:
            self.client.indices.put_settings(index=index_name, body={"index": settings})
            logger.info(f"成功更新索引 {index_name} 的设置")
        except Exception as e:
            logger.error(f"更新索引设置时发生错误: {str(e)}")
            raise

    def update_documents(self, index_name: str, updates: List[Dict[str, Any]]):
        """批量更新文档"""
        operations = []
        for update in updates:
            operations.append({"update": {"_index": index_name, "_id": update["_id"]}})
            operations.append({"doc": update["doc"]})
        try:
            self.client.bulk(operations=operations)
            logger.info(f"成功批量更新索引 {index_name} 中的文档")
        except Exception as e:
            logger.error(f"批量更新文档时发生错误: {str(e)}")
            raise


if __name__ == "__main__":

    es_client = ESClient()

    # 首先测试连接
    if not es_client.test_connection():
        logger.error("无法连接到ES集群，请检查配置")

    try:
        # 测试索引名称
        test_index = "test_index3"

        # 定义映射
        mapping = {
            "mappings": {
                "properties": {
                    "title": {"type": "text"},
                    "content": {"type": "text"},
                    "timestamp": {"type": "date"},
                }
            }
        }

        # 检查并创建索引
        if not es_client.index_exists(test_index):
            es_client.create_index(test_index, mapping)
            logger.info(f"索引 {test_index} 创建成功")

        # 添加测试文档
        test_docs = [
            {
                "title": "测试文档1",
                "content": "这是第一个测试文档的内容",
                "timestamp": "2024-03-20",
            },
            {
                "title": "测试文档2",
                "content": "这是第二个测试文档的内容",
                "timestamp": "2024-03-21",
            },
        ]

        es_client.add_documents(test_index, test_docs)
        logger.info("文档添加成功")

        # 执行搜索查询
        search_query = {"query": {"match": {"content": "测试文档"}}}

        results = es_client.search(test_index, search_query)
        logger.info("搜索结果: %s", results)

    finally:
        # 关闭客户端连接
        es_client.close()
