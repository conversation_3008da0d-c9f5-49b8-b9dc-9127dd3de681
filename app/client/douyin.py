import base64
import requests

from app.config import (
    DOUYIN_CLIENT_KEY,
    DOUYIN_REDIRECT_URI,
    DOUYIN_ACCESS_TOKEN_URL,
    DOUYIN_CLIENT_SECRET,
    DOUYIN_REFERSH_ACCESS_TOKEN_URL,
    DOUYIN_CLIENT_TOKEN_URL,
    DOUYIN_USER_INFO_URL,
)
from app.logger import logger
from app.db.common_type import BillboardKey
from app.db.vo import BillboardData, BillboardDataItem

AUTH_URL = "https://open.douyin.com/platform/oauth/connect?client_key={}&response_type=code&scope={}&redirect_uri={}&state={}"

# 热榜和挑战榜API
BILLBOARD_URL = {
    "hot": "https://www.douyin.com/aweme/v1/web/hot/search/list/?aid=6383&board_type=0&board_sub_type=&version_code=170400",
    "challenge": "https://www.douyin.com/aweme/v1/web/hot/search/list/?aid=6383&board_type=2&board_sub_type=hotspot_challenge&version_code=170400",
}

BILLBOARD_LABEL_MAP = {
    1: "新",
    3: "热",
    16: "辟谣",
}

TIMEOUT = 6


class DouYinClient:
    def __init__(self):
        self.headers = {"content-type": "application/json"}

    def getRequestCodeUrl(self, user_id: int):
        user_id_str = str(user_id)
        user_id_bytes = user_id_str.encode("utf-8")
        state = base64.b64encode(user_id_bytes).decode("utf-8")
        return AUTH_URL.format(
            DOUYIN_CLIENT_KEY,
            "user_info,data.external.item,data.external.user,data.external.fans_source,fans.data.bind",
            # trial.whitelist,video.list.bind
            DOUYIN_REDIRECT_URI,
            state,
        )

    def requestAccessToken(self, code: str):
        data = '{"grant_type":"authorization_code","client_key":"%s","client_secret":"%s","code":"%s"}'
        data = data % (DOUYIN_CLIENT_KEY, DOUYIN_CLIENT_SECRET, code)
        logger.info("requestAccessToken post data: %s" % data)
        response = requests.post(
            DOUYIN_ACCESS_TOKEN_URL,
            data=data,
            headers=self.headers,
            timeout=TIMEOUT,
        )
        return response.json()

    def refershAccessToken(self, refresh_token: str):
        data = '{"grant_type":"refresh_token","client_key":"%s","refresh_token":"%s"}'
        data = data % (DOUYIN_CLIENT_KEY, refresh_token)
        logger.info("refershAccessToken post data: %s" % data)
        response = requests.post(
            DOUYIN_REFERSH_ACCESS_TOKEN_URL, data=data, timeout=TIMEOUT
        )
        return response.json()

    def requestClientToken(self):
        data = (
            '{"grant_type":"client_credential","client_key":"%s","client_secret":"%s"}'
        )
        data = data % (DOUYIN_CLIENT_KEY, DOUYIN_CLIENT_SECRET)
        response = requests.post(
            DOUYIN_CLIENT_TOKEN_URL, data=data, headers=self.headers, timeout=TIMEOUT
        )
        return response.json()

    def requestAccountInfo(self, access_token: str, open_id: str):
        data = '{"open_id":"%s","access_token":"%s"}' % (open_id, access_token)
        response = requests.post(
            DOUYIN_USER_INFO_URL, data=data, headers=self.headers, timeout=TIMEOUT
        )
        return response.json()

    def requestCreatorUserInfo(self, sid_tt: str):
        try:
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Cookie": "sid_tt=" + sid_tt + ";",
                "user-agent": "Mozilla/5.0",
            }
            response = requests.get(
                "https://creator.douyin.com/aweme/v1/creator/user/info/",
                headers=headers,
                timeout=TIMEOUT,
            )
            response_json = response.json()
            if response.status_code != 200:
                logger.error(
                    f"get user info failed, sid_tt: {sid_tt}, response: {response.status_code} {response.reason}"
                )
                return {}
            elif response_json.get("status_code", 0) != 0:
                logger.error(
                    f'get user info failed, sid_tt: {sid_tt}, response: {response_json.get("status_code", 0)} {response_json.get("status_msg", "")}'
                )
                return {}
            else:
                logger.debug(
                    f"get user info success, sid_tt: {sid_tt}, response: {response_json}"
                )
            return response_json.get("user_profile", {})
        except Exception as e:
            logger.error(f"get user info failed: {e}")
            return {}

    def requestBillboardData(self, key: BillboardKey) -> BillboardData:
        billboard_data = BillboardData()

        url = BILLBOARD_URL[key.value]
        response = requests.get(
            url,
            headers={
                "Cookie": "passport_csrf_token=471c8b2f4e021628d19761e86dea8f14",
                "User-Agent": "Mozilla/5.0",
                "Connection": "keep-alive",
            },
            timeout=TIMEOUT,
        )
        if response.status_code != 200:
            logger.error(
                f"get billboard data failed, key: {key}, response: {response.status_code} {response.reason}"
            )
            return billboard_data
        data = response.json()["data"]
        if "active_time" in data:
            billboard_data.active_time = data["active_time"]
        if "word_list" in data:
            for data in data["word_list"]:
                # 跳过置顶条目，目前不需要展示
                if "position" not in data:
                    continue
                item = BillboardDataItem()
                item.word = data["word"] if "word" in data else ""
                item.position = data["position"]
                item.label = (
                    BILLBOARD_LABEL_MAP.get(data["label"], "")
                    if "label" in data
                    else ""
                )
                item.hot_value = data["hot_value"] if "hot_value" in data else 0
                item.view_count = data["view_count"] if "view_count" in data else 0

                billboard_data.items.append(item)

        return billboard_data


# Dependency
def get_client():
    client = DouYinClient()
    return client


if __name__ == "__main__":
    client = DouYinClient()
    data = client.requestClientToken()
    print(data)
