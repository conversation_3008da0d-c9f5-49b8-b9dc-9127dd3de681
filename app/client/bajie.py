import json
from enum import Enum
from typing import List, Dict, Any, Optional
import threading
import requests
from pydantic import BaseModel

from app.logger import logger
from app.env import SIHEGPT_STAGE, StageEnv


class FileSetStatus(str, Enum):
    FILE_UPLOADING = "file-uploading"
    FILE_UPLOADED = "file-uploaded"
    LABELLING = "labelling"
    LABELLED = "labelled"
    REVIEWING = "reviewing"
    REVIEWED = "reviewed"
    REJECTED = "rejected"


class LabelData(BaseModel):
    """标注操作的边界框配置"""

    # 根据具体业务需求定义，这里提供一个基本结构
    # 可以根据实际需要添加更多字段
    x: Optional[float] = None
    y: Optional[float] = None
    width: Optional[float] = None
    height: Optional[float] = None
    # 可以添加更多标注相关的字段


class ClusterInfo(BaseModel):
    """聚类信息配置"""

    initial_n_clusters: int  # 初始使用 k-means 聚类时定义的类数
    label_data: Optional[Dict[str, Any]] = None  # 配置独立成组等标注操作的 bounding box


class ClusteringMetadata(BaseModel):
    """聚类标注元数据"""

    description: str  # 数据集描述（也是提示词）
    cluster_info: ClusterInfo


class CustomLabels(BaseModel):
    """表格化标注的自定义标签配置"""

    apply_label_num: int  # 申请标注数量
    labeler_max_pending_num: int  # 标注员最大待处理数量
    task_description: str  # 任务描述
    clustering_metadata: Optional[ClusteringMetadata] = None  # 聚类标注相关配置，可选


class FilesetMetadata(BaseModel):
    """文件集元数据"""

    MachineId: str
    MaterialType: str
    MaterialModel: str
    Description: str


class CreateFilesetRequest(BaseModel):
    """创建文件集请求"""

    Metadata: FilesetMetadata
    CustomLabels: CustomLabels


class LabelFileInfo(BaseModel):
    """标注文件信息"""

    Id: str
    FileType: str
    CustomLabels: Dict[str, Any]


class TableFileSet(BaseModel):
    """表格化标注文件集信息"""

    Id: str
    LabelFiles: List[LabelFileInfo]
    Metadata: Dict[str, Any]

    def get_name(self) -> str:
        """获取文件集名称"""
        return self.Metadata.get("MaterialModel", "<未知文件集名称>")

    def get_datasheet_id(self) -> Optional[str]:
        """获取数据表ID"""
        if self.LabelFiles:
            return self.LabelFiles[0].CustomLabels.get("datasheet_id")
        return None

    def get_space_id(self) -> Optional[str]:
        """获取空间ID"""
        if self.LabelFiles:
            return self.LabelFiles[0].CustomLabels.get("space_id")
        return None


class BajieClient:
    """这是标注平台的客户端，可以列出标注任务、物料、读写某一个物料的信息"""

    def __init__(self, stage_env: Optional[StageEnv] = None):
        """
        初始化 BajieClient

        Args:
            stage_env (Optional[StageEnv], optional): 环境配置. Defaults to None，则根据SIHEGPT_STAGE确定环境配置
        """
        # 优先判断stage_env，如果为空，则根据SIHEGPT_STAGE确定环境配置
        if stage_env is None:
            stage_env = SIHEGPT_STAGE

        # 添加环境配置日志
        print(f"🔧 BajieClient 环境配置:")
        print(f"  - SIHEGPT_STAGE: {SIHEGPT_STAGE}")
        print(f"  - SIHEGPT_STAGE.is_prod_env(): {SIHEGPT_STAGE.is_prod_env()}")
        print(f"  - stage_env 参数: {stage_env}")
        print(f"  - 最终环境: {stage_env.name}")

        # 保存环境配置
        self.stage_env = stage_env

        print(f"  - bajie_address: {self.address}")
        print(f"  - bajie_username: {self.username}")
        print(f"  - company_id: {self.company_id}")

        _token = self._login(self.username, self.password)
        self.token = f"Bearer {_token}"

    @property
    def address(self) -> str:
        """获取 Bajie 服务地址"""
        if self.stage_env.is_prod_env():
            return "https://cybercore-backend.sihe6.com"
        else:
            return "https://cybercore-backend.staging.sihe6.com"

    @property
    def username(self) -> str:
        """获取 Bajie 用户名"""
        if self.stage_env.is_prod_env():
            return "robot-sihe"
        else:
            return "<EMAIL>"

    @property
    def password(self) -> str:
        """获取 Bajie 密码"""
        if self.stage_env.is_prod_env():
            return "2dcf926f10c19842c27392b49ca98b"
        else:
            return "Bijo2;GocrTevr"

    @property
    def company_id(self) -> int:
        """获取公司ID"""
        if self.stage_env.is_prod_env():
            return 315
        else:
            return 7209

    @property
    def affine_user_id(self) -> str:
        """获取 Affine 用户ID"""
        if self.stage_env.is_prod_env():
            return "d8cf824c-dee8-4582-898c-bbaad038dccb"
        else:
            return "06d7ef3a-321d-4c57-825a-029a20a1677a"

    @property
    def affine_workspace_id(self) -> str:
        """获取 Affine 工作空间ID"""
        if self.stage_env.is_prod_env():
            return "b7a2a57c-13b1-4623-b24b-c3cf901231be"
        else:
            return "4c19a83d-e8a0-4359-b2aa-e5da8c815323"

    def _login(self, username: str, password: str) -> str:
        res = requests.post(
            self.address + "/auth/login",
            json={
                "Username": username,
                "Password": password,
            },
        )
        return res.json().get("token", "")

    def create_table_fileset(
        self,
        company_id: int,
        machine_id: str,
        material_model: str,
        description: str,
        apply_label_num: int,
        labeler_max_pending_num: int,
        task_description: str,
        clustering_metadata: Optional[ClusteringMetadata] = None,
    ) -> Optional[TableFileSet]:
        """
        创建表格化标注类型的文件集

        Args:
            company_id (int): 公司ID
            machine_id (str): 机器ID
            material_model (str): 物料模型名称
            description (str): 描述
            apply_label_num (int): 申请标注数量
            labeler_max_pending_num (int): 标注员最大待处理数量
            task_description (str): 任务描述
            clustering_metadata (Optional[ClusteringMetadata]): 聚类标注元数据，可选

        Returns:
            Optional[TableFileSet]: 创建成功时返回文件集信息，失败时返回None
        """
        try:
            # 构建请求数据
            request_data = CreateFilesetRequest(
                Metadata=FilesetMetadata(
                    MachineId=machine_id,
                    MaterialType="表格化标注",
                    MaterialModel=material_model,
                    Description=description,
                ),
                CustomLabels=CustomLabels(
                    apply_label_num=apply_label_num,
                    labeler_max_pending_num=labeler_max_pending_num,
                    task_description=task_description,
                    clustering_metadata=clustering_metadata,
                ),
            )

            # 发送创建请求
            res = requests.post(
                f"{self.address}/companies/{company_id}/filesets",
                headers={"Authorization": self.token},
                json=request_data.dict(exclude_none=True),  # 排除None值
            )

            if res.status_code != 200:
                logger.error(
                    f"创建表格化标注文件集失败: company_id={company_id}, "
                    f"status_code={res.status_code}, response={res.text}"
                )
                return None

            # 解析响应
            response_data = res.json()
            return TableFileSet(**response_data)

        except Exception as e:
            logger.error(f"创建表格化标注文件集异常: {e}")
            return None

    def update_label_file(
        self, company_id: int, fileset_id: str, label_file_id: str, label_file: dict
    ) -> bool:
        res = requests.patch(
            f"{self.address}/companies/{company_id}/filesets/{fileset_id}/labelFiles/{label_file_id}",
            headers={"Authorization": self.token},
            json=label_file,
        )
        if res.status_code != 200:
            logger.error(
                f"call bajie update label file api failed, company_id={company_id}, fileset_id={fileset_id}, label_file_id={label_file_id}, status_code={res.status_code}, response={res.text}"
            )
        return res.status_code == 200

    def finish_uploading_label_file(self, company_id: int, fileset_id: str) -> bool:
        res = requests.post(
            f"{self.address}/companies/{company_id}/filesets/{fileset_id}/finishUploadingLabelFile",
            headers={"Authorization": self.token},
            json={},
        )
        return res.status_code == 200

    def query_fileset(
        self, company_id: int, aweme_id: str, material_type: str = "抖音视频拆解v2"
    ) -> str:
        res = requests.post(
            f"{self.address}/companies/{company_id}/filesets-query",
            headers={"Authorization": self.token},
            json={
                "MongodbFilter": {
                    "DeletedAt": None,
                    "Metadata.MaterialType": material_type,
                    "Metadata.MaterialModel": f"{aweme_id}",
                },
                "Sort": {"FinishUploadInfo.TimeInt64": -1},
                "Limit": 10,
                "Skip": 0,
            },
        )
        res.raise_for_status()
        response: dict = res.json()
        count = response.get("TotalCount", 0)
        if count == 0:
            return ""
        return response["Filesets"][0].get("Id")

    def query_table_fileset(
        self, company_id: int, material_type: str = "表格化标注"
    ) -> List[TableFileSet]:
        """
        查询表格化标注的文件集

        约定：
        1. 聚类标注对应的物料类型为 表格化标注
        2. 物料名称的格式为 "聚类标注|$category-$dimension_name"

        Args:
            company_id (int): 公司ID
            material_type (str): 物料类型，默认为"表格化标注"

        Returns:
            List[TableFileSet]: 表格化标注文件集列表
        """
        res = requests.post(
            f"{self.address}/companies/{company_id}/filesets-query",
            headers={"Authorization": self.token},
            json={
                "MongodbFilter": {
                    "DeletedAt": None,
                    "Metadata.MaterialType": material_type,
                    "Status": {"$nin": ["file-uploading"]},
                },
                "Sort": {"FinishUploadInfo.TimeInt64": -1},
                "Limit": 1000,
                "Skip": 0,
            },
        )
        res.raise_for_status()
        response: dict = res.json()

        filesets = []
        for fileset_data in response.get("Filesets", []):
            try:
                fileset = TableFileSet(**fileset_data)
                filesets.append(fileset)
            except Exception as e:
                logger.warning(f"解析文件集数据失败: {e}, data: {fileset_data}")
                continue

        return filesets

    @staticmethod
    def extract_v1_field(
        label_file: Dict[str, Any], name: str, default: Any = None
    ) -> Any:
        # 创建标注任务时传的一些信息
        custom_labels = label_file.get("CustomLabels") or {}
        origin_v1_fields = custom_labels.get("v1Fields") or []

        # 标注员改过的一些信息
        label_info_custom_labels = (label_file.get("LabelInfo") or {}).get(
            "CustomLabels"
        ) or {}
        v1_fields = label_info_custom_labels.get("v1FieldsUserOverride") or []

        # v1_fields -> origin_v1_fields -> default
        for field in v1_fields:
            if field["key"] == name:
                return field["valueUserOverride"]
        for field in origin_v1_fields:
            if field["key"] == name:
                return field["value"]
        return default

    @staticmethod
    def set_v1_field(
        v1_fields: List[Dict[str, Any]],
        name: str,
        value: Any,
        type: str = "text",
        readonly: bool = True,
        extra: Optional[Dict[str, Any]] = None,
    ):
        for i, v in enumerate(v1_fields):
            if v["key"] == name:
                v["value"] = value
                return

        item = {
            "key": name,
            "value": value,
            "type": type,
            "readOnly": readonly,
        }
        if extra:
            item.update(extra)
        v1_fields.append(item)

    @staticmethod
    def remove_v1_field(
        v1_fields: List[Dict[str, Any]],
        name: str,
    ):
        for i, v in enumerate(v1_fields):
            if v["key"] == name:
                del v1_fields[i]
                break

    def delete_fileset(self, company_id: int, fileset_id: str):
        """
        删除文件集

        Args:
            company_id (int): 公司ID
            fileset_id (str): 文件集ID

        Returns:
            bool: 删除是否成功
        """
        res = requests.delete(
            f"{self.address}/companies/{company_id}/filesets/{fileset_id}",
            headers={"Authorization": self.token},
            json={},
        )

        if res.status_code != 200:
            logger.error(
                f"删除文件集失败: company_id={company_id}, fileset_id={fileset_id}, "
                f"status_code={res.status_code}, response={res.text}"
            )
            raise ValueError(
                f"删除文件集失败: company_id={company_id}, fileset_id={fileset_id}, status_code={res.status_code}, response={res.text}"
            )

        logger.info(f"成功删除文件集: company_id={company_id}, fileset_id={fileset_id}")

    def join_table_fileset_vika_space(self, space_id: str) -> bool:
        """
        邀请用户进入 APITable space

        Args:
            space_id (str): APITable space ID

        Returns:
            bool: 邀请是否成功
        """
        user_to_join = self.username
        try:
            res = requests.post(
                f"{self.address}/apitable/spaces/invite",
                headers={"Authorization": self.token},
                json={
                    "SpaceId": space_id,
                    "UserToJoin": user_to_join,
                },
            )

            if res.status_code != 200:
                logger.error(
                    f"邀请用户进入 APITable space 失败: space_id={space_id}, "
                    f"user_to_join={user_to_join}, status_code={res.status_code}, response={res.text}"
                )
                return False

            logger.info(
                f"成功邀请用户进入 APITable space: space_id={space_id}, user_to_join={user_to_join}"
            )
            return True

        except Exception as e:
            logger.error(f"邀请用户进入 APITable space 异常: {e}")
            return False


_bajie_client = None
_bajie_client_lock = threading.Lock()  # 用于保护_bajie_client的初始化


def get_bajie_client(stage_env: Optional[StageEnv] = None):
    """
    获取 BajieClient 实例，使用延迟初始化，并加锁避免多线程竞争

    Args:
        stage_env (Optional[StageEnv], optional): 环境配置. Defaults to None，则使用默认环境的单例实例

    Returns:
        BajieClient: BajieClient 实例

    Note:
        如果指定了 stage_env 参数，则不使用单例模式，直接创建新实例。
        只有使用默认环境时才使用单例模式。
    """
    # 如果指定了环境参数，则不使用单例模式，直接创建新实例
    if stage_env is not None:
        return BajieClient(stage_env=stage_env)

    # 使用单例模式，只对默认环境有效
    global _bajie_client
    if _bajie_client is None:
        with _bajie_client_lock:
            if _bajie_client is None:  # 双重检查，确保线程安全
                _bajie_client = BajieClient()
    return _bajie_client
