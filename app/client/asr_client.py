import json
import os
import time
import uuid

import httpx
import opencc
import requests
from moviepy.editor import Video<PERSON><PERSON><PERSON><PERSON>

from app.client.openai_client import OpenA<PERSON>lient
from app.config import GLOBAL_CONF
from app.logger import logger
from app.utils import langfuse_utils

host = GLOBAL_CONF.WHISPER_HOST
FASTER_WHISPER_ADDRESS = host

TIMEOUT = 1500

openai_client = OpenAIClient()


def traditional_to_simplified(text):
    # 创建一个 OpenCC 转换器，选择繁体到简体的转换
    converter = opencc.OpenCC("t2s")
    return converter.convert(text)


ASR_HEADERS = {"accept": "application/json", "expect": "100-continue"}


def extract_video_text(file_path, faster_whisper_address="", language=""):
    extract_url = (
        FASTER_WHISPER_ADDRESS
        + "/asr?encode=true&task=transcribe&word_timestamps=false&output=txt"
    )
    if faster_whisper_address != "":
        extract_url = (
            faster_whisper_address
            + "/asr?encode=true&task=transcribe&word_timestamps=false&output=txt"
        )
    if language != "":
        extract_url = extract_url + "&language=" + language

    audio_file_path = _mp4_to_mp3(file_path)
    if not audio_file_path:
        logger.warning(f"file {file_path} has no audio, skip extract video text")
        return ""

    try:
        with open(audio_file_path, "rb") as audio_file:
            files = {
                "audio_file": (
                    os.path.basename(audio_file_path),
                    audio_file,
                    _filename_to_mimetype(audio_file_path),
                ),
            }
            extract_url = extract_url + "&uuid=" + str(uuid.uuid4())
            logger.info(f"Uploading {audio_file_path} to {extract_url}")
            start_time = time.time()
            response = requests.post(
                extract_url, headers=ASR_HEADERS, files=files, timeout=TIMEOUT
            )
            if response.status_code != 200:
                logger.error(f"extract video text error: {response}")
                return ""
            logger.info(
                f"extract video {file_path} text success, cost time: {time.time() - start_time}"
            )
            return traditional_to_simplified(response.text)
    finally:
        # 如果生成了新的mp3文件，则删除它
        if audio_file_path != file_path and os.path.exists(audio_file_path):
            os.remove(audio_file_path)
            logger.info(f"已删除临时音频文件: {audio_file_path}")


async def extract_video_json(file_path, faster_whisper_address="", language=""):
    logger.info(f"extract_video_json 开始执行，准备提取视频文本。文件路径: {file_path}")
    extract_url = (
        FASTER_WHISPER_ADDRESS
        + "/asr?encode=true&task=transcribe&word_timestamps=false&output=json"
    )
    if faster_whisper_address != "":
        extract_url = (
            faster_whisper_address
            + "/asr?encode=true&task=transcribe&word_timestamps=false&output=json"
        )
    if language != "":
        extract_url = extract_url + "&language=" + language

    audio_file_path = _mp4_to_mp3(file_path)
    if not audio_file_path:
        logger.warning(f"file {file_path} has no audio, skip extract video json")
        return {}

    try:
        with open(audio_file_path, "rb") as audio_file:
            files = {
                "audio_file": (
                    os.path.basename(audio_file_path),
                    audio_file,
                    _filename_to_mimetype(audio_file_path),
                ),
            }
            extract_url = extract_url + "&uuid=" + str(uuid.uuid4())
            logger.info(f"Uploading {audio_file_path} to {extract_url}")
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    extract_url, headers=ASR_HEADERS, files=files, timeout=TIMEOUT
                )
                if response.status_code != 200:
                    logger.error(f"extract video json error: {response}")
                    return ""
                logger.info(
                    f"extract video {file_path} json success, cost time: {time.time() - start_time}"
                )
                return json.loads(traditional_to_simplified(response.text))
    finally:
        # 如果生成了新的mp3文件，则删除它
        if audio_file_path != file_path and os.path.exists(audio_file_path):
            os.remove(audio_file_path)
            logger.info(f"已删除临时音频文件: {audio_file_path}")


def _mp4_to_mp3(file_path) -> str:
    """
    将mp4文件转换为mp3文件，并返回mp3文件路径。因为 whisper 服务对某些编码并不支持，所以在这里先转好了，比较安全。比如奥胖.mp4
    """
    if file_path.endswith(".mp3"):
        return file_path
    elif not file_path.endswith(".mp4"):
        logger.warning(f"file {file_path} is not a mp4 file, skip convert")
        return file_path
    video = VideoFileClip(file_path)
    if video.audio is None:
        logger.warning(f"file {file_path} has no audio, skip convert")
        return ""
    audio = video.audio
    audio_path = file_path.replace(".mp4", ".mp3")
    audio.write_audiofile(audio_path)
    return audio_path


def _filename_to_mimetype(file_path):
    if file_path.endswith(".mp3"):
        return "audio/mpeg"
    elif file_path.endswith(".mp4"):
        return "video/mp4"
    return "application/octet-stream"


async def extract_video_text_formated(video_file, language="", llm_fix=False):
    video_content = await extract_video_json(video_file, language=language)
    video_content_json = []
    if video_content:
        for item in video_content["segments"]:
            video_content_json.append(
                {
                    "start_time": int(item[2] * 100) / 100,
                    "end_time": int(item[3] * 100) / 100,
                    "content": traditional_to_simplified(item[4]),
                }
            )
    # 在这个位置fix，可以减少token量，extract_video_json的返回里面有很多其他内容
    if llm_fix:
        prompt = langfuse_utils.get_prompt(
            "asr_fix",
            "",
            video_content_json=json.dumps(video_content_json, ensure_ascii=False),
        )
        video_content_json_fix = await openai_client.request_claude(
            prompt=prompt,
        )
        if video_content_json_fix:
            video_content_json = video_content_json_fix
    return video_content_json


if __name__ == "__main__":
    print("whisper address:", FASTER_WHISPER_ADDRESS)
    # print(extract_video_text("/Users/<USER>/Downloads/三个视频拆解的中间结果，供人工使用提示工程调试GPT/audio/奥胖.mp3"))
    print(
        extract_video_text(
            "/Users/<USER>/Downloads/三个视频拆解的中间结果，供人工使用提示工程调试GPT/videos/奥胖.mp4"
        )
    )
