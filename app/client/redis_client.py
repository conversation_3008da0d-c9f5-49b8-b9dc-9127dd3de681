import redis
import json
import time
import os
from contextlib import contextmanager, asynccontextmanager
from app.db.common_type import BillboardKey
from app.db.vo import BillboardData, BillboardDataItem
from app.utils.async_utils import to_async
from app.logger import logger
from app.config import GL<PERSON><PERSON>L_CONF  # use centralized config

KEY_CHAT_ANSWER_MAP = "answer_map"
KEY_CHAT_DOCS_MAP = "docs_map"
KEY_CHAT_OPTION_MAP = "option_map"
KEY_CHAT_STATE_MAP = "state_map"
KEY_CHAT_VIDEO_MAP = "video_map"

KEY_CHAT_VIDEO_SET = "cached_video_set"
KEY_CHAT_VIDEO_AUTHOR_SET = "cached_author_set"

KEY_RECOMMEND_VIDEOS = "user_recommend_videos"
KEY_RECOMMEND_PULL_TIME = "user_recommend_pull_time"

KEY_VIDEO_MARK_LOCK = "video_mark_lock"

KEY_SOCIAL_RECOMMEND_VIDEOS = "social_user_recommend_videos"
KEY_SOCIAL_READ_VIDEOS = "social_user_read_videos"
KEY_SOCIAL_USER_LOCK = "social_user_lock"

KEY_VIDEO_DISASSEMBLE_MAP = "video_disassemble_map"

KEY_NOTIFICATION = "notification_user"


billboard_keymap = {"hot": "hot_billboard", "challenge": "challenge_billboard"}

host = GLOBAL_CONF.REDIS_HOST
port = GLOBAL_CONF.REDIS_PORT
password = GLOBAL_CONF.REDIS_PASSWORD
db_index = GLOBAL_CONF.REDIS_DB_INDEX
connection_pool = redis.ConnectionPool(
    host=host, port=port, db=db_index, password=password, decode_responses=True
)


def redis_url():
    return f"redis://:{password}@{host}:{port}/{db_index}"


class RedisClient:

    redis_client = None

    def __init__(self):
        self.redis_client = redis.Redis(connection_pool=connection_pool)

    def get_client(self):
        if self.redis_client is None:
            self.redis_client = redis.Redis(connection_pool=connection_pool)
        return self.redis_client

    def get_and_clean_json_map(self, key: str, field: int):
        if self.get_client().hexists(key, str(field)):
            data = self.get_client().hget(key, str(field))
            result = json.loads(data) if data else []  # type: ignore
            self.get_client().hdel(key, str(field))
            return result
        return []

    def get_and_clean_chat_state_map(
        self, conversation_id: int, default_value: str = "default"
    ):
        if self.get_client().hexists(KEY_CHAT_STATE_MAP, str(conversation_id)):
            data = self.get_client().hget(KEY_CHAT_STATE_MAP, str(conversation_id))
            res = data if data else default_value
            self.get_client().hdel(KEY_CHAT_STATE_MAP, str(conversation_id))
            return res
        return default_value

    def get_and_clean_chat_answer_map(self, conversation_id: int):
        if self.get_client().hexists(KEY_CHAT_ANSWER_MAP, str(conversation_id)):
            data = self.get_client().hget(KEY_CHAT_ANSWER_MAP, str(conversation_id))
            self.get_client().hdel(KEY_CHAT_ANSWER_MAP, str(conversation_id))
            return data if data else ""
        return ""

    def set_json_value(self, key: str, value, timeout: int = 5) -> bool:
        data = json.dumps(value)
        return self.get_client().setex(key, timeout, data)  # type: ignore

    def get_json_value(self, key: str):
        value = self.get_client().get(key)
        assert (
            type(value) is str or value is None
        ), f"Invalid value type for key {key}: {type(value)}"
        if not value:
            return value
        return json.loads(value)

    def delete(self, key: str):
        self.get_client().delete(key)

    def lock(self, lock_key, lock_timeout, blocking=False):
        return RedisLock(self.get_client(), lock_key, lock_timeout, blocking=blocking)

    def try_lock_social_user(self, user_id: int):
        key = f"{KEY_SOCIAL_USER_LOCK}_{user_id}"
        current_time = int(time.time())

        value = self.get_client().get(key)
        assert (
            type(value) is str or value is None
        ), f"Invalid value type for key {key}: {type(value)}"
        if value is not None:
            lock_time = int(value)
            if lock_time < current_time - 3600:
                if self.get_client().getset(key, current_time) == value:
                    return True
            else:
                return False
        else:
            self.get_client().set(key, current_time)
            return True

    def add_read_videos(self, user_id: int, video_id: str):
        key = f"{KEY_SOCIAL_READ_VIDEOS}_{user_id}"
        # TODO(kevin): data expire
        self.get_client().sadd(key, video_id)

    def check_video_read(self, user_id: int, video_id: str):
        key = f"{KEY_SOCIAL_READ_VIDEOS}_{user_id}"
        return self.get_client().sismember(key, video_id)

    def get_recommend_video_size(self, user_id: int):
        key = f"{KEY_SOCIAL_READ_VIDEOS}_{user_id}"
        return self.get_client().llen(key)

    def push_recommend_video(self, user_id: int, video: str):
        key = f"{KEY_SOCIAL_RECOMMEND_VIDEOS}_{user_id}"
        self.get_client().rpush(key, video)

    def pop_recommend_video(self, user_id: int):
        key = f"{KEY_SOCIAL_RECOMMEND_VIDEOS}_{user_id}"
        return self.get_client().lpop(key)

    def subscribe_to_channel(self, user_id):
        key = f"{KEY_NOTIFICATION}_{user_id}"
        return self.subscribe(key)

    def publish_to_notification_channel(self, user_id: int, msg: str):
        key = f"{KEY_NOTIFICATION}_{user_id}"
        self.publish(key, msg)

    def subscribe(self, channel):
        pubsub = self.get_client().pubsub()
        pubsub.psubscribe(channel)
        # use pubsub then punsubscribe
        return pubsub

    def publish(self, channel, msg):
        self.get_client().publish(channel, msg)


class RedisLock:
    def __init__(self, redis_client, lock_key, lock_timeout=10, blocking=False):
        self.redis_client = redis_client
        self.lock_key = lock_key
        self.lock_timeout = lock_timeout
        self.lock_value = None
        self.blocking = blocking
        self.locked_begin = 0
        self.wait_time = 0

    def acquire_lock(self):
        self.lock_value = str(time.time() + self.lock_timeout)
        if self.redis_client.set(
            self.lock_key, self.lock_value, nx=True, px=self.lock_timeout * 1000
        ):
            return True
        return False

    def release_lock(self):
        release_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        self.redis_client.eval(release_script, 1, self.lock_key, self.lock_value)

    def __enter__(self):
        wait_time = 0.0
        step = 0.25
        while not self.acquire_lock():
            # 自动解锁超时到达后，再等1s
            if not self.blocking or wait_time >= self.lock_timeout + 1:
                blocking = "blocking" if self.blocking else "non-blocking"
                raise RuntimeError(
                    f"Failed to {blocking} acquire lock for {wait_time}s"
                )
            time.sleep(step)
            wait_time += step
        self.locked_begin = time.time()
        self.wait_time = wait_time
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release_lock()
        hold_time = time.time() - self.locked_begin
        if self.wait_time > 1.5 or hold_time > 1.5:
            logger.warning(
                f"redis lock {self.lock_key} released, wait {self.wait_time}s then hold for {hold_time}s",
                exc_info=True,
            )


class Subscriber:
    def __init__(self, sub):
        self.sub = sub

    def wait(self, timeout: float | None = None):
        # 当超时无消息时返回None，否则返回信息如：
        # {'type': 'pmessage', 'pattern': 'sample_*', 'channel': 'sample_abc', 'data': 'hello'}
        # 订阅成功时，会得到：
        # {'type': 'pmessage', 'pattern': None, 'channel': 'sample_abc', 'data': 1}
        res = self.sub.get_message(timeout=timeout)
        if res and res.get("data") == 1:
            # 忽略此消息，重新等待
            return self.wait(timeout)
        return res

    aswait = to_async(wait)


class RedisEvent:
    def __init__(self, event_group: str):
        self.redis_client = RedisClient()
        self.event_group = event_group

    def get_channel(self, first_id, second_id) -> str:
        if not first_id:
            first_id = "*"
        if not second_id:
            second_id = "*"
        return f"{self.event_group}:{first_id}:{second_id}"

    def set(self, first_id, second_id, msg: str = ""):
        channel = self.get_channel(first_id, second_id)
        self.redis_client.publish(channel, msg)

    @contextmanager
    def subscriber(self, first_id, second_id):
        """
        second_id为空时，将订阅first_id的所有事件。
        """
        channel = self.get_channel(first_id, second_id)
        sub = self.redis_client.subscribe(channel)
        yield Subscriber(sub)
        sub.punsubscribe(channel)

    @asynccontextmanager
    async def asubscriber(self, first_id, second_id):
        """
        second_id为空时，将订阅first_id的所有事件。
        """
        channel = self.get_channel(first_id, second_id)
        sub = await to_async(self.redis_client.subscribe)(channel)  # type: ignore
        yield Subscriber(sub)
        await to_async(sub.punsubscribe)(channel)  # type: ignore


redis_client = RedisClient()
