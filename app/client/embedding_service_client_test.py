import pytest
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from app.client.embedding_service_client import embedding_text


@pytest.mark.parametrize("model_name", ["bge", "bge-v1.5", "clip"])
def test_embedding_text_cosine_similarity(model_name):
    """测试不同模型的文本嵌入功能和余弦相似度"""
    # 测试语料
    text_zh = "我是一只狗"
    text_en = "我是一只忠诚的狗"

    # 获取嵌入向量
    embedding_zh = embedding_text(text_zh, model_name)
    embedding_en = embedding_text(text_en, model_name)

    # 验证返回的是列表且包含浮点数
    assert isinstance(embedding_zh, list)
    assert isinstance(embedding_en, list)
    assert all(isinstance(x, (int, float)) for x in embedding_zh)
    assert all(isinstance(x, (int, float)) for x in embedding_en)

    # 计算余弦相似度
    similarity = cosine_similarity(np.array([embedding_zh]), np.array([embedding_en]))[
        0
    ][0]

    # 断言余弦相似度大于0.8
    assert similarity > 0.8, f"{model_name}模型余弦相似度为 {similarity}，应该大于0.8"

    print(f"{model_name}模型 - 余弦相似度: {similarity:.4f}")
