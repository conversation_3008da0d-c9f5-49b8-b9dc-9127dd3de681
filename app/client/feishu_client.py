import json
import os
import time
from dataclasses import dataclass
from typing import List, Any
from datetime import datetime

import requests
from app.logger import logger
from app.config import GLOBAL_CONF


@dataclass
class Sheet:
    """
    飞书电子表格中的 sheet
    """

    sheet_id: str
    start_col: str
    end_col: str
    start_row: int
    end_row: int


@dataclass
class Spreadsheet:
    """
    飞书电子表格
    """

    spreadsheet_token: str
    sheets: List[Sheet]


class FeishuClient:
    def __init__(self):
        self.base_url = "https://open.feishu.cn/open-apis"
        self._auth_token = None
        self._token_time = None
        self._token_expire_minutes = 30  # token 过期时间（参考：https://open.feishu.cn/document/server-docs/authentication-management/access-token/tenant_access_token_internal）

    def _refresh_token(self):
        """获取新的 token 并更新时间戳"""
        self._auth_token = self.get_auth_token()
        self._token_time = datetime.now()

    def _should_refresh_token(self) -> bool:
        """判断是否需要刷新 token"""
        if not self._token_time:
            return True

        time_elapsed = datetime.now() - self._token_time
        return time_elapsed.total_seconds() / 60 >= self._token_expire_minutes

    @property
    def auth_token(self):
        """获取 token，如果过期则自动刷新"""
        if self._should_refresh_token():
            self._refresh_token()
        return self._auth_token

    @staticmethod
    def _retry_request(
        method: str,
        url: str,
        headers: dict,
        payload: dict | str,
        max_retries: int = 5,
        retry_delay: int = 5,
    ):
        last_error = None
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.request(
                    method,
                    url,
                    headers=headers,
                    json=payload,
                )
                response.raise_for_status()
                return response.json()
            except Exception as e:
                last_error = e
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * 2**attempt)
        logger.error(f"请求失败，已达到最大重试次数。response: {response}")
        raise last_error or Exception("请求失败，已达到最大重试次数")

    def get_auth_token(self):
        """自建应用获取 tenant_access_token"""
        api = "/auth/v3/tenant_access_token/internal"
        url = f"{self.base_url}{api}"
        payload = {
            "app_id": GLOBAL_CONF.FEISHU_APP_ID,
            "app_secret": GLOBAL_CONF.FEISHU_APP_SECRET,
        }

        response = self._retry_request("POST", url, headers={}, payload=payload)
        return response["tenant_access_token"]

    def get_spreadsheet_data(self, spreadsheet_token: str, sheet_range: str):
        api = f"/sheets/v2/spreadsheets/{spreadsheet_token}/values/{sheet_range}"
        url = f"{self.base_url}{api}"
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        response = self._retry_request("GET", url, headers, {})
        return response

    def append_spreadsheet_data(
        self, spreadsheet_token: str, sheet_range: str, data: List[Any]
    ):
        # insertDataOption 为 INSERT_ROWS 时，插入足够数量的行后再进行数据追加
        api = f"/sheets/v2/spreadsheets/{spreadsheet_token}/values_append?insertDataOption=INSERT_ROWS"
        url = f"{self.base_url}{api}"
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        payload = {"valueRange": {"range": sheet_range, "values": data}}

        # retry 次数设为1，避免数据重复插入
        response = self._retry_request("POST", url, headers, payload, max_retries=1)
        return response

    def get_spreadsheet_id(self, sheet_name: str):
        """
        对于知识库中的电子表格，你需调用获取知识空间节点信息接口来获取电子表格的 obj_token。此时，该 obj_token 也是该表格的 spreadsheetToken。
        """
        pass

    def get_data_from_sheet(self, spreadsheet: Spreadsheet):
        """
        从产品标注表中，获取现有的产品标签数据
        """
        spreadsheet_token = spreadsheet.spreadsheet_token
        sheet = spreadsheet.sheets[0]
        sheet_id = sheet.sheet_id
        start_col, end_col = sheet.start_col, sheet.end_col
        start_row, end_row = sheet.start_row, sheet.end_row
        # 一次性不能拉取太大，否则会超过大小限制
        processed_data = []
        for idx in range(start_row, end_row, 3000):
            # range 写法：<sheetId>!<开始单元格>:<结束单元格>
            # sheet_range = f"{sheet_id}!{start_col}{start_row}:{end_col}{end_row}"
            # range 写法：<sheetId>!<开始单元格>:<结束列>
            sheet_range = f"{sheet_id}!{start_col}{idx}:{end_col}{idx+2999}"
            try:
                response = self.get_spreadsheet_data(spreadsheet_token, sheet_range)
            except Exception as e:
                logger.error(f"获取产品标注表数据失败: {e}")
                return processed_data

            if not response.get("data", {}):
                logger.error(f"获取产品标注表数据失败，没有数据：{response}")
                return processed_data

            data_list = response["data"]["valueRange"]["values"]
            if len(data_list) == 0 or len(data_list[0]) == 0 or data_list[0][0] is None:
                return processed_data

            # 处理每一行数据的第二列(index=1)
            for item in data_list:
                # 跳过没有数据的行
                if not item or len(item) <= 1 or item[0] is None:
                    continue
                processed_data.append(item)

        return processed_data

    def append_data_to_sheet(
        self, spreadsheet: Spreadsheet, data: List[Any], batch: int = 5000
    ):
        """
        将数据追加到产品标注表中
        """
        spreadsheet_token = spreadsheet.spreadsheet_token
        sheet = spreadsheet.sheets[0]
        sheet_id = sheet.sheet_id
        start_col, end_col = sheet.start_col, sheet.end_col
        # range 写法：<sheetId>!<开始列>:<结束列>
        sheet_range = f"{sheet_id}!{start_col}:{end_col}"

        for i in range(0, len(data), batch):
            batch_data = data[i : i + batch]
            self.append_spreadsheet_data(spreadsheet_token, sheet_range, batch_data)

    def remove_data_from_sheet(
        self, spreadsheet: Spreadsheet, start_row: int, end_row: int
    ):
        """
        删除表中的数据
        """
        spreadsheet_token = spreadsheet.spreadsheet_token
        sheet = spreadsheet.sheets[0]
        sheet_id = sheet.sheet_id
        # insertDataOption 为 INSERT_ROWS 时，插入足够数量的行后再进行数据追加
        api = f"/sheets/v2/spreadsheets/{spreadsheet_token}/dimension_range"
        url = f"{self.base_url}{api}"
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        for idx in range(start_row, end_row, 5000):
            payload = {
                "dimension": {
                    "sheetId": sheet_id,
                    "majorDimension": "ROWS",
                    "startIndex": start_row,
                    "endIndex": min(start_row + 4999, end_row),
                }
            }

            # retry 次数设为1，避免数据重复插入
            response = self._retry_request(
                "DELETE", url, headers, payload, max_retries=1
            )
            logger.info(f"delete response:{response}")


if __name__ == "__main__":
    client = FeishuClient()
    whitelist = Spreadsheet(
        spreadsheet_token="XbeSs73eAhhjMvtUTHOcPr37nzb",
        sheets=[
            Sheet(
                sheet_id="060505",
                start_col="A",
                end_col="E",
                start_row=2,
                end_row=3000,
            )
        ],
    )
    data = client.get_data_from_sheet(whitelist)
    print(data)
