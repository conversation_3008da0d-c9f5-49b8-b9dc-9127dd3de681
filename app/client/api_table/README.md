# API Table 客户端

## 聚类标注客户端 (ClusteringClient)

聚类标注客户端提供了对聚类标注数据的读写操作，支持管理多个聚类标注物料。

### 约定

1. 聚类标注对应的物料类型为 **表格化标注**
2. 物料名称的格式为 **"聚类标注|$category-$dimension_name"**

例如：`聚类标注|白酒-营销卖点`、`聚类标注|美妆-适用场景`

### 数据模型

#### ClusteringDataRow
聚类标注数据行，包含以下字段：
- `word: str` - 待聚类的文本
- `display_x: float` - UI上显示的x坐标（可拖动修改）
- `display_y: float` - UI上显示的y坐标（可拖动修改）
- `initial_cluster_id: Optional[int]` - 初始时的类id（可重新生成）
- `labeled_cluster_id: Optional[int]` - 标注后的类id（自动计算填充）

#### ClusteringMaterial
聚类标注物料信息：
- `fileset_id: str` - 文件集ID
- `name: str` - 物料名称
- `category: str` - 类别（从名称解析）
- `dimension_name: str` - 维度名称（从名称解析）
- `datasheet_id: Optional[str]` - 数据表ID
- `space_id: Optional[str]` - 空间ID

### 使用示例

```python
from app.client.api_table.clustering_client import ClusteringClient, ClusteringDataRow

# 初始化客户端
client = ClusteringClient()

# 1. 列出所有聚类标注物料
materials = client.list_clustering_materials()
print(f"发现 {len(materials)} 个聚类标注物料")

for material in materials:
    print(f"- {material.name} ({material.category}-{material.dimension_name})")

# 2. 获取特定物料
material = client.get_material_by_name("白酒", "营销卖点")
if material:
    print(f"数据表ID: {material.datasheet_id}")

# 3. 读取聚类数据
data = client.get_material_data("白酒", "营销卖点")
print(f"数据行数: {len(data)}")

# 4. 创建新的数据行
new_data = [
    ClusteringDataRow(
        word="高端品质",
        display_x=1.23,
        display_y=4.56,
        initial_cluster_id=1
    ),
    ClusteringDataRow(
        word="传统工艺",
        display_x=2.34,
        display_y=5.67,
        initial_cluster_id=1
    )
]

# 5. 初始化表格（会检查是否已有数据）
success = client.initialize_table(material.datasheet_id, new_data)
if success:
    print("表格初始化成功")
else:
    print("表格初始化失败，可能已有数据需要先清空")

# 6. 更新数据
updated_data = client.get_material_data("白酒", "营销卖点")
# 修改数据...
for row in updated_data:
    if row.word == "高端品质":
        row.labeled_cluster_id = 2

# 保存更新
success = client.update_material_data("白酒", "营销卖点", updated_data)
print(f"数据更新{'成功' if success else '失败'}")
```

### 注意事项

1. **初始化检查**：使用 `initialize_table()` 时，如果表中已有超过10行数据，会抛出 `ValueError`，需要先手动清空数据。

2. **数据覆盖**：`write_all_data()` 和 `update_material_data()` 默认为覆盖写入模式。

3. **错误处理**：所有读写操作都包含异常处理，失败时会记录日志并返回适当的默认值。

4. **环境依赖**：需要正确配置 BAJIE 相关的环境变量（地址、用户名、密码、公司ID）。

### 测试

运行基本功能测试：
```bash
python app/client/api_table/clustering_client_test.py
```

运行完整测试（需要真实环境）：
```bash
pytest app/client/api_table/clustering_client_test.py -v
``` 