import json
import pytest
from pathlib import Path
from app.client.api_table.clustering_client import (
    ClusteringClient,
    ClusteringMaterial,
    ClusteringDataRow,
)


def load_clustering_test_data(
    file_path: str, limit: int = 10
) -> list[ClusteringDataRow]:
    """
    从JSON文件中加载聚类测试数据并转换为ClusteringDataRow对象

    Args:
        file_path (str): JSON文件路径
        limit (int): 限制加载的数据条数，默认10条

    Returns:
        list[ClusteringDataRow]: 转换后的聚类数据行列表
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        clustering_rows = []
        values = data.get("values", [])

        for i, item in enumerate(values):
            if i >= limit:  # 限制数据条数
                break

            row = ClusteringDataRow(
                数据ID=item["text_value"],
                display_x=float(item.get("tsne_x", 0.0)),
                display_y=float(item.get("tsne_y", 0.0)),
                weight=1.0,  # 默认权重为1
                initial_cluster_id=item.get("cluster_id"),
                labeled_cluster_id=None,  # 标注后的聚类ID，初始为None
                mapped_text=None,  # 映射到的代表词，初始为None
                actions=None,  # 标注操作，初始为None
            )
            clustering_rows.append(row)

        print(f"成功从 {file_path} 加载 {len(clustering_rows)} 条测试数据")
        return clustering_rows

    except Exception as e:
        print(f"加载测试数据失败: {e}")
        return []


def test_clustering_material_from_table_fileset():
    """测试从TableFileSet创建ClusteringMaterial"""
    from app.client.bajie import TableFileSet, LabelFileInfo

    # 创建测试数据
    label_file = LabelFileInfo(
        Id="test_label_file_id",
        FileType="apitable",
        CustomLabels={
            "space_id": "spcTest123",
            "datasheet_id": "dstTest456",
            "apply_label_num": 10,
            "labeler_max_pending_num": 20,
            "task_description": "请在表格中标注",
        },
    )

    # 测试正确格式的物料名称
    fileset = TableFileSet(
        Id="test_fileset_id",
        Metadata={"MaterialModel": "聚类标注|白酒-营销卖点"},
        LabelFiles=[label_file],
    )

    material = ClusteringMaterial.from_table_fileset(fileset)
    assert material is not None
    assert material.category == "白酒"
    assert material.dimension_name == "营销卖点"
    assert material.datasheet_id == "dstTest456"
    assert material.space_id == "spcTest123"

    # 测试错误格式的物料名称
    fileset_invalid = TableFileSet(
        Id="test_fileset_id",
        Metadata={"MaterialModel": "其他标注|白酒-营销卖点"},
        LabelFiles=[label_file],
    )

    material_invalid = ClusteringMaterial.from_table_fileset(fileset_invalid)
    assert material_invalid is None


def test_clustering_data_row():
    """测试ClusteringDataRow模型"""
    row = ClusteringDataRow(
        数据ID="高端品质",
        display_x=1.23,
        display_y=4.56,
        weight=1.0,
        initial_cluster_id=1,
        labeled_cluster_id=2,
        mapped_text="品质",
        actions=None,
    )

    assert row.数据ID == "高端品质"
    assert row.display_x == 1.23
    assert row.display_y == 4.56
    assert row.initial_cluster_id == 1
    assert row.labeled_cluster_id == 2
    assert row.mapped_text == "品质"


def test_clustering_client_list_materials():
    """测试列出聚类标注物料"""
    client = ClusteringClient()
    materials = client.list_clustering_materials()

    print(f"发现 {len(materials)} 个聚类标注物料")
    for material in materials:
        print(
            f"- {material.category}-{material.dimension_name}, datasheet_id: {material.datasheet_id}"
        )


@pytest.mark.e2e_test
def test_clustering_client_get_material():
    """测试获取特定的聚类标注物料"""
    client = ClusteringClient()

    # 这里需要根据实际的物料名称进行测试
    material = client.get_material_by_name("白酒", "营销卖点")

    if material:
        print(f"找到物料: {material.name}")
        print(f"数据表ID: {material.datasheet_id}")

        # 测试读取数据
        data = client.get_material_data("白酒", "营销卖点")
        print(f"数据行数: {len(data)}")
    else:
        print("未找到指定的聚类标注物料")


@pytest.mark.e2e_test
def test_创建聚类标注文件集2000点():
    """端到端测试：创建聚类标注文件集"""
    client = ClusteringClient()

    # 测试参数
    category = "测试分类"
    dimension_name = "测试维度"

    print(f"开始端到端测试：创建聚类标注文件集 {category}-{dimension_name}")

    # 1. 先检查是否已存在同名物料，如果有则删除
    print("1. 检查并删除已存在的同名物料...")
    existing_material = client.get_material_by_name(category, dimension_name)
    if existing_material:
        print(
            f"发现已存在的物料: {existing_material.name}, fileset_id: {existing_material.fileset_id}"
        )
        client.delete_material(category, dimension_name)
        print(f"成功删除已存在的物料")
    else:
        print("未发现同名物料，可以直接创建")

    # 2. 创建新的聚类标注物料
    print("2. 创建新的聚类标注物料...")
    material = client.create_material(
        category=category,
        dimension_name=dimension_name,
        description="这是一个端到端测试创建的聚类标注物料",
        apply_label_num=20,
        labeler_max_pending_num=10,
        task_description="请在表格中标注测试数据的聚类结果",
    )

    assert material is not None, "创建聚类标注物料失败"
    assert material.datasheet_id is not None, "物料缺少datasheet_id"

    print(f"成功创建物料: {material.name}")
    print(f"文件集ID: {material.fileset_id}")
    print(f"数据表ID: {material.datasheet_id}")

    # 3. 准备测试数据
    print("3. 准备测试数据...")

    # 从JSON文件加载测试数据
    json_file_path = "test_scripts/test_cases/白酒营销卖点聚类2000.json"
    test_data = load_clustering_test_data(json_file_path, limit=2000)

    assert len(test_data) > 5, f"数据条数应该大于5条，实际为 {len(test_data)} 条"

    print(f"准备了 {len(test_data)} 行测试数据")

    # 打印加载的数据样例
    for i, row in enumerate(test_data[:3]):
        print(
            f"  样例 {i+1}: 数据ID='{row.数据ID}', x={row.display_x:.2f}, y={row.display_y:.2f}, cluster={row.initial_cluster_id}"
        )

    # 4. 初始化数据表并写入数据
    print("4. 初始化数据表并写入数据...")
    client.truncate_and_initialize_table(material.space_id, material.datasheet_id)
    client.write_all_data(material.space_id, material.datasheet_id, test_data)
    print("数据写入成功")

    # 5. 读取数据验证
    print("5. 读取数据进行验证...")
    read_data = client.read_all_data(material.space_id, material.datasheet_id)

    print(f"读取到 {len(read_data)} 行数据")
    assert len(read_data) == len(
        test_data
    ), f"数据行数不匹配，期望: {len(test_data)}, 实际: {len(read_data)}"

    # 创建字典以便快速查找
    read_data_dict = {row.数据ID: row for row in read_data}
    test_data_dict = {row.数据ID: row for row in test_data}

    # 验证所有原始数据都存在于读取的数据中
    for original_row in test_data:
        数据ID = original_row.数据ID
        assert 数据ID in read_data_dict, f"未找到期望的数据ID: {数据ID}"

        read_row = read_data_dict[数据ID]

        # 严格对比每个字段
        assert (
            read_row.数据ID == original_row.数据ID
        ), f"数据ID不匹配: 期望='{original_row.数据ID}', 实际='{read_row.数据ID}'"

        assert (
            abs(read_row.display_x - original_row.display_x) < 0.001
        ), f"display_x不匹配 (数据ID={数据ID}): 期望={original_row.display_x}, 实际={read_row.display_x}"

        assert (
            abs(read_row.display_y - original_row.display_y) < 0.001
        ), f"display_y不匹配 (数据ID={数据ID}): 期望={original_row.display_y}, 实际={read_row.display_y}"

        assert (
            abs(read_row.weight - original_row.weight) < 0.001
        ), f"weight不匹配 (数据ID={数据ID}): 期望={original_row.weight}, 实际={read_row.weight}"

        assert (
            read_row.initial_cluster_id == original_row.initial_cluster_id
        ), f"initial_cluster_id不匹配 (数据ID={数据ID}): 期望={original_row.initial_cluster_id}, 实际={read_row.initial_cluster_id}"

        assert (
            read_row.labeled_cluster_id == original_row.labeled_cluster_id
        ), f"labeled_cluster_id不匹配 (数据ID={数据ID}): 期望={original_row.labeled_cluster_id}, 实际={read_row.labeled_cluster_id}"

        assert (
            read_row.mapped_text == original_row.mapped_text
        ), f"mapped_text不匹配 (数据ID={数据ID}): 期望={original_row.mapped_text}, 实际={read_row.mapped_text}"

        assert (
            read_row.actions == original_row.actions
        ), f"actions不匹配 (数据ID={数据ID}): 期望={original_row.actions}, 实际={read_row.actions}"

    # 验证读取的数据中没有多余的数据
    for read_row in read_data:
        数据ID = read_row.数据ID
        assert 数据ID in test_data_dict, f"读取到未预期的数据ID: {数据ID}"

    print("✅ 所有数据验证通过！原始数据与读取数据完全一致")

    # 6. 打印成功信息和访问链接
    print("\n" + "=" * 60)
    print("🎉 测试成功！聚类标注文件集创建完成")
    print(f"物料名称: {material.name}")
    print(f"文件集ID: {material.fileset_id}")
    print(f"数据表ID: {material.datasheet_id}")
    print(f"数据行数: {len(read_data)}")
    print("")
    print("请访问这里查看生成的测试标注：")
    print(
        f"https://cybercore.staging.sihe6.com/labeller/label?id={material.fileset_id}"
    )
    print("=" * 60)


@pytest.mark.e2e_test
def test_load_data_from_json():
    """测试从JSON文件加载聚类数据"""
    print("\n=== 测试从JSON文件加载聚类数据 ===")

    json_file_path = "test_scripts/test_cases/白酒营销卖点聚类2000.json"

    # 测试加载少量数据
    test_data = load_clustering_test_data(json_file_path, limit=10)

    assert len(test_data) > 0, "应该能够加载到数据"
    assert len(test_data) <= 10, "数据条数不应超过限制"

    # 从原始JSON文件中读取数据进行对比验证
    with open(json_file_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)

    original_values = original_data.get("values", [])
    expected_count = min(len(original_values), 10)  # 期望的数据条数

    assert (
        len(test_data) == expected_count
    ), f"加载的数据条数不正确，期望: {expected_count}, 实际: {len(test_data)}"

    # 验证每一条数据与原始JSON数据的对应关系
    for i, (converted_row, original_item) in enumerate(
        zip(test_data, original_values[:10])
    ):
        # 验证字段类型
        assert isinstance(
            converted_row.数据ID, str
        ), f"数据 {i+1}: 数据ID应该是字符串类型，实际为 {type(converted_row.数据ID)}"
        assert isinstance(
            converted_row.display_x, float
        ), f"数据 {i+1}: display_x应该是浮点类型，实际为 {type(converted_row.display_x)}"
        assert isinstance(
            converted_row.display_y, float
        ), f"数据 {i+1}: display_y应该是浮点类型，实际为 {type(converted_row.display_y)}"
        assert isinstance(
            converted_row.weight, float
        ), f"数据 {i+1}: weight应该是浮点类型，实际为 {type(converted_row.weight)}"

        # 验证字段值的正确性（与原始JSON数据对比）
        assert (
            converted_row.数据ID == original_item["text_value"]
        ), f"数据 {i+1}: 数据ID不匹配，期望='{original_item['text_value']}', 实际='{converted_row.数据ID}'"

        assert (
            abs(converted_row.display_x - float(original_item["tsne_x"])) < 0.001
        ), f"数据 {i+1}: display_x不匹配，期望={original_item['tsne_x']}, 实际={converted_row.display_x}"

        assert (
            abs(converted_row.display_y - float(original_item["tsne_y"])) < 0.001
        ), f"数据 {i+1}: display_y不匹配，期望={original_item['tsne_y']}, 实际={converted_row.display_y}"

        assert (
            converted_row.initial_cluster_id == original_item["cluster_id"]
        ), f"数据 {i+1}: initial_cluster_id不匹配，期望={original_item['cluster_id']}, 实际={converted_row.initial_cluster_id}"

        # 验证默认值
        assert (
            converted_row.weight == 1.0
        ), f"数据 {i+1}: 权重应该为1.0，实际为 {converted_row.weight}"
        assert (
            converted_row.labeled_cluster_id is None
        ), f"数据 {i+1}: 标注聚类ID应该为None，实际为 {converted_row.labeled_cluster_id}"
        assert (
            converted_row.mapped_text is None
        ), f"数据 {i+1}: 映射词应该为None，实际为 {converted_row.mapped_text}"
        assert (
            converted_row.actions is None
        ), f"数据 {i+1}: 操作应该为None，实际为 {converted_row.actions}"

    print(
        f"✅ 数据转换验证通过！成功验证从JSON文件加载并转换的 {len(test_data)} 条数据"
    )
    print(
        f"   原始数据示例: text_value='{original_values[0]['text_value']}', tsne_x={original_values[0]['tsne_x']}"
    )
    print(
        f"   转换后数据示例: 数据ID='{test_data[0].数据ID}', display_x={test_data[0].display_x}"
    )
