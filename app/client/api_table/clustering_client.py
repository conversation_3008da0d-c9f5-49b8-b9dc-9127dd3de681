import re
from typing import List, Optional
from pydantic import BaseModel, Field
import vika.const

from app.client.bajie import BajieClient, TableFileSet
from app.client.api_table.vika_client import ApiTableClient
from app.config import GLOBAL_CONF
from app.logger import logger

# 聚类标注表的主键字段名
PRIMARY_FIELD = "数据ID"


class ClusteringMaterial(BaseModel):
    """
    聚类标注物料信息

    约定：
    1. 聚类标注对应的物料类型为 表格化标注
    2. 物料名称的格式为 "聚类标注|$category-$dimension_name"
    """

    fileset_id: str
    name: str
    category: str
    dimension_name: str
    datasheet_id: str
    space_id: str

    @classmethod
    def from_table_fileset(
        cls, fileset: TableFileSet
    ) -> Optional["ClusteringMaterial"]:
        """从TableFileSet创建ClusteringMaterial"""
        # 解析物料名称，格式：聚类标注|$category-$dimension_name
        # 注意 category 可能包含子分类，比如 "$category1-$category2"，在这里不做识别
        pattern = r"^聚类标注\|(.+)-(.+?)$"
        match = re.match(pattern, fileset.get_name())

        if not match:
            return None

        category, dimension_name = match.groups()

        datasheet_id = fileset.get_datasheet_id()
        space_id = fileset.get_space_id()

        assert datasheet_id, "数据表ID为空"
        assert space_id, "空间ID为空"

        return cls(
            fileset_id=fileset.Id,
            name=fileset.get_name(),
            category=category,
            dimension_name=dimension_name,
            datasheet_id=datasheet_id,
            space_id=space_id,
        )


class ClusteringDataRow(BaseModel):
    """
    聚类标注数据行
    """

    数据ID: str = Field(description="待聚类的词语")
    display_x: float = Field(description="UI上显示的x坐标")
    display_y: float = Field(description="UI上显示的y坐标")
    weight: float = Field(description="权重")
    initial_cluster_id: Optional[int] = Field(None, description="初始时的类id")
    labeled_cluster_id: Optional[int] = Field(None, description="标注后的类id")
    mapped_text: Optional[str] = Field(None, description="映射到的代表词")
    actions: Optional[str] = Field(None, description="标注操作")


class ClusteringClient:
    """
    聚类标注客户端

    约定：
    1. 聚类标注对应的物料类型为 表格化标注
    2. 物料名称的格式为 "聚类标注|$category-$dimension_name"
    """

    def __init__(self):
        self.bajie_client = BajieClient()
        self.company_id = self.bajie_client.company_id

    def list_clustering_materials(self) -> List[ClusteringMaterial]:
        """
        列出所有聚类标注的物料

        Returns:
            List[ClusteringMaterial]: 聚类标注物料列表
        """
        # 获取所有表格化标注的文件集
        table_filesets = self.bajie_client.query_table_fileset(
            self.company_id, material_type="表格化标注"
        )

        clustering_materials = []
        for fileset in table_filesets:
            # 解析聚类标注物料
            material = ClusteringMaterial.from_table_fileset(fileset)
            if material:
                clustering_materials.append(material)

        return clustering_materials

    def get_material_by_name(
        self, category: str, dimension_name: str
    ) -> Optional[ClusteringMaterial]:
        """
        根据category和dimension_name获取聚类标注物料

        Args:
            category (str): 类别
            dimension_name (str): 维度名称

        Returns:
            Optional[ClusteringMaterial]: 聚类标注物料，如果不存在则返回None
        """
        materials = self.list_clustering_materials()
        for material in materials:
            if (
                material.category == category
                and material.dimension_name == dimension_name
            ):
                return material
        return None

    def delete_material(self, category: str, dimension_name: str):
        """
        删除指定的聚类标注物料

        Args:
            category (str): 类别
            dimension_name (str): 维度名称

        Returns:
            bool: 删除是否成功
        """
        material = self.get_material_by_name(category, dimension_name)
        if not material:
            logger.info(f"未找到需要删除的聚类标注物料: {category}-{dimension_name}")
            return

        self.bajie_client.delete_fileset(self.company_id, material.fileset_id)

    def create_material(
        self,
        category: str,
        dimension_name: str,
        description: str,
        apply_label_num: int = 20,
        labeler_max_pending_num: int = 50,
        task_description: str = "请在表格中标注聚类结果",
    ) -> Optional[ClusteringMaterial]:
        """
        创建聚类标注物料

        Args:
            category (str): 类别
            dimension_name (str): 维度名称
            description (str): 描述
            apply_label_num (int): 申请标注数量，默认50
            labeler_max_pending_num (int): 标注员最大待处理数量，默认30
            task_description (str): 任务描述

        Returns:
            Optional[ClusteringMaterial]: 创建的聚类标注物料，失败时返回None
        """
        # 构建物料名称
        material_model = f"聚类标注|{category}-{dimension_name}"

        # 调用bajie客户端创建文件集
        fileset = self.bajie_client.create_table_fileset(
            company_id=self.company_id,
            machine_id="clustering_test",
            material_model=material_model,
            description=description,
            apply_label_num=apply_label_num,
            labeler_max_pending_num=labeler_max_pending_num,
            task_description=task_description,
        )

        if not fileset:
            logger.error(f"创建聚类标注物料失败: {category}-{dimension_name}")
            return None

        # 转换为ClusteringMaterial
        material = ClusteringMaterial.from_table_fileset(fileset)
        if material:
            logger.info(
                f"成功创建聚类标注物料: {material.name}, fileset_id: {material.fileset_id}"
            )
            # 添加详细的物料信息日志
            print(f"🔧 创建的聚类标注物料详情:")
            print(f"  - fileset_id: {material.fileset_id}")
            print(f"  - name: {material.name}")
            print(f"  - category: {material.category}")
            print(f"  - dimension_name: {material.dimension_name}")
            print(f"  - space_id: {material.space_id}")
            print(f"  - datasheet_id: {material.datasheet_id}")

        return material

    def _get_api_client(self, space_id: str, datasheet_id: str) -> ApiTableClient:
        """获取API Table客户端"""
        # 明确传递环境配置，确保与bajie环境一致
        # 不传递stage_env参数，让ApiTableClient根据SIHEGPT_STAGE自动判断
        return ApiTableClient(space_id, datasheet_id, stage_env=None)

    def truncate_and_initialize_table(self, space_id: str, datasheet_id: str):
        """
        初始化某个表，让它符合聚类标注的列
        （检查数据行数，如果>10行则报错）

        Args:
            datasheet_id (str): 数据表ID
            data (List[ClusteringDataRow]): 初始化数据

        Returns:
            bool: 是否成功初始化

        Raises:
            ValueError: 如果表中已有数据（>10行）
        """
        api_client = self._get_api_client(space_id, datasheet_id)

        # 检查现有数据行数
        existing_records = api_client.get_all_records()
        existing_count = len(list(existing_records))

        if existing_count > 10:
            raise ValueError(
                f"表中已有 {existing_count} 行数据，需要先手动清空数据再初始化"
            )

        # 清空现有数据
        while True:
            batch_size = api_client.get_datasheet().records.all().count()
            if batch_size == 0:
                break
            logger.info(f"清空表中现有的 {batch_size} 行数据")
            # 这里需要调用vika的删除接口，具体实现需要根据vika客户端的API
            assert api_client.get_datasheet().records.all().delete(), "清空表失败"

        datasheet = api_client.get_datasheet()
        # 使用字典定义所有需要的字段及其类型
        columns = {
            "display_x": ("Number", {"precision": 4}),
            "display_y": ("Number", {"precision": 4}),
            "weight": ("Number", {"precision": 2}),
            "initial_cluster_id": ("Number", {"precision": 0}),
            "labeled_cluster_id": ("Number", {"precision": 0}),
            "mapped_text": ("Text", None),
            "actions": ("Text", None),
        }
        # 删除无关的列
        for field in datasheet.fields.all():
            if not field.isPrimary and field.name not in columns:
                try:
                    api_client.delete_field_by_name(field.name)
                except Exception as e:
                    logger.warning(f"删除字段 {field.name} 失败: {e}")
        # 添加需要的字段
        for column, (field_type, field_property) in columns.items():
            try:
                api_client.add_field(column, field_type, field_property)
            except Exception as e:
                logger.warning(f"创建字段 {column} 失败: {e}")

    def read_all_data(
        self, space_id: str, datasheet_id: str
    ) -> List[ClusteringDataRow]:
        """
        全量读取表数据

        Args:
            datasheet_id (str): 数据表ID

        Returns:
            List[ClusteringDataRow]: 聚类数据行列表
        """
        api_client = self._get_api_client(space_id, datasheet_id)

        records = api_client.get_all_records()
        data_rows = []
        for record in records:
            # 直接抛出异常，不做捕获
            row = record.json()
            row = ClusteringDataRow(**row)
            data_rows.append(row)
        return data_rows

    def write_all_data(
        self, space_id: str, datasheet_id: str, data: List[ClusteringDataRow]
    ):
        """
        全量写入表数据（覆盖写入）

        Args:
            datasheet_id (str): 数据表ID
            data (List[ClusteringDataRow]): 要写入的数据

        Returns:
            bool: 是否成功写入
        """

        api_client = self._get_api_client(space_id, datasheet_id)

        datasheet = api_client.get_datasheet()

        # 批量创建记录
        records_to_create = []
        for row in data:
            record_data = row.model_dump()
            records_to_create.append(record_data)

        # 批量插入记录
        if records_to_create:
            # 这里需要调用vika的批量创建接口
            # 具体实现需要根据vika客户端的API
            logger.info(f"准备写入 {len(records_to_create)} 行数据到表 {datasheet_id}")
            result = datasheet.records.bulk_create(records_to_create)
            assert len(result) == len(
                records_to_create
            ), f"写入数据失败，期望写入{len(records_to_create)}行，实际写入{len(result)}行"

    def get_material_data(
        self, category: str, dimension_name: str
    ) -> List[ClusteringDataRow]:
        """
        根据category和dimension_name获取聚类标注数据

        Args:
            category (str): 类别
            dimension_name (str): 维度名称

        Returns:
            List[ClusteringDataRow]: 聚类数据行列表
        """
        material = self.get_material_by_name(category, dimension_name)
        if not material or not material.datasheet_id:
            logger.warning(f"未找到聚类标注物料: {category}-{dimension_name}")
            return []

        return self.read_all_data(material.space_id, material.datasheet_id)

    def update_material_data(
        self, category: str, dimension_name: str, data: List[ClusteringDataRow]
    ):
        """
        更新聚类标注数据

        Args:
            category (str): 类别
            dimension_name (str): 维度名称
            data (List[ClusteringDataRow]): 要更新的数据

        Returns:
            bool: 是否成功更新
        """
        material = self.get_material_by_name(category, dimension_name)
        if not material or not material.datasheet_id:
            logger.error(f"未找到聚类标注物料: {category}-{dimension_name}")
            raise ValueError(f"未找到聚类标注物料: {category}-{dimension_name}")

        self.write_all_data(material.space_id, material.datasheet_id, data)
