from enum import Enum
from typing import Optional

from vika import Vika
from vika.datasheet import Datasheet, QuerySet
from app.env import SIHEGPT_STAGE, StageEnv
import vika.const


class ApiTableClient:
    """
    底层API Table客户端，提供基础的数据表操作功能
    """

    def __init__(
        self, space_id: str, datasheet_id: str, stage_env: Optional[StageEnv] = None
    ):
        """
        初始化API Table客户端

        Args:
            space_id (str): 空间ID
            datasheet_id (str): 数据表ID
            stage_env (Optional[StageEnv], optional): 环境配置. Defaults to None，则根据SIHEGPT_STAGE确定环境配置
        """
        self.space_id = space_id
        self.datasheet_id = datasheet_id

        # 优先判断stage_env，如果为空，则根据SIHEGPT_STAGE确定环境配置
        if stage_env is None:
            stage_env = SIHEGPT_STAGE

        is_prod = stage_env.is_prod_env()

        # 添加详细的环境配置日志
        print(f"🔧 VikaClient 环境配置:")
        print(f"  - SIHEGPT_STAGE: {SIHEGPT_STAGE}")
        print(f"  - SIHEGPT_STAGE.is_prod_env(): {SIHEGPT_STAGE.is_prod_env()}")
        print(f"  - stage_env 参数: {stage_env}")
        print(f"  - 最终 is_prod: {is_prod}")
        print(f"  - space_id: {space_id}")
        print(f"  - datasheet_id: {datasheet_id}")

        if is_prod:
            # 生产环境配置 (robot-sihe账号)
            api_token = "uskwU27Ydbl6eHuGrFGBLGf"
            api_base = "https://apitable.sihe6.com"
            print(f"  - 使用生产环境配置: {api_base}")
            print(f"  - API Token: {api_token[:8]}...")
            self.vika = Vika(api_token, api_base=api_base)
        else:
            # 其他环境（包括STAGING, DEV等）使用staging配置
            api_token = "uskCKkqTZm7LEpGtc6PKPDx"
            api_base = "https://apitable.staging.sihe6.com"
            print(f"  - 使用Staging环境配置: {api_base}")
            print(f"  - API Token: {api_token[:8]}...")
            self.vika = Vika(api_token, api_base=api_base)

        # 初始化后，创建BajieClient并邀请用户加入vika space
        self._ensure_user_joined_vika_space(stage_env)

    def _ensure_user_joined_vika_space(self, stage_env: StageEnv) -> None:
        """
        确保用户已加入vika space，这是两个系统，用户信息需要同步

        Args:
            stage_env (StageEnv): 环境配置
        """
        # 导入放在这里避免循环导入
        from app.client.bajie import BajieClient

        # 创建对应环境的BajieClient
        bajie_client = BajieClient(stage_env=stage_env)

        # 邀请用户加入vika space
        success = bajie_client.join_table_fileset_vika_space(self.space_id)
        if success:
            print(f"✅ 成功邀请自己加入 vika space: {self.space_id}")
        else:
            raise Exception(
                f"⚠️ 成功邀请自己加入 vika space 失败: space_id={self.space_id}"
            )

    def get_datasheet(self, field_key: str = "name") -> Datasheet:
        """
        获取数据表实例

        Args:
            field_key (str, optional): 字段键类型. Defaults to "name".

        Returns:
            Datasheet: 数据表实例
        """
        return self.vika.space(self.space_id).datasheet(
            self.datasheet_id, field_key=field_key
        )

    def get_all_records(self, field_key: str = "name") -> QuerySet:
        """
        获取数据表的所有记录

        Args:
            field_key (str, optional): 字段键类型. Defaults to "name".

        Returns:
            QuerySet: 记录查询集
        """
        return self.get_datasheet(field_key).records.all()

    def filter_records(self, field_key: str = "name", **filters) -> QuerySet:
        """
        根据条件过滤记录

        Args:
            field_key (str, optional): 字段键类型. Defaults to "name".
            **filters: 过滤条件

        Returns:
            QuerySet: 过滤后的记录查询集
        """
        return self.get_datasheet(field_key).records.filter(**filters)

    def delete_field_by_name(self, field_name: str) -> None:
        """
        根据字段名称删除字段

        Args:
            field_name (str): 要删除的字段名称

        Raises:
            ValueError: 如果字段不存在
            Exception: 删除字段失败或发生异常
        """
        datasheet = self.get_datasheet()

        # 查找指定名称的字段
        target_field = None
        for field in datasheet.fields.all():
            if field.name == field_name:
                target_field = field
                break

        if target_field is None:
            return

        # 删除字段
        result = datasheet.fields.delete(target_field.id)
        if result:
            # 删除成功
            print(f"✅ 成功删除字段: {field_name}")
        else:
            # 删除失败，抛出异常
            raise Exception(f"删除字段失败: {field_name}")

    def add_field(
        self, field_name: str, field_type: str, field_property: Optional[dict] = None
    ) -> None:
        """
        添加单文本字段

        Args:
            field_name (str): 字段名称
            field_type (str): 字段类型, 推荐使用 SingleText, Text, Number
            更多字段类型见  https://developers.aitable.ai/api/reference/#tag/Field

        Raises:
            Exception: 获取现有字段失败、字段已存在、创建字段失败或发生异常
        """
        # 先检查字段是否已存在
        datasheet = self.get_datasheet()
        existing_fields = set()

        try:
            for field in datasheet.fields.all():
                existing_fields.add(field.name)
        except Exception as e:
            # 获取现有字段失败，抛出异常
            raise Exception(f"获取现有字段失败: {e}")

        # 检查字段是否已存在
        if field_name in existing_fields:
            # 字段已存在，记录日志并返回
            print(f"⏭️  字段已存在，跳过创建: {field_name}")
            return

        # 创建新字段
        req_data = {"name": field_name, "type": field_type, "property": field_property}

        try:
            field = self.get_datasheet().fields.create(req_data)
        except Exception as e:
            # 创建字段时发生异常，抛出异常
            raise Exception(f"创建字段异常: {field_name} - {e}")

        if field:
            # 创建成功，打印日志
            print(f"✅ 成功创建文本字段: {field_name}")
        else:
            # 创建失败，抛出异常
            raise Exception(f"创建字段失败: {field_name}")

    def debug_record_structure(self) -> None:
        """
        调试方法：打印 record 的完整结构，帮助理解主键值的存储方式
        """
        try:
            records = self.get_all_records()
            datasheet = self.get_datasheet()

            print(f"\n=== 调试 Record 结构 ===")
            print(f"主键字段信息:")
            print(f"  - 主键字段名: {datasheet.primary_field.name}")
            print(f"  - 主键字段ID: {datasheet.primary_field.id}")
            print(f"  - 主键字段类型: {datasheet.primary_field.type}")

            print(f"\n所有字段信息:")
            for field in datasheet.fields.all():
                print(
                    f"  - 字段名: {field.name}, ID: {field.id}, 类型: {field.type}, 是否主键: {field.isPrimary}"
                )

            print(f"\n记录结构示例:")
            for i, record in enumerate(records):
                if i >= 2:  # 只看前2条记录
                    break

                print(f"\n--- 记录 {i+1} ---")
                print(f"Record ID: {record.id}")
                print(f"Record 属性:")
                for attr in dir(record):
                    if not attr.startswith("_"):
                        try:
                            value = getattr(record, attr)
                            if not callable(value):
                                print(f"  - {attr}: {value}")
                        except:
                            pass

                print(f"Record.json(): {record.json()}")

                print(f"Record.fields 内容:")
                if hasattr(record, "fields"):
                    fields_data = record.fields
                    print(f"  - fields 类型: {type(fields_data)}")
                    print(f"  - fields 内容: {fields_data}")
                else:
                    print("  - 没有 fields 属性")

        except Exception as e:
            print(f"调试时发生错误: {e}")


class ProductDimensionTableClient:
    """
    产品维度表客户端，提供产品维度相关的业务操作
    """

    # 产品维度表的datasheet_id
    SPACE_ID = "spcLWjVr1o7Kk"
    DATASHEET_ID = "dsti9s3rPSegabMLVQ"

    def __init__(self):
        """
        初始化产品维度表客户端
        """
        # 创建生产环境的StageEnv实例
        prod_stage_env = StageEnv("PROD")
        self.api_client = ApiTableClient(
            self.SPACE_ID, self.DATASHEET_ID, stage_env=prod_stage_env
        )

    def get_product_dimension_records(self) -> QuerySet:
        """
        获取已审核通过的产品维度记录

        Returns:
            QuerySet: 审核通过的产品维度记录
        """
        return self.api_client.filter_records(标注状态="审核通过")

    def get_all_product_dimension_records(self) -> QuerySet:
        """
        获取所有产品维度记录（不过滤状态）

        Returns:
            QuerySet: 所有产品维度记录
        """
        return self.api_client.get_all_records()


class VideoDimensionTableClient:
    """
    视频维度表客户端，视频产品维度相关的业务操作
    """

    SPACE_ID = "spcs5XWWhx1br"
    DATASHEET_ID = "dst3cXQqy0GPLkkHaw"

    def __init__(self):
        """
        初始化产品维度表客户端
        """
        # 创建生产环境的StageEnv实例
        prod_stage_env = StageEnv("PROD")
        self.api_client = ApiTableClient(
            self.SPACE_ID, self.DATASHEET_ID, stage_env=prod_stage_env
        )

    def get_video_dimension_records(self) -> QuerySet:
        """
        获取已审核通过的产品维度记录

        Returns:
            QuerySet: 审核通过的产品维度记录
        """
        return self.api_client.filter_records(标注状态="审核通过")
