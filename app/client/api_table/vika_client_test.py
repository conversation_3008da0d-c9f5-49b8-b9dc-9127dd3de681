import pytest
from app.client.api_table.vika_client import ApiTableClient, ProductDimensionTableClient


@pytest.mark.e2e_test
def test_product_dimension_table_client():
    """测试应用层ProductDimensionTableClient"""
    # 创建 ProductDimensionTableClient 实例
    client = ProductDimensionTableClient()

    # 测试获取审核通过的产品维度记录
    approved_records = client.get_product_dimension_records()
    assert approved_records is not None, "无法获取审核通过的产品维度记录"

    approved_list = list(approved_records)
    print(f"ProductDimensionTableClient - 审核通过的记录数: {len(approved_list)}")

    # 测试获取所有产品维度记录
    all_records = client.get_all_product_dimension_records()
    assert all_records is not None, "无法获取所有产品维度记录"

    all_list = list(all_records)
    print(f"ProductDimensionTableClient - 所有记录数: {len(all_list)}")

    # 验证审核通过的记录数量应该小于等于总记录数
    assert len(approved_list) <= len(all_list), "审核通过的记录数不应超过总记录数"


@pytest.mark.skip(reason="还没有稳定的 datasheet 用来测试")
@pytest.mark.e2e_test
def test_read_specific_datasheet():
    """测试读取指定的 datasheet (dstgTNXaeHH51PX35M) 并打印所有行内容"""
    # 指定的 datasheet ID
    space_id = "spc_XXXXXXXXXXXXXXXXXx"
    datasheet_id = "dstgTNXaeHH51PX35M"

    # 创建 ApiTableClient 实例
    client = ApiTableClient(space_id, datasheet_id)

    # 获取所有记录
    try:
        records = client.get_all_records()
        assert records is not None, f"无法获取 datasheet {datasheet_id} 的记录"

        records_list = [x.json() for x in records]
        print(f"\n=== 数据表 {datasheet_id} 的所有行内容 ===")
        print(f"总记录数: {len(records_list)}")

        # 打印每一行的内容
        for i, record in enumerate(records_list, 1):
            print(f"\n--- 第 {i} 行 ---")
            print(record)

        print(f"\n=== 数据表读取完成，共 {len(records_list)} 行 ===")

    except Exception as e:
        print(f"读取 datasheet {datasheet_id} 时发生错误: {e}")
        raise
