from langchain.globals import set_llm_cache
from langchain_community.cache import RedisCache
import app.client.redis_client as redis_client
from app.config import GLOBAL_CONF
from app.logger import logger


_inited = False


def setup_global_cache():
    """这个函数调用一次就行了，重复调用也不会有影响"""
    global _inited
    if _inited:
        return
    if GLOBAL_CONF.ENABLE_LLM_CACHE:
        set_llm_cache(
            RedisCache(redis_client.RedisClient().get_client(), ttl=60 * 60 * 24 * 30)
        )
        logger.info("[setup_global_cache] 已配置并启用 LLM 缓存")
    else:
        pass
        # logger.warn(
        #     "[setup_global_cache] 由于 ENABLE_LLM_CACHE 为 False，不启用 LLM 缓存"
        # )
    _inited = True
