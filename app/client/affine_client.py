import base64
import datetime
import hashlib
from typing import List, Dict, Any

import requests
from jose import jwt
from nanoid import generate

from app.config import (
    AFFINE_ADDRESS_FRONTEND_PROD,
    AFFINE_ADDRESS_FRONTEND_TEST,
    AFFINE_ADDRESS_BACKEND_PROD,
    AFFINE_ADDRESS_BACKEND_TEST,
    AFFINE_SIHE_WORKSPACE_ID_PROD,
    AFFINE_SIHE_WORKSPACE_ID_TEST,
    ISSUER,
    AUDIENCE,
    SECRET_KEY,
)
from app.env import SIHEGPT_STAGE
from app.logger import logger
from app.utils.fractional_indexing_utils import generate_fractional_indexing_key_between

AFFINE_BLOCK_TYPE_WITH_BLOB = {"affine:image", "affine:chart"}

authorization_token = "*3XC%8W(TL(ibu#zm76B"

# demo doc space
sihe_workspace = (
    AFFINE_SIHE_WORKSPACE_ID_PROD
    if SIHEGPT_STAGE.is_prod_env()
    else AFFINE_SIHE_WORKSPACE_ID_TEST
)


# deprecated：仅标注系统用
class AffineClient:
    def __init__(self):
        self._frontend_host = (
            AFFINE_ADDRESS_FRONTEND_PROD
            if SIHEGPT_STAGE.is_prod_env()
            else AFFINE_ADDRESS_FRONTEND_TEST
        )
        self._header = {
            "x-sihe-authorization": authorization_token,
        }
        self._backend_host = (
            AFFINE_ADDRESS_BACKEND_PROD
            if SIHEGPT_STAGE.is_prod_env()
            else AFFINE_ADDRESS_BACKEND_TEST
        )
        self._url_init_user = f"http://{self._backend_host}/api/liulian/user-init"
        self._url_import_markdown = (
            f"http://{self._backend_host}/api/liulian/import-markdown"
        )
        self._url_list_workspace = f"http://{self._backend_host}/api/liulian/workspace"
        self._url_create_folders_meta = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/folders"
        self._url_create_doc = (
            f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs"
        )
        self._url_read_doc = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs/{{doc_id}}"
        self._url_update_doc = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs/{{doc_id}}"
        self._url_delete_doc = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs/{{doc_id}}"
        self._url_list_doc = (
            f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs"
        )

        self._url_get_workspace_blobs = (
            f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/blobs"
        )
        self._url_get_workspace_blob = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/blobs/{{blob_id}}"
        self._url_copy_blob = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/blobs/copy"  # 这里url中的workspace_id是source workspace_id, target workspace_id在body中, blob_id是copy前后不变
        self._url_create_blob = (
            f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/blobs"
        )

        self._url_get_doc_tags = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs/{{doc_id}}/tags"
        self._url_put_doc_tags = f"http://{self._backend_host}/api/liulian/workspace/{{workspace_id}}/docs/{{doc_id}}/tags"

    def doc_url(self, workspace_id: str, doc_id: str) -> str:
        if not workspace_id:
            workspace_id = sihe_workspace
        return (
            f"https://{self._frontend_host}/workspace/{workspace_id}/{doc_id}?mode=page"
        )

    def folder_doc_id(self, workspace_id: str) -> str:
        return f"db${workspace_id}$folders"

    def init_user(self, user_id: int) -> tuple[str, str]:
        authorization_token = jwt.encode(
            {
                "iss": ISSUER,
                "aud": AUDIENCE,
                "sub": str(user_id),
                "iat": datetime.datetime.utcnow() + datetime.timedelta(minutes=30),
                "exp": datetime.datetime.utcnow() + datetime.timedelta(minutes=30),
                "authorities": [],
                "authorityPatterns": [],
                "refreshCount": 0,
                "refreshLimit": 365,
            },
            SECRET_KEY,
            algorithm="HS256",
        )

        res = requests.post(
            self._url_init_user,
            headers=self._header,
            json={
                "userId": user_id,
                "authorizationToken": f"Bearer {authorization_token}",
            },
        )
        if res.status_code != 201:
            raise Exception(res.text)
        j = res.json()
        affine_user_id = j["userId"]
        affine_workspace_id = j["workspaceId"]
        return affine_user_id, affine_workspace_id

    def import_markdown(self, workspace_id: str, title: str, file_url: str) -> str:
        res = requests.post(
            self._url_import_markdown,
            headers=self._header,
            json={
                "workspaceId": workspace_id,
                "title": title,
                "fileUrl": file_url,
            },
        )
        if res.status_code != 201:
            raise Exception(res.text)
        j = res.json()
        affine_doc_id = j["docId"]
        return affine_doc_id

    def get_all_sihe_docs(self) -> dict[str, Any]:
        # 获取所有workspace下的docs 并添加到docs_map
        workspace = sihe_workspace
        docs_map = {}
        doc_ids = requests.get(
            f"http://{self._backend_host}/api/liulian/workspace/{workspace}/docs",
            headers=self._header,
        ).json()["docIds"]
        logger.info("查询到所有原报告，ids：" + str(doc_ids))
        for id in doc_ids:
            res = requests.get(
                f"http://{self._backend_host}/api/liulian/workspace/{workspace}/docs/{id}",
                headers=self._header,
            )
            if res.status_code != 200:
                raise Exception(res.text)
            j = res.json()
            docs_map[id] = j
        return docs_map

    def list_workspace(self) -> List[str]:
        res = requests.get(
            self._url_list_workspace,
            headers=self._header,
        )
        if res.status_code != 200:
            raise Exception(res.text)
        j = res.json()
        return j.get("workspaceIds", [])

    def list_doc(self, workspace_id: str) -> List[str]:
        res = requests.get(
            self._url_list_doc.format(workspace_id=workspace_id),
            headers=self._header,
        )
        if res.status_code != 200:
            raise Exception(res.text)
        j = res.json()
        return j.get("docIds", [])

    def create_doc(self, workspace_id: str, body: Dict[str, Any]) -> str:
        res = requests.post(
            self._url_create_doc.format(workspace_id=workspace_id),
            headers=self._header,
            json=body,
        )
        if res.status_code != 201:
            raise Exception(res.text)
        j = res.json()
        return j["docId"]

    def read_doc(self, workspace_id: str, doc_id: str) -> Dict[str, Any]:
        res = requests.get(
            self._url_read_doc.format(workspace_id=workspace_id, doc_id=doc_id),
            headers=self._header,
        )
        if res.status_code != 200:
            raise Exception(res.text)
        j = res.json()
        return j

    def update_doc(self, workspace_id: str, doc_id: str, body: Dict[str, Any]):
        res = requests.put(
            self._url_update_doc.format(workspace_id=workspace_id, doc_id=doc_id),
            headers=self._header,
            json=body,
        )
        if res.status_code != 200:
            raise Exception(res.text)

    def delete_doc(self, workspace_id: str, doc_id: str):
        res = requests.delete(
            self._url_delete_doc.format(workspace_id=workspace_id, doc_id=doc_id),
            headers=self._header,
        )
        if res.status_code != 200:
            raise Exception(res.text)

    def list_folders(self, workspace_id: str) -> Dict[str, Any]:
        return self.read_doc(workspace_id, self.folder_doc_id(workspace_id))

    def create_folder(self, workspace_id: str, folder_name: str) -> str:
        id = generate()

        try:
            # 文件夹已经存在了
            folders_info = self.list_folders(workspace_id)

            largest_top_folder_index = None
            for k, v in folders_info.items():
                if v.get("data", "") == folder_name:
                    return k
                if largest_top_folder_index is None or (
                    v.get("parentId") is None and k > largest_top_folder_index
                ):
                    largest_top_folder_index = k

            folders_info[id] = {
                "parentId": None,
                "type": "folder",
                "data": folder_name,
                "index": generate_fractional_indexing_key_between(
                    largest_top_folder_index, None
                ),
                "id": id,
            }
            self.update_doc(
                workspace_id, self.folder_doc_id(workspace_id), folders_info
            )
        except Exception:
            # 文件夹元数据的snapshot还不存在
            folders_info = {
                id: {
                    "parentId": None,
                    "type": "folder",
                    "data": folder_name,
                    "index": generate_fractional_indexing_key_between(None, None),
                    "id": id,
                },
            }

            res = requests.post(
                self._url_create_folders_meta.format(workspace_id=workspace_id),
                headers=self._header,
                json=folders_info,
            )
            if res.status_code != 201:
                raise Exception(res.text)

        return id

    def move_doc_to_folder(
        self,
        workspace_id: str,
        doc_id: str,
        folder_id: str = None,  # type: ignore
        folder_name: str = None,  # type: ignore
    ):
        info = {
            "parentId": None,
            "type": "doc",
            "data": doc_id,
            "index": None,
            "id": None,
        }

        folder_index = None
        folders_info = self.list_folders(workspace_id)
        for k, v in folders_info.items():
            if (
                folder_id is None
                and folder_name is not None
                and v.get("data", "") == folder_name
            ):
                folder_id = k
                folder_index = v.get("index")
            if v.get("data", "") == doc_id:
                info = v

        first_brother_index = None
        for k, v in folders_info.items():
            if v.get("parentId", "") == folder_id:
                first_brother_index = v.get("index")
                break

        if folder_id is None and folder_name is None:
            raise Exception("You must specify a folder or a folder name")

        if info["id"] is not None:
            info["parentId"] = folder_id
            info["index"] = generate_fractional_indexing_key_between(
                folder_index, first_brother_index
            )
            folders_info[info["id"]] = info
        else:
            id = generate()
            info["parentId"] = folder_id
            info["index"] = generate_fractional_indexing_key_between(
                folder_index, first_brother_index
            )
            info["id"] = id
            folders_info[id] = info

        self.update_doc(workspace_id, self.folder_doc_id(workspace_id), folders_info)

    def copy_blob(
        self, source_workspace_id: str, target_workspace_id: str, blob_id: str
    ):
        res = requests.post(
            self._url_copy_blob.format(workspace_id=source_workspace_id),
            headers=self._header,
            json={
                "targetWorkspaceId": target_workspace_id,
                "sourceBlobId": blob_id,
            },
        )
        if res.status_code != 201:
            raise Exception(res.text)

        return

    # 遍历整个文档，找到所有的存放在blob中的资源，返回blob_id列表
    def get_doc_resource_blobs(self, doc: Dict[str, Any]) -> List[str]:
        blobs = set()
        blocks = doc.get("blocks", {})

        for block in blocks.values():
            if block.get("sys:flavour") in AFFINE_BLOCK_TYPE_WITH_BLOB:
                blob_id = block.get("prop:sourceId")
                if blob_id:  # 只添加有效的 blob_id
                    blobs.add(blob_id)

        return list(blobs)

    def copy_doc(
        self, source_workspace_id: str, target_workspace_id: str, doc_id: str
    ) -> str:
        raw = self.read_doc(source_workspace_id, doc_id)
        blobs = self.get_doc_resource_blobs(raw)
        for blob in blobs:
            self.copy_blob(source_workspace_id, target_workspace_id, blob)
        new_doc_id = self.create_doc(target_workspace_id, raw)
        try:
            tags = self.get_doc_tags(source_workspace_id, doc_id)
            if tags and len(tags) > 0:
                self.put_doc_tags(target_workspace_id, new_doc_id, tags)
        except Exception as e:
            logger.warning(
                f"copy {source_workspace_id}/{doc_id} tags to {target_workspace_id}/{new_doc_id} failed: {e}"
            )
        return new_doc_id

    def create_blob(self, workspace_id: str, filepath: str, mime_type: str) -> str:
        # @see @blocksuite/global/src/utils/crypto.ts sha
        def generate_source_id(input_data: bytes) -> str:
            hash_object = hashlib.sha256(input_data)
            hash_bytes = hash_object.digest()
            base64_string = base64.b64encode(hash_bytes).decode("utf-8")
            return base64_string.replace("+", "-").replace("/", "_")

        with open(filepath, "rb") as file:
            buffer = file.read()
            source_id = generate_source_id(buffer)

        if len(source_id) == 0:
            raise Exception("empty source id")

        payload = {
            "operations": '{ "query": "mutation ($file: Upload!) { createBlob(file: $file) }", "variables": { "file": null } }',
            "map": '{ "0": ["variables.file"] }',
        }
        files = [("0", (source_id, open(filepath, "rb"), mime_type))]
        res = requests.post(
            self._url_create_blob.format(workspace_id=workspace_id),
            headers=self._header,
            data=payload,
            files=files,
        )
        if res.status_code != 201:
            raise Exception(res.text)
        return source_id

    def get_doc_tags(self, workspace_id: str, doc_id: str) -> List[str]:
        res = requests.get(
            self._url_get_doc_tags.format(workspace_id=workspace_id, doc_id=doc_id),
            headers=self._header,
        )
        if res.status_code != 200:
            raise Exception(res.text)
        j = res.json()
        return j

    def put_doc_tags(self, workspace_id: str, doc_id: str, tags: List[str]):
        res = requests.put(
            self._url_put_doc_tags.format(workspace_id=workspace_id, doc_id=doc_id),
            headers=self._header,
            json=tags,
        )
        if res.status_code != 200:
            raise Exception(res.text)


if __name__ == "__main__":
    client = AffineClient()
    # print(client.init_user(1))
    # print(client.import_markdown(demo_workspace, "test", "https://raw.githubusercontent.com/sihegpt/affine/main/README.md"))
    # client.get_all_sihe_docs()

    # workspace_ids = client.list_workspace()
    # print(workspace_ids)
    workspace_ids = ["c1392954-9e9c-462c-8925-1275f3acce13"]
    doc_ids = client.list_doc(workspace_ids[0])
    print(doc_ids)

    # doc_content = client.read_doc(workspace_ids[0], doc_ids[0])
    # print(doc_content)

    # doc_id = client.create_doc(workspace_ids[0], {'blocks': {'4CGb6YPURlV38rJucxaDE': {'sys:id': '4CGb6YPURlV38rJucxaDE', 'sys:flavour': 'affine:page', 'sys:version': 2, 'sys:children': ['KeqAPTbn-XI7BlEKc4jX0', 'yoY4V7WQ3MhtMkt7nSbIa'], 'prop:title': 'title'}, 'KeqAPTbn-XI7BlEKc4jX0': {'sys:id': 'KeqAPTbn-XI7BlEKc4jX0', 'sys:flavour': 'affine:surface', 'sys:version': 5, 'sys:children': [], 'prop:elements': {'type': '$blocksuite:internal:native$', 'value': {}}}, 'yoY4V7WQ3MhtMkt7nSbIa': {'sys:id': 'yoY4V7WQ3MhtMkt7nSbIa', 'sys:flavour': 'affine:note', 'sys:version': 1, 'sys:children': ['mxpi5T7UAZ2C37sQIeEqL', 'Tcjx_F8IiicBPnJ18PfFe', 'XR0_fAZc9fOZSxXR7iS7F', 'giLjx0k0Co2wUIzeK6Ggu'], 'prop:xywh': '[0,0,800,95]', 'prop:background': '--affine-note-background-white', 'prop:index': 'a0', 'prop:hidden': False, 'prop:displayMode': 'both', 'prop:edgeless': {'style': {'borderRadius': 8, 'borderSize': 4, 'borderStyle': 'none', 'shadowType': '--affine-note-shadow-box'}}}, 'mxpi5T7UAZ2C37sQIeEqL': {'sys:id': 'mxpi5T7UAZ2C37sQIeEqL', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'text', 'prop:text': ''}, 'Tcjx_F8IiicBPnJ18PfFe': {'sys:id': 'Tcjx_F8IiicBPnJ18PfFe', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'h1', 'prop:text': 'head 1'}, 'XR0_fAZc9fOZSxXR7iS7F': {'sys:id': 'XR0_fAZc9fOZSxXR7iS7F', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'h2', 'prop:text': 'head 2'}, 'giLjx0k0Co2wUIzeK6Ggu': {'sys:id': 'giLjx0k0Co2wUIzeK6Ggu', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'text', 'prop:text': 'text'}}})
    # print(doc_id)
    #
    # doc_content = client.read_doc(workspace_ids[0], doc_id)
    # print(doc_content)
    #
    # client.update_doc(workspace_ids[0], doc_id, {'blocks': {'4CGb6YPURlV38rJucxaDE': {'sys:id': '4CGb6YPURlV38rJucxaDE', 'sys:flavour': 'affine:page', 'sys:version': 2, 'sys:children': ['KeqAPTbn-XI7BlEKc4jX0', 'yoY4V7WQ3MhtMkt7nSbIa'], 'prop:title': 'title'}, 'KeqAPTbn-XI7BlEKc4jX0': {'sys:id': 'KeqAPTbn-XI7BlEKc4jX0', 'sys:flavour': 'affine:surface', 'sys:version': 5, 'sys:children': [], 'prop:elements': {'type': '$blocksuite:internal:native$', 'value': {}}}, 'yoY4V7WQ3MhtMkt7nSbIa': {'sys:id': 'yoY4V7WQ3MhtMkt7nSbIa', 'sys:flavour': 'affine:note', 'sys:version': 1, 'sys:children': ['mxpi5T7UAZ2C37sQIeEqL', 'Tcjx_F8IiicBPnJ18PfFe', 'XR0_fAZc9fOZSxXR7iS7F', 'giLjx0k0Co2wUIzeK6Ggu'], 'prop:xywh': '[0,0,800,95]', 'prop:background': '--affine-note-background-white', 'prop:index': 'a0', 'prop:hidden': False, 'prop:displayMode': 'both', 'prop:edgeless': {'style': {'borderRadius': 8, 'borderSize': 4, 'borderStyle': 'none', 'shadowType': '--affine-note-shadow-box'}}}, 'mxpi5T7UAZ2C37sQIeEqL': {'sys:id': 'mxpi5T7UAZ2C37sQIeEqL', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'text', 'prop:text': ''}, 'Tcjx_F8IiicBPnJ18PfFe': {'sys:id': 'Tcjx_F8IiicBPnJ18PfFe', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'h1', 'prop:text': 'head 1'}, 'XR0_fAZc9fOZSxXR7iS7F': {'sys:id': 'XR0_fAZc9fOZSxXR7iS7F', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'h2', 'prop:text': 'head 2'}, 'giLjx0k0Co2wUIzeK6Ggu': {'sys:id': 'giLjx0k0Co2wUIzeK6Ggu', 'sys:flavour': 'affine:paragraph', 'sys:version': 1, 'sys:children': [], 'prop:type': 'text', 'prop:text': 'texttext'}}})
    #
    #
    for doc in doc_ids:
        print(f"删除文档 {doc}")
        client.get_doc_tags_doc(workspace_ids[0], doc)  # type: ignore

    # client.create_folder('48a55528-9104-44c1-ba48-6c94b4593f01', '测试测试')
    # folders_info = client.list_folders('48a55528-9104-44c1-ba48-6c94b4593f01')
    # print(folders_info)
    # client.create_folder('48a55528-9104-44c1-ba48-6c94b4593f01', '测试测试2')

    # client.move_doc_to_folder('48a55528-9104-44c1-ba48-6c94b4593f01', 'J2EP8FWnE9h5ByPKC5qqH', folder_name='测试测试')

    # copied_doc_id = client.copy_doc('1a17a4e6-9a28-4c4f-b86f-a5c176e98c56', 'f96c3943-aaae-41bb-955a-5c44fda693f0', 'WzGLtHs6xIzT-6lUQCNa7')
