import os
import time
from typing import Callable

from app.ch.orm import ChClient
from app.client.openai_client import OpenAIClient
from app.client.s3client import S3Client
from app.config import VIDEO_S3_PATH
from app.db.common_type import TaskType
from app.db.models import TasksEntity
from app.logger import logger
from app.routers.common import get_user_path
from app.service import video_service, document_service
from app.service.disassemble_graph.disassemble_content_type import (
    ContentType,
    ContentPlatform,
)
from app.service.video_service import DeriveOptions
from app.utils import blocksuite_utils, tasks_utils
from app.utils.tasks_utils import range_updater


# 根据本地视频文件直接进行拆解，封装任务数据和结果
# TODO 调用本函数后，请将video_filepath文件删除
async def disassemble_video_by_file(
    video_filepath,
    task: TasksEntity,
    openai_client: OpenAIClient,
    content_type: ContentType = ContentType.短视频,
    content_platform: ContentPlatform = ContentPlatform.抖音,
    video_desc: str = "",
    updater: Callable[[float], None] = None,
    media_meta: dict = None,
    disassemble_usage: str = "bajie",  # 默认为bajie使用场景
):
    start = time.time() * 1000
    video_info, disassemble_json = await video_service.summarize_video_file(
        video_filepath,
        content_type,
        content_platform,
        video_desc,
        progress_updater=range_updater(updater, 0, 0.9),
        media_meta=media_meta,
        disassemble_usage=disassemble_usage,  # 传递使用场景参数
    )
    markdown_report = tasks_utils.convert_video_summarize_report_to_markdown(
        disassemble_json
    )
    affine_url = document_service.import_markdown_doc(
        markdown_report, user_id=task.user_id
    )
    s3 = S3Client()
    if updater:
        updater(0.95)
    task.frame_grid_path = ""
    task.subtitles = video_info.video_content_json
    task.report = disassemble_json
    task.affine_url = affine_url
    task.elapsed = int(time.time() * 1000 - start)
    task.cover_file_path = s3.put_disassemble_video_cover_image(
        video_filepath, get_user_path(task.user_id)
    )
    return task


# 根据video_id拆解视频，实现视频的复制、下载、拆解，封装任务数据和结果；
async def disassemble_by_video_id(
    video_id: str,
    task: TasksEntity,
    client: OpenAIClient,
    content_type: ContentType,
    content_platform: ContentPlatform,
    media_meta: dict = None,
    updater: Callable[[float], None] = None,
):
    chClient = ChClient()
    video = chClient.search_video_by_aweme_id(video_id)
    if not video:
        raise Exception(f"video not found: {video_id}")

    if updater:
        updater(0.1)
    s3 = S3Client()
    # 复制oss中的视频到用户拆解数据目录下
    origin_file_path = f"{VIDEO_S3_PATH}{video_id}.mp4"
    temp_file_path = task.svideo_path
    video_filepath = None
    try:
        s3.copy_from_crawler_to_user(
            origin_file_path, temp_file_path, get_user_path(task.user_id)
        )
        # 下载视频到本地目录用于拆解分析
        video_filepath = s3.download_obj(
            filepath=temp_file_path, user_path=get_user_path(task.user_id)
        )
        if not video_filepath:
            raise Exception(f"download video error: {video_id}")
        if updater:
            updater(0.2)
        # 拆解视频
        res = await disassemble_video_by_file(
            video_filepath,
            task,
            client,
            content_type,
            content_platform,
            video_desc=video.desc,
            media_meta=media_meta,
            updater=range_updater(updater, 0.2, 1.0),
        )
        if not res:
            raise Exception(f"disassemble video error: {video_id}")
        return res
    except Exception as e:
        raise e
    finally:
        if video_filepath:
            os.remove(video_filepath)


# 生成拆解视频的同款报告
async def derive_similar_report(
    origin_report: dict,
    derive_options: DeriveOptions,
    task: TasksEntity,
):
    start = time.time() * 1000
    report = await video_service.derive_similar_report(origin_report, derive_options)
    markdown_report = tasks_utils.convert_video_story_board_report_to_markdown(report)
    affine_url = document_service.import_markdown_doc(
        markdown_report, user_id=task.user_id
    )

    task.report = report
    task.affine_url = affine_url
    task.elapsed = int(time.time() * 1000 - start)
    return task


if __name__ == "__main__":

    # 调用示例,如使用video_id：
    video_id = "7367328210754833691"
    task_id = 1
    user_id = 19
    client = OpenAIClient()
    task = TasksEntity(
        task_id=task_id,
        task_type=TaskType.select,
        svideo_path="",
        frame_grid_path="",
        subtitles={},
        report={},
        elapsed=0,
        user_id=user_id,
    )
    # await disassemble_by_video_id(video_id, task, client)
    print(task)
