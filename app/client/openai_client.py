# type: ignore
from __future__ import annotations

import asyncio
import base64
import io
import json
import os
import random
import time
from abc import abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Optional, List, Tuple, Dict, Union, Any, Callable
import threading

import cv2
import numpy as np
from anthropic import AsyncAnthropic
from fastapi import BackgroundTasks
from langchain.prompts.chat import Chat<PERSON><PERSON>age<PERSON>romptTemplate
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langfuse.decorators import observe, langfuse_context
from langfuse.openai import OpenAI, AsyncOpenAI
from pydantic import BaseModel, Field

import app.utils.video_utils as video_utils
from app.ch.model import DateTimeEnumEncoder
from app.ch.orm import ChClient
from app.client.prompts import TEMPLATES, FUNCTIONS
from app.client.redis_client import (
    RedisClient,
    KEY_CHAT_ANSWER_MAP,
    KEY_CHAT_DOCS_MAP,
    KEY_CHAT_OPTION_MAP,
    <PERSON><PERSON><PERSON>_CHAT_STATE_MAP,
    <PERSON><PERSON><PERSON>_CHAT_VIDEO_MAP,
    <PERSON><PERSON><PERSON>_CHAT_VIDEO_SET,
    K<PERSON>Y_CHAT_VIDEO_AUTHOR_SET,
)
from app.config import (
    KNOWLEDGE_BASE_NAME,
    PRODUCT_KB_NAME,
    GLOBAL_CONF,
    STRUCTURED_DOUYIN_VIDEO_KB_NAME,
)
from app.db.common_type import (
    AssistantType,
    ContentType,
    TaskType,
    ChatStatus,
    TaskStatus,
    KnowledgeBaseType,
)
from app.db.repository import (
    knowledge_file_repository,
    chat_repository,
    task_repository,
)
from app.knowledge_base.kb_api import asearch_docs
from app.knowledge_base.kb_file.base import KnowledgeFile
from app.logger import logger, exception_logger
from app.service import document_service
from app.utils import (
    read_assistant_utils,
    tasks_utils,
    langfuse_utils,
)
from app.utils.async_utils import to_sync
from app.utils.common_utils import get_kb_name_by_user_id, extract_text_from_md_content
from app.utils.json_repair import loads
from app.utils.prometheus_metrics import inc_generate_product_report_counter
from app.utils.proxy_utils import get_httpx_client, get_async_httpx_client
from app.utils.tasks_utils import progress_onupdater

os.environ["ALIYUN_DASHSCOPE_API_KEY"] = os.getenv(
    "ALIYUN_DASHSCOPE_API_KEY", "sk-b2f437da107142bf95250e79dd10fc0a"
)
os.environ["ALIYUN_DASHSCOPE_BASE_URL"] = os.getenv(
    "ALIYUN_DASHSCOPE_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"
)
os.environ["ALIYUN_DASHSCOPE_MODEL_NAME"] = os.getenv(
    "ALIYUN_DASHSCOPE_MODEL_NAME", "qwen-long"
)

fast_llm = ChatOpenAI(
    model="gpt-4.1-nano",
    http_client=get_httpx_client(),
    http_async_client=get_async_httpx_client(),
)
long_context_llm = ChatOpenAI(
    model="gpt-4.1-mini",
    http_client=get_httpx_client(),
    http_async_client=get_async_httpx_client(),
)

expensive_llm = ChatOpenAI(
    model="gpt-4.1",
    http_client=get_httpx_client(),
    http_async_client=get_async_httpx_client(),
)


class AppendStringIO(io.StringIO):
    def append(self, s: str):
        self.write(s)


class GPTAnnotation:
    def __init__(
        self,
        description: str = "",
        keywords: List[str] = [],
        script: str = "",
        score: int = 0,
        highlights: str = "",
        hook: str = "",
        **kwargs,
    ):
        self.description = description
        self.keywords = keywords
        self.script = script
        self.score = score
        self.highlights = highlights
        self.hook = hook

    def __str__(self):
        keywords_str = ", ".join(self.keywords)
        return (
            f"GPTAnnotation:\n"
            f"  description: {self.description}\n"
            f"  keywords: {keywords_str}\n"
            f"  script: {self.script}\n"
            f"  score: {self.score}\n"
            f"  highlights: {self.highlights}\n"
            f"  hook: {self.hook}"
        )

    def __repr__(self):
        return self.__str__()

    @classmethod
    def from_json(cls, json_str):
        data = loads(json_str)
        return cls(**data)


class GPTCreatorAnnotation:
    """对创作者的总结与标注，即标注页面"根据创作者近期多个视频总结得出"的章节"""

    def __init__(
        self,
        topic_keywords: List[str] = [],
        eye_catching_points: str = "",
        opening_quality_score: int = 0,
        **kwargs,
    ):
        self.topic_keywords = topic_keywords
        self.eye_catching_points = eye_catching_points
        self.opening_quality_score = opening_quality_score

    def __str__(self):
        return (
            f"GPTCreatorAnnotation:\n"
            f"  topic_keywords: {self.topic_keywords}\n"
            f"  eye_catching_points: {self.eye_catching_points}\n"
            f"  opening_quality_score: {self.opening_quality_score}"
        )

    def __repr__(self):
        return self.__str__()

    @classmethod
    def from_json(cls, json_str):
        data = loads(json_str)
        return cls(**data)


class History(BaseModel):
    """
    对话历史
    可从dict生成，如
    h = History(**{"role":"user","content":"你好"})
    也可转换为tuple，如
    h.to_msy_tuple = ("human", "你好")
    """

    role: str = Field(...)
    content: str = Field(...)

    def to_msg_tuple(self):
        return "ai" if self.role == "assistant" else "human", self.content

    def to_msg_template(self, is_raw=True) -> ChatMessagePromptTemplate:
        role_maps = {
            "ai": "assistant",
            "human": "user",
        }
        role = role_maps.get(self.role, self.role)
        if is_raw:  # 当前默认历史消息都是没有input_variable的文本。
            content = "{% raw %}" + self.content + "{% endraw %}"
        else:
            content = self.content

        return ChatMessagePromptTemplate.from_template(
            content,
            "jinja2",
            role=role,
        )

    @classmethod
    def from_data(cls, h: Union[List, Tuple, Dict]) -> "History":
        if isinstance(h, (list, tuple)) and len(h) >= 2:
            h = cls(role=h[0], content=h[1])
        elif isinstance(h, dict):
            h = cls(**h)

        return h


# 聊天的进度管理器 (负责更新进度条、实时内容)
# update_interval_sec: 读取gpt流式输出的间隔，默认为0.2s
# updater_func: 进度更新函数，用于更新进度条及可能需要的content
# init_progress_value: 初始进度，默认为0.2
# max_progress_value: 完成时进度，默认为0.9
# progress_interval: 进度更新阶梯，默认为0.02
@dataclass
class ProgressArgs:
    update_interval_sec: float = 0.2
    updater_func: Callable[[float, str], None] = None
    init_progress_value: float = 0.2
    max_progress_value: float = 0.9
    progress_interval: float = 0.02


# 聊天返回结果处理器（负责 json格式化\业务转换）
# expect_json: 是否期望返回json结果，默认为False
# json_result_converter 允许提供业务回调函数，进行必要的业务处理; 入参：json格式数据，返回：转换后的数据和原始数据(str, dict)
@dataclass
class JsonConvertArgs:
    expect_json: bool = False
    json_result_converter: Callable[[dict], tuple] = None


# LLMArgs: 语言模型参数 (负责控制调用模型的参数)
# model: 模型名称
# stream: 是否流式调用，默认为True
# kwargs: 调用模型的其他可选参数，比如temperature, timeout等
@dataclass
class LLMArgs:
    model: str
    stream: bool
    kwargs: Dict[str, Any]

    def __init__(self, model: str, stream: bool, **kwargs):
        self.model = model
        self.stream = stream
        self.kwargs = kwargs


# MyBaseLLMClient: 语言模型客户端基类。 (负责定义open ai的调用接口，以及调用逻辑的实现)
# call：进行聊天的逻辑处理
# 调用gpt聊天统一使用异步client，因此这里的call方法也是异步的，使用者需要通过await来调用
class MyBaseLLMClient:
    def __init__(self, llm_args: LLMArgs):
        self.llm_args = llm_args

    @abstractmethod
    async def _call_client(self, *args, **kwargs):
        """这个方法由子类实现，使用者无需关心，之所以定义这样的抽象方法，是因为openai和claude的调用方法不一样"""
        pass

    @abstractmethod
    def _extract_content_from_non_stream_response(self, response) -> str:
        """这个方法由子类实现，使用者无需关心，之所以定义这样的抽象方法，是因为openai和claude的响应结构不一样"""
        pass

    async def __call__(
        self,
        messages: List,
        retry: int = 2,
        retry_interval_seconds: int = 1,
        json_convert_args: JsonConvertArgs = JsonConvertArgs(),
        progress_args: ProgressArgs = ProgressArgs(),
    ):
        """openai client的调用gpt函数"""
        for i in range(retry):
            if i != 0:
                await asyncio.sleep(retry_interval_seconds)
            try:
                response = await self._call_client(
                    model=self.llm_args.model,
                    messages=messages,
                    stream=self.llm_args.stream,
                    **self.llm_args.kwargs,
                )
                if not response:
                    logger.error(f"第{i+1}次调用GPT返回结果为空")
                    continue
                if self.llm_args.stream:
                    # 流式结果拼接处理，同时管理进度
                    full_content = io.StringIO()
                    next_chunk = time.time() + progress_args.update_interval_sec / 2
                    dirty = False
                    progress = progress_args.init_progress_value
                    async for chunk in response:
                        chunk_message = chunk.choices[
                            0
                        ].delta.content  # extract the message
                        if chunk_message is None:
                            break
                        full_content.write(chunk_message)
                        dirty = True
                        if time.time() < next_chunk:
                            continue
                        next_chunk = time.time() + progress_args.update_interval_sec
                        dirty = False
                        # 保存进度与片段输出
                        if progress_args.updater_func:
                            progress = min(
                                progress + progress_args.progress_interval,
                                progress_args.max_progress_value,
                            )
                            progress_args.updater_func(
                                progress, full_content.getvalue()
                            )
                    if dirty and progress_args.updater_func:
                        progress_args.updater_func(
                            progress_args.max_progress_value, full_content.getvalue()
                        )
                    chat_response = full_content.getvalue()
                else:
                    chat_response = self._extract_content_from_non_stream_response(
                        response
                    )
                logger.info(f"第{i+1}次调用GPT返回结果: {chat_response}")
                # expect_json时，进行json格式处理
                if json_convert_args.expect_json:
                    chat_response = loads(chat_response)
                    if not chat_response:
                        logger.warning(
                            f"第{i + 1}次调用GPT返回结果为空，但 expect_json 为 True，重试"
                        )
                        # 若gpt返回了异常数据，比如一些错误信息，或者被截断的json，则视为当前次调用无效，重来一次
                        continue
                # 结果业务转换(可选)
                if json_convert_args.json_result_converter:
                    convert_res = json_convert_args.json_result_converter(chat_response)
                    # 注意，这个场景下这里是返回的一个tuple(str, dict)，而不是直接返回dict，因为业务上可能两个结果都需要用到。
                    return convert_res
                return chat_response
            except Exception as e:
                logger.error(f"调用GPT报错，尝试重新调用，错误信息: {e}", exc_info=True)
                continue
        logger.error(f"调用GPT失败")
        return None


# MyOpenAiLLMClient: OpenAI语言模型客户端
# 定义聊天的函数实现，openai中定义为：chat.completions.create
class MyOpenAiLLMClient(MyBaseLLMClient):
    def __init__(self, openai_client: AsyncOpenAI, model: str, stream: bool, **kwargs):
        super().__init__(LLMArgs(model, stream, **kwargs))
        self.openai_client = openai_client

    async def _call_client(self, *args, **kwargs):
        return await self.openai_client.chat.completions.create(*args, **kwargs)

    def _extract_content_from_non_stream_response(self, response) -> str:
        return response.choices[0].message.content


class MyClaudeLLMClient(MyBaseLLMClient):
    def __init__(
        self, claude_client: AsyncAnthropic, model: str, stream: bool, **kwargs
    ):
        # claude 强制非流式
        super().__init__(LLMArgs(model, False, **kwargs))
        self.claude_client = claude_client

    @observe(as_type="generation", name="Claude")
    async def _call_client(self, *args, **kwargs):
        kwargs_clone = kwargs.copy()
        messages = kwargs_clone.pop("messages", None)
        model = kwargs_clone.pop("model", None)

        langfuse_context.update_current_observation(
            input=messages[:1],
            model=model,
            metadata=kwargs_clone,
        )

        response = await self.claude_client.messages.create(*args, **kwargs)

        langfuse_context.update_current_observation(
            usage={
                "input": response.usage.input_tokens,
                "output": response.usage.output_tokens,
            }
        )

        text = response.content[0].text
        # TODO：这个强制json的写法太诡异了
        if not text.startswith("{"):
            text = "{" + text
        return text

    def _extract_content_from_non_stream_response(self, response) -> str:
        return response


class OpenAIClient:
    def __init__(self, chclient=None):
        self.model_name = "gpt-4.1-mini"
        self.claude_model_name = "claude-3-5-sonnet-20240620"
        self.chclient = chclient if chclient is not None else ChClient()
        # 60s 超时，否则连单元测试都不通过
        OPENAI_CLIENT_TIMEOUT = 60
        ANTHROPIC_CLIENT_TIMEOUT = 180
        self.async_anthropic_client = AsyncAnthropic(
            api_key=GLOBAL_CONF.ANTHROPIC_API_KEY,
            base_url=GLOBAL_CONF.ANTHROPIC_BASE_URL,
            timeout=ANTHROPIC_CLIENT_TIMEOUT,
            http_client=get_async_httpx_client(timeout=ANTHROPIC_CLIENT_TIMEOUT),
        )
        self.client_chat_async = AsyncOpenAI(
            base_url=GLOBAL_CONF.OPENAI_API_BASE_URL,
            api_key=GLOBAL_CONF.OPENAI_API_KEY,
            timeout=OPENAI_CLIENT_TIMEOUT,
            http_client=get_async_httpx_client(timeout=OPENAI_CLIENT_TIMEOUT),
        )
        self.client_chat = OpenAI(
            base_url=GLOBAL_CONF.OPENAI_API_BASE_URL,
            api_key=GLOBAL_CONF.OPENAI_API_KEY,
            timeout=OPENAI_CLIENT_TIMEOUT,
            http_client=get_httpx_client(timeout=OPENAI_CLIENT_TIMEOUT),
        )
        self.client_annotate = OpenAI(
            base_url=GLOBAL_CONF.OPENAI_API_BASE_URL_ANNOTATE,
            api_key=GLOBAL_CONF.OPENAI_API_KEY_ANNOTATE,
            timeout=OPENAI_CLIENT_TIMEOUT,
            http_client=get_httpx_client(timeout=OPENAI_CLIENT_TIMEOUT),
        )
        self.client_read_async = AsyncOpenAI(
            base_url=GLOBAL_CONF.OPENAI_API_BASE_URL,
            api_key=GLOBAL_CONF.OPENAI_API_KEY,
            timeout=OPENAI_CLIENT_TIMEOUT,
            http_client=get_async_httpx_client(timeout=OPENAI_CLIENT_TIMEOUT),
        )
        self.client_read = OpenAI(
            base_url=GLOBAL_CONF.OPENAI_API_BASE_URL,
            api_key=GLOBAL_CONF.OPENAI_API_KEY,
            timeout=OPENAI_CLIENT_TIMEOUT,
            http_client=get_httpx_client(timeout=OPENAI_CLIENT_TIMEOUT),
        )
        self.read_model_name = "gpt-4.1-nano"

    def prepare_history_message(
        self,
        content: str,
        assistant: str,
        state: str,
        doc_list: str,
        history: Optional[List[History]] = [],
    ):
        # 首先在message中添加system角色的内容，再将用户历史加入
        messages = [
            {
                "role": "system",
                "content": TEMPLATES[assistant]["system"].format(doc_list=doc_list),
            }
        ]
        # TODO: 这里先只取最近10条历史，未来可能会根据实际情况调整
        for i in history[-10:]:
            messages.append({"content": i.content, "role": i.role})
        # 加入用户本次的query
        messages.append(
            {
                "role": "user",
                "content": TEMPLATES[assistant][state]["chat"].format(
                    user_content=content
                ),
            }
        )
        return messages

    async def collect_and_yield_chunks(
        self,
        response,
        chat_response: AppendStringIO,
        result: Dict[str, Any],
        yield_delta: bool = True,
    ):
        """
        收集GPT返回的内容，将content和tool_calls分别存入result中
        :param response: GPT返回的response
        :param chat_response: 用于存储返回的内容
        :param result: 用于存储tool_calls和collected_output
        """
        collected_content = AppendStringIO()
        tool_calls = []
        async for chunk in response:
            if chunk.choices[0].delta.content is not None:
                collected_content.append(chunk.choices[0].delta.content)
                chat_response.append(chunk.choices[0].delta.content)
                yield (
                    self.replace_special_chars(chunk.choices[0].delta.content)
                    if yield_delta
                    else chat_response.getvalue()
                )
            # 如果不需要调用函数，这里担心前端需要用最后一个None来判断结束
            elif (
                chunk.choices[0].delta.content is None
                and chunk.choices[0].finish_reason == "stop"
            ):
                # chat_response.append(chunk.choices[0].delta.content)
                if yield_delta:
                    yield self.replace_special_chars(chunk.choices[0].delta.content)
            elif chunk.choices[0].delta.tool_calls is not None:
                if chunk.choices[0].delta.tool_calls:
                    if chunk.choices[0].delta.tool_calls[0].function.name is not None:
                        tool_calls.append(chunk.choices[0].delta.tool_calls[0])
                    else:
                        tool_calls[-1].function.arguments += (
                            chunk.choices[0].delta.tool_calls[0].function.arguments
                        )
        result["tool_calls"] = tool_calls
        result["collected_content"] = collected_content.getvalue()

    async def generate_options(
        self,
        messages: list,
        assistant=AssistantType.general,
        conversation_id="",
        user_id=0,
        tag="",
    ):
        additional_prompt = {
            "role": "user",
            "content": TEMPLATES["option"].get(
                assistant.value, TEMPLATES["option"]["default"]
            ),
        }
        messages.append(additional_prompt)
        open_ai_client = MyOpenAiLLMClient(
            self.client_chat_async,
            model=self.model_name,
            stream=False,
            session_id=conversation_id,
            user_id=str(user_id),
            tags=[tag],
        )
        option_response = await open_ai_client(messages=messages)
        options = eval(option_response)
        return options

    async def generate_state(self, messages, state):
        if state != "default":
            additional_prompt = {
                "role": "user",
                "content": TEMPLATES["state"]["default"],
            }
            open_ai_client = MyOpenAiLLMClient(
                self.client_chat_async,
                model=self.model_name,
                stream=False,
            )
            next_state = await open_ai_client(messages=messages + [additional_prompt])
        else:
            next_state = state
        return next_state.strip()

    def update_maps(
        self, conversation_id, chat_response: str, videos, docs, options, next_state
    ):
        redis_client = RedisClient().get_client()
        redis_client.hset(
            KEY_CHAT_ANSWER_MAP, conversation_id, chat_response if chat_response else ""
        )
        if len(videos) > 0:
            redis_client.hset(KEY_CHAT_VIDEO_MAP, conversation_id, json.dumps(videos))
        if len(docs) > 0:
            redis_client.hset(KEY_CHAT_DOCS_MAP, conversation_id, json.dumps(docs))
        redis_client.hset(KEY_CHAT_OPTION_MAP, conversation_id, json.dumps(options))
        redis_client.hset(KEY_CHAT_STATE_MAP, conversation_id, next_state)

    def update_non_stream_result(
        self, non_stream_result, chat_response: str, videos, docs, options, next_state
    ):
        non_stream_result["chat_response"] = chat_response
        non_stream_result["videos"] = videos
        non_stream_result["docs"] = docs
        non_stream_result["options"] = options
        non_stream_result["state"] = next_state

    async def auto_chat(
        self,
        content: str,
        assistant: AssistantType = AssistantType.general,
        state: str = "default",
        history: Optional[List[History]] = [],
        search_internet=True,
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
        stream: bool = True,
        yield_delta: bool = True,
        non_stream_result: Optional[Dict[str, Any]] = None,
        conversation_id="",
        user_id=0,
        tag="",
        file_ids=[],
        background_tasks: BackgroundTasks = None,
        chat_id: str = "",
    ):
        logger.info("调用auto_chat")
        logger.info(
            {
                "content": content,
                "history": history,
                "assistant": assistant.value,
                "state": state,
                "stream": stream,
                "search_internet": search_internet,
                "knowledge_base_name": knowledge_base_name,
                "conversation_id": conversation_id,
                "non_stream_result": non_stream_result,
            }
        )
        # 如果用户@流联，则调用通用聊天助手，且不查询互联网信息
        if "@流联" in content and assistant == AssistantType.document:
            assistant = AssistantType.general
            knowledge_base_name = KNOWLEDGE_BASE_NAME
            search_internet = True
            logger.info(
                f"@流联 对话, assistant调整为{assistant}, knowledge_base_name 调整为{knowledge_base_name}, search_internet: {search_internet}"
            )
        videos, docs = [], []
        chat_response = AppendStringIO()

        # 首先在message中添加system角色的内容，再将用户历史加入
        doc_info_list = (
            knowledge_file_repository.list_file_info_from_db(knowledge_base_name)
            if assistant == AssistantType.document
            else None
        )

        messages = self.prepare_history_message(
            content=content,
            history=history,
            assistant=assistant.value,
            state=state,
            doc_list=(
                "\n".join(f"{doc.file_id}  {doc.file_name}" for doc in doc_info_list)
                if doc_info_list
                else ""
            ),
        )

        # 进行第一次GPT调用，根据search_internet来决定是否需要调用函数
        if not search_internet:
            logger.info("不需要查询互联网信息")
            first_response = await self.client_chat_async.chat.completions.create(
                model=self.model_name,
                messages=messages,
                stream=stream,
                session_id=conversation_id,
                user_id=str(user_id),
                tags=[tag],
            )
        else:
            tools = [v for v in FUNCTIONS.values()]
            if assistant == AssistantType.video:
                tools = [
                    FUNCTIONS["get_hot_video_by_keyword"],
                    FUNCTIONS["get_marketing_video"],
                ]
            elif assistant == AssistantType.document:
                tools = [
                    FUNCTIONS["search_knowledge_base"],
                    FUNCTIONS["get_marketing_video"],
                    FUNCTIONS["read_one_book"],
                ]
            elif assistant in [AssistantType.general, AssistantType.planner]:
                tools = [
                    FUNCTIONS["get_hot_video_by_keyword"],
                    FUNCTIONS["search_knowledge_base"],
                    FUNCTIONS["get_marketing_video"],
                ]
            elif (
                assistant == AssistantType.product
            ):  # 产品助手会话，有可能需要调用函数生成产品报告
                tools = [
                    FUNCTIONS["search_knowledge_base"],
                    FUNCTIONS["search_and_generate_product_report"],
                ]
            first_response = await self.client_chat_async.chat.completions.create(
                model=self.model_name,
                messages=messages,
                tools=tools,
                tool_choice="required",
                stream=stream,
                session_id=conversation_id,
                user_id=str(user_id),
                tags=[tag],
            )
        if stream:
            result = {}
            async for item in self.collect_and_yield_chunks(
                first_response, chat_response, result, yield_delta
            ):
                yield item
            tool_calls = result["tool_calls"]
        else:
            if first_response.choices[0].message.content:
                chat_response.append(first_response.choices[0].message.content)
            tool_calls = first_response.choices[0].message.tool_calls

        # 根据是否需要调用函数来决定是否需要进行第二次GPT调用
        if not tool_calls:
            logger.info("GPT没有选择调用函数")
            if stream:
                messages.append(
                    {"content": result["collected_content"], "role": "assistant"}
                )
            else:
                messages.append(
                    {
                        "content": first_response.choices[0].message.content,
                        "role": "assistant",
                    }
                )
        elif "read_one_book" in [tool_call.function.name for tool_call in tool_calls]:
            logger.info(
                "需要调用read_one_book，总结一本书作为输出，直接忽略其他函数调用"
            )
            tool_call = None
            for call in tool_calls:
                if call.function.name == "read_one_book":
                    tool_call = call
            function_args = json.loads(tool_call.function.arguments)
            # TODO(lxc): 目前还没有遇到过这种情况，如有遇到再具体分析，考虑一些默认值
            if (
                len(function_args) != 4
                or "file_name" not in function_args
                or "file_id" not in function_args
                or "book_type" not in function_args
                or "result_type" not in function_args
            ):
                logger.error("GPT调用read_one_book时参数出错: {}".format(function_args))
                chat_response.append("总结失败，请稍后再试")
                messages.append(
                    {"content": "总结失败，请稍后再试", "role": "assistant"}
                )
            # 如果需要生成读书报告，那么创建后台任务，提前结束auto_chat
            elif function_args["result_type"] == "report":
                logger.info(
                    "使用后台任务调用generate_book_report, function_args为{}".format(
                        function_args
                    )
                )
                filename = function_args["file_name"]
                file_id = function_args["file_id"]
                logger.info(
                    f"kb {knowledge_base_name}, filename {filename}, file_id {file_id}, user_id {user_id}, conversation_id {conversation_id}"
                )
                background_tasks.add_task(
                    to_sync(self.generate_book_report),
                    file_name=function_args["file_name"],
                    file_id=function_args["file_id"],
                    book_type=function_args["book_type"],
                    result_type=function_args["result_type"],
                    doc_info_list=doc_info_list,
                    knowledge_base_name=knowledge_base_name,
                    chat_id=chat_id,
                    user_id=user_id,
                    conversation_id=conversation_id,
                )
                if non_stream_result is not None:  # 防止chat.py中使用出错
                    self.update_non_stream_result(
                        non_stream_result=non_stream_result,
                        chat_response="",
                        videos=[],
                        docs=[],
                        options=[],
                        next_state="default",
                    )
                return  # 提前结束
            else:
                logger.info(
                    "调用{}, 参数为{}".format(tool_call.function.name, function_args)
                )
                read_result = {}
                async for item in self.read_one_book(
                    file_name=function_args["file_name"],
                    file_id=function_args["file_id"],
                    book_type=function_args["book_type"],
                    result_type=function_args["result_type"],
                    knowledge_base_name=knowledge_base_name,
                    doc_info_list=doc_info_list,
                    chat_response=chat_response,
                    yield_delta=yield_delta,
                    read_result=read_result,
                ):
                    if stream:
                        yield item
                messages.append(
                    {"content": read_result["collected_content"], "role": "assistant"}
                )
        elif "search_and_generate_product_report" in [
            tool_call.function.name for tool_call in tool_calls
        ]:
            # 1.先搜索支持库
            tool_call = None
            for call in tool_calls:
                if call.function.name == "search_and_generate_product_report":
                    tool_call = call
            function_args = json.loads(tool_call.function.arguments)
            if len(function_args) != 1 or "input" not in function_args:
                logger.error(
                    "GPT调用search_and_generate_product_report时参数出错: {}".format(
                        function_args
                    )
                )
                chat_response.append("总结失败，请稍后再试")
                messages.append(
                    {"content": "总结失败，请稍后再试", "role": "assistant"}
                )
            _, docs = await self.search_knowledge_base(
                input=function_args["input"],
                knowledge_base_name=knowledge_base_name,
                user_id=user_id,
                file_ids=file_ids,
            )
            # 2. 创建生成产品报告任务
            logger.info("使用后台任务调用generate_product_report")
            background_tasks.add_task(
                to_sync(self.generate_product_report),
                docs=docs,
                chat_id=chat_id,
                user_id=user_id,
                conversation_id=conversation_id,
            )
            return  # 提前结束
        else:
            if stream:
                messages.append(
                    {
                        "content": result["collected_content"],
                        "role": "assistant",
                        "function_call": None,
                        "tool_calls": tool_calls,
                    }
                )
            else:
                messages.append(first_response.choices[0].message)
            messages, videos, docs = await self.call_function_tools(
                messages,
                tool_calls,
                knowledge_base_name,
                user_id=user_id,
                tag=tag,
                conv_id=conversation_id,
                file_ids=file_ids,
            )
            logger.info(
                f"调用{tool_calls}, 得到docs数量：{len(docs) if docs else 0}, videos数量：{len(videos) if videos else 0}"
            )

            if (
                "get_hot_video_by_keyword"
                in [tool_call.function.name for tool_call in tool_calls]
                and not videos
            ):
                logger.error("get_hot_video_by_keyword没有搜索到视频")

            if (
                "get_marketing_video"
                in [tool_call.function.name for tool_call in tool_calls]
                and not videos
            ):
                logger.error("get_marketing_video没有搜索到视频")

            open_ai_client = MyOpenAiLLMClient(
                self.client_chat_async,
                self.model_name,
                stream=stream,
                session_id=conversation_id,
                user_id=str(user_id),
                tags=[tag],
            )

            second_response = await open_ai_client(
                messages=messages,
                retry=2,
                json_convert_args=JsonConvertArgs(expect_json=False),
                progress_args=ProgressArgs(
                    update_interval_sec=0.02,
                    updater_func=progress_onupdater(
                        user_id, conversation_id, chat_id, True
                    ),
                    init_progress_value=0.1,
                    max_progress_value=0.99,
                    progress_interval=0.002,
                ),
            )

            if chat_response:  # 如果第一次有回答内容，让格式好看点
                chat_response.append("\n\n")
                if stream and yield_delta:
                    yield self.replace_special_chars("\n\n")
                else:
                    pass
            chat_response.append(second_response)
            messages.append({"content": second_response, "role": "assistant"})

        # 输出一下messages
        logger.info("messages:")
        for message in messages:
            logger.info(message)

        # 生成给用户的一系列选项
        options = await self.generate_options(
            messages, assistant, conversation_id, user_id, tag
        )
        # 生成对话的状态
        next_state = await self.generate_state(messages, state)

        # 将videos和docs转换为dict
        video_dicts = [
            json.loads(json.dumps(v.__dict__, cls=DateTimeEnumEncoder)) for v in videos
        ]
        docs_dicts = [
            json.loads(json.dumps(d.__dict__, cls=DateTimeEnumEncoder)) for d in docs
        ]
        # 更新一系列map
        if non_stream_result is not None:
            self.update_non_stream_result(
                non_stream_result=non_stream_result,
                chat_response=chat_response.getvalue(),
                videos=video_dicts,
                docs=docs_dicts,
                options=options,
                next_state=next_state,
            )
        if stream and conversation_id != "":
            self.update_maps(
                conversation_id=conversation_id,
                chat_response=chat_response.getvalue(),
                videos=video_dicts,
                docs=docs_dicts,
                options=options,
                next_state=next_state,
            )
            if yield_delta:
                yield None

    async def planning_chat(
        self,
        content: str,
        history: Optional[List[History]],
        interval: float = 0.2,
        updater: Callable[[float, str], None] = None,
    ):
        messages = self.prepare_history_message(
            content=content,
            history=history,
            assistant=AssistantType.planner,
            state="default",
            doc_list=[],
        )

        open_ai_client = MyOpenAiLLMClient(
            self.client_chat_async,
            model=self.model_name,
            stream=True,
            temperature=0,
        )
        response = await open_ai_client(
            messages=messages,
            progress_args=ProgressArgs(updater_func=updater),
        )
        logger.info("planning_chat response: {}".format(response))

    async def call_function_tools(
        self,
        messages,
        tool_calls,
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
        user_id=0,
        tag="",
        conv_id="",
        file_ids=[],
    ):
        available_functions = {
            "get_hot_video_by_keyword": self.get_hot_video_by_keyword,
            "search_knowledge_base": self.search_knowledge_base,
            "get_marketing_video": self.get_marketing_video,
            "read_one_book": None,  # 在外面单独处理
            "generate_product_report": None,  # 由于需要创建异步任务，在外面单独处理
        }
        video_search_flag = False
        videos = []
        docs = []
        doc_i, video_i = 0, 0
        for tool_call in tool_calls:
            if tool_call.function.name not in available_functions:
                messages.append(
                    {
                        "tool_call_id": tool_call.id,
                        "role": "tool",
                        "name": tool_call.function.name,
                        "content": "调用出错，未找到对应函数",
                    }
                )
            if tool_call.function.name == "get_hot_video_by_keyword":
                # TODO：如果已经搜索过，那么不再搜索，之后优化
                function_args = json.loads(tool_call.function.arguments)
                if video_search_flag:
                    logger.info(
                        "get_hot_video_by_keyword video_search_flag=true 已经搜索过, 不再执行\n 调用{}, 参数为{}".format(
                            tool_call.function.name, function_args
                        )
                    )
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": "",
                        }
                    )
                    continue
                logger.info(
                    "调用{}, 参数为{}".format(tool_call.function.name, function_args)
                )
                try:
                    r_videos_str, r_videos = await available_functions[
                        tool_call.function.name
                    ](**function_args, conv_id=conv_id)
                    logger.info(
                        "调用{}成功: {}".format(tool_call.function.name, r_videos_str)
                    )
                    videos += r_videos
                    video_content = ""
                    for video in r_videos:
                        video_content += "\n****相关视频START({})****\n".format(
                            video_i + 1
                        )
                        video_content += "\n视频描述: \n" + video.desc + "\n"
                        video_content += (
                            "\n视频内容(大模型总结): \n" + video.content + "\n"
                        )
                        video_content += "\n视频亮点: \n" + video.mark_highlights + "\n"
                        if video.mark_hook != None and video.mark_hook != "":
                            video_content += (
                                "\n视频关键钩子: \n" + video.mark_hook + "\n"
                            )
                        if video.tag != [] and len(video.tag) > 0:
                            video_content += (
                                "\n视频类型： \n" + "、".join(video.tag) + "\n"
                            )
                        video_content += (
                            "\n视频作者创作开头亮点（由作者近期5个视频总结得出）: \n"
                            + video.author_eye_catching_points
                            + "\n"
                        )
                        video_content += (
                            "\n视频作者作品开头质量评分（由作者近期5个视频总结得出）: \n"
                            + str(video.author_opening_quality_score)
                            + "\n"
                        )
                        video_content += "\n****相关视频END({})****\n".format(
                            video_i + 1
                        )
                        video_i += 1
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": video_content,
                        }
                    )
                    video_search_flag = True
                except Exception as e:
                    logger.error("调用{}出错，{}".format(tool_call.function.name, e))
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": "调用出错，{}".format(e),
                        }
                    )
            if tool_call.function.name == "search_knowledge_base":
                function_args = json.loads(tool_call.function.arguments)
                logger.info(
                    "调用{}, 参数为{}".format(tool_call.function.name, function_args)
                )
                try:
                    kb_content, r_docs = await available_functions[
                        tool_call.function.name
                    ](
                        **function_args,
                        knowledge_base_name=knowledge_base_name,
                        user_id=user_id,
                        tag=tag,
                        file_ids=file_ids,
                    )
                    docs += r_docs
                    doc_content = ""
                    for doc in r_docs:
                        doc_content += "\n****相关书籍节选START({})****\n".format(
                            doc_i + 1
                        )
                        doc_content += "\n引用id: \n" + str(doc_i + 1) + "\n"
                        doc_content += "\n书籍名称: \n" + doc.filename + "\n"
                        # 针对产品助手，需要移除page_content中的图标标签（affine文档元素，暂时无法直接展示）
                        # 这里需要两次提取，因为有些标签可能会rag换行截断，第一次将换行拼接后再提取
                        if knowledge_base_name == PRODUCT_KB_NAME:
                            doc.page_content = extract_text_from_md_content(
                                extract_text_from_md_content(doc.page_content)
                            )
                        doc_content += "\n节选内容: \n" + doc.page_content + "\n"
                        doc_content += "\n****相关书籍节选END({})****\n".format(
                            doc_i + 1
                        )
                        doc_i += 1
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": doc_content,
                        }
                    )
                except Exception as e:
                    logger.error("调用{}出错，{}".format(tool_call.function.name, e))
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": "调用出错，{}".format(e),
                        }
                    )
            if tool_call.function.name == "get_marketing_video":
                if video_search_flag:
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": "",
                        }
                    )
                    continue
                function_args = {}
                logger.info(
                    "调用{}, 参数为{}".format(tool_call.function.name, function_args)
                )
                try:
                    _, r_videos = await available_functions[tool_call.function.name](
                        **function_args, conv_id=conv_id
                    )
                    videos += r_videos
                    video_content = ""
                    for video in r_videos:
                        video_content += "\n****相关视频START({})****\n".format(
                            video_i + 1
                        )
                        video_content += "\n视频描述: \n" + video.desc + "\n"
                        if video.content and len(video.content) > 0:
                            video_content += (
                                "\n视频内容(大模型总结): \n" + video.content + "\n"
                            )
                        if video.mark_highlights and len(video.mark_highlights) > 0:
                            video_content += (
                                "\n视频亮点: \n" + video.mark_highlights + "\n"
                            )
                        if (
                            video.author_eye_catching_points
                            and video.author_opening_quality_score
                        ):
                            video_content += (
                                "\n视频作者创作开头亮点（由作者近期5个视频总结得出）: \n"
                                + video.author_eye_catching_points
                                + "\n"
                            )
                            video_content += (
                                "\n视频作者作品开头质量评分（由作者近期5个视频总结得出）: \n"
                                + str(video.author_opening_quality_score)
                                + "\n"
                            )
                        video_content += "\n****相关视频END({})****\n".format(
                            video_i + 1
                        )
                        video_i += 1
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": video_content,
                        }
                    )
                    video_search_flag = True
                except Exception as e:
                    logger.error("调用{}出错，{}".format(tool_call.function.name, e))
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": tool_call.function.name,
                            "content": "调用出错，{}".format(e),
                        }
                    )

        return messages, videos, docs

    # 前端那边\n有时候会有问题，所以做一些处理
    @staticmethod
    def replace_special_chars(content):
        if not isinstance(content, str):
            return content
        else:
            content = content.replace("\n", "|~回车~|")
            return content

    async def get_hot_video_by_keyword(
        self,
        input,
        conv_id,
        max_cnt=3,
        tags: list[str] = [],
        author_name: str = "",
        intention: int = 0,
        category: list[str] = [],
        sorts: list[str] = [],
        source: str = "",
        kb_name=STRUCTURED_DOUYIN_VIDEO_KB_NAME,
    ):
        """
        从kb_api (ES) 中搜索，然后去 video mark 表拿元数据。拼接后给LLM作为视频描述（按银浩旧标注结构拼接，对于新数据非常残缺！）
        此外，返回的第一个参数是字符串（原来给LLM的），但似乎调用方都不再使用了，而是在外部重新拼接的，而且拼接得也不对（按旧的格式拼接的）。
        """
        logger.info(f"get_hot_video_by_keyword: input={input}, conv_id={conv_id}")

        docs = await asearch_docs(
            input,
            knowledge_base_name=kb_name,
            kb_type=KnowledgeBaseType.video,
            top_k=int(max_cnt * 2),  # 多返回一些，避免筛选后数量不足
            score_threshold=0.1,
            tag=tags,
            author_name=author_name,
            intention=intention,
            category=category,
            sorts=sorts,
            source=source,
        )
        m = {}
        for doc in docs:
            m[doc.metadata["aweme_id"]] = {
                "score": doc.score,
                "frame_id": doc.metadata["frame_id"],
                "frame_oss_url": doc.metadata["frame_oss_url"],
                "disassemble": doc.page_content,
            }

        result = self.chclient.search_mark_video_by_aweme_ids(m.keys())
        result = sorted(
            result, key=lambda x: m.get(x.aweme_id, {}).get("score", 0), reverse=True
        )
        logger.info(
            f"get_hot_video_by_keyword chclient rerturn: result length {len(result)}"
        )

        count = 0
        videos = []
        video_disassembles = []
        redis_client = RedisClient().get_client()
        videos_key = f"{KEY_CHAT_VIDEO_SET}_{conv_id}"
        authors_key = f"{KEY_CHAT_VIDEO_AUTHOR_SET}_{conv_id}"
        if redis_client.scard(videos_key) > 20:
            redis_client.delete(videos_key)
            redis_client.delete(authors_key)
        for item in result:
            if item.aweme_id and redis_client.sismember(videos_key, item.aweme_id):
                continue
            """
                由于存在根据作者搜索视频的问题，这段代码先注释掉处理
            if item.author_sec_uid and redis_client.sismember(
                authors_key, item.author_sec_uid
            ):
                continue
            """

            item.cover_url = m[item.aweme_id]["frame_oss_url"]
            item.cover_oss_url = m[item.aweme_id]["frame_oss_url"]
            item.distance = -m[item.aweme_id]["score"]

            videos.append(item)
            video_disassembles.append(m[item.aweme_id]["disassemble"])
            if item.aweme_id:
                redis_client.sadd(videos_key, item.aweme_id)
            """
            if item.author_sec_uid:
                redis_client.sadd(authors_key, item.author_sec_uid)
            """
            count += 1
            # 数量足够了
            if count == max_cnt:
                break
        # 筛选后的视频数量已经达不到要求了，清理下自己的去重缓存，避免之后无视频可返回
        if len(videos) < max_cnt:
            redis_client.delete(videos_key)
            redis_client.delete(authors_key)
        return video_disassembles, videos

    async def search_knowledge_base(
        self,
        input,
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
        user_id=0,
        tag="",
        file_ids=[],
    ):
        logger.info(
            f"DEBUG_search_knowledge_base: input={input}, knowledge_base_name={knowledge_base_name}, user_id={user_id}"
        )
        try:
            from app.knowledge_base.kb_api import asearch_docs, asearch_docs_by_multi_kb
            from app.config import (
                SCORE_THRESHOLD,
                VECTOR_SEARCH_TOP_K,
                REPORT_SEARCH_TOP_K,
            )

            logger.info(
                f"search_knowledge_base: input={input}, knowledge_base_name={knowledge_base_name}, user_id={user_id}, tag={tag}, file_ids={file_ids}, SCORE_THRESHOLD={SCORE_THRESHOLD}, VECTOR_SEARCH_TOP_K={VECTOR_SEARCH_TOP_K}"
            )
            docs = await asearch_docs(
                query=input,
                knowledge_base_name=knowledge_base_name,
                top_k=(
                    REPORT_SEARCH_TOP_K
                    if knowledge_base_name == PRODUCT_KB_NAME
                    else VECTOR_SEARCH_TOP_K
                ),
                score_threshold=SCORE_THRESHOLD,
                tag=tag,
                file_ids=file_ids,
            )
            if knowledge_base_name == KNOWLEDGE_BASE_NAME:
                # TODO(kevin): remove this hack, chat with temp kb
                # 需要加上用户的知识库、还有报告库
                user_kb = get_kb_name_by_user_id(user_id)
                # TODO 先回退到只查询用户知识库
                # kbs = OFFICIAL_SUPPORT_KBS
                # kbs.append(user_kb)
                # user_docs = await asearch_docs_by_multi_kb(
                #     query=input,
                #     knowledge_base_names=kbs,
                #     score_threshold=SCORE_THRESHOLD,
                #     tag=tag,
                #     file_ids=file_ids,
                # )
                # docs.extend(user_docs)
                # docs = sorted(docs, key=lambda x: x.score, reverse=True)[
                #     :VECTOR_SEARCH_TOP_K
                # ]
                user_docs = await asearch_docs(
                    query=input,
                    knowledge_base_name=user_kb,
                    top_k=VECTOR_SEARCH_TOP_K,
                    score_threshold=SCORE_THRESHOLD,
                    tag=tag,
                    file_ids=file_ids,
                )
                # TODO(kevin): order this list
                docs.extend(user_docs)
            context = "\n".join([doc.page_content for doc in docs])
            return context, docs
        except:
            return "没有在知识库中查询到相关内容", []

    async def get_marketing_video(self, conv_id):
        logger.info(f"get_marketing_video: conv_id={conv_id}")
        max_cnt = 3

        result = self.chclient.search_marketing_video(limit=100, allow_artificial=True)
        logger.info(f"get_marketing_video chclient rerturn: result={result}")
        random.shuffle(result)

        video_str = "以下是一些有关" + "营销" + "的热门视频的视频内容: \n"
        count = 0
        videos = []
        redis_client = RedisClient().get_client()
        videos_key = f"{KEY_CHAT_VIDEO_SET}_{conv_id}"
        authors_key = f"{KEY_CHAT_VIDEO_AUTHOR_SET}_{conv_id}"
        if redis_client.scard(videos_key) > 100:
            redis_client.delete(videos_key)
            redis_client.delete(authors_key)
        for item in result:
            if item.aweme_id and redis_client.sismember(videos_key, item.aweme_id):
                continue
            if item.author_sec_uid and redis_client.sismember(
                authors_key, item.author_sec_uid
            ):
                continue
            video_str = (
                video_str + str(count) + ". " + item.desc + "\n" + item.content + "\n"
            )
            videos.append(item)
            if item.aweme_id:
                redis_client.sadd(videos_key, item.aweme_id)
            if item.author_sec_uid:
                redis_client.sadd(authors_key, item.author_sec_uid)
            count += 1
            # 数量足够了
            if count == max_cnt:
                break
        # 筛选后的视频数量已经达不到要求了，清理下自己的去重缓存，避免之后无视频可返回
        if len(videos) < max_cnt:
            redis_client.delete(videos_key)
            redis_client.delete(authors_key)
        return video_str, videos

    def annotate_video(
        self,
        video_desc: str,
        video_content: str,
        video_file: str,
        verbose: bool = False,
    ) -> GPTAnnotation:
        # 拼成的图片包含多少帧，以及图片的最大尺寸
        num_per_row, num_per_col = 8, 8
        num_per_image = num_per_row * num_per_col
        max_width, max_height = 1536, 1536

        # 通过视频的宽高以及输入给GPT的图片宽高，计算出图片resize后的宽高
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            if verbose:
                logger.info("无法打开视频文件")
            return None
        w = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
        h = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
        if h / max_height > w / max_width:
            resize_h = int(max_height / num_per_col)
            resize_w = int(w / h * resize_h)
        else:
            resize_w = int(max_width / num_per_row)
            resize_h = int(h / w * resize_w)

        # 根据视频的长度来确定视频取帧的频率
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration_seconds = frame_count / fps
        if duration_seconds <= 30:
            frame_interval = 0.5
        elif duration_seconds <= 180:
            frame_interval = min(1, duration_seconds / 60)
        else:
            frame_interval = duration_seconds / 180
        if verbose:
            logger.info("视频时长为{}秒".format(int(duration_seconds)))
            logger.info("每{:.02f}秒取1帧".format(frame_interval))
        cap.release()

        # 读取视频图片，存入images中
        images = []
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            if verbose:
                logger.info("无法打开视频文件")
        else:
            fps = cap.get(cv2.CAP_PROP_FPS)
            interval = int(fps * frame_interval)  # 每多少帧取一帧
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            for i in range(0, frame_count, interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                if not ret:
                    break
                resized_frame = cv2.resize(frame, (resize_w, resize_h))
                images.append(resized_frame)
            cap.release()
        if verbose:
            logger.info(f"共提取了 {len(images)} 帧")

        # 把小图拼成大图
        combined_images = []
        for i in range((len(images) + num_per_image - 1) // num_per_image):
            image_w, image_h = resize_w * num_per_row, resize_h * num_per_col
            combined_image = np.zeros((image_h, image_w, 3)).astype("uint8")
            for j, img in enumerate(
                images[i * num_per_image : i * num_per_image + num_per_image]
            ):
                row, col = j // num_per_row, j % num_per_col
                combined_image[
                    row * resize_h : (row + 1) * resize_h,
                    col * resize_w : (col + 1) * resize_w,
                ] = img
            combined_images.append(combined_image)
        if verbose:
            logger.info(f"共拼成了 {len(combined_images)} 张大图")

        # 构建GPT的messages
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": TEMPLATES["annotation"]["default"].format(
                            frame_interval=frame_interval,
                            video_desc=video_desc,
                            video_content=video_content,
                        ),
                    }
                ],
            }
        ]
        if verbose:
            logger.info(f"message:\n{messages[0]['content'][0]['text']}")

        def encode_image(image_in):
            _, buffer = cv2.imencode(".jpg", image_in)
            return base64.b64encode(buffer).decode("utf-8")

        for image in combined_images:
            messages[0]["content"].append(
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encode_image(image)}"
                    },
                }
            )

        # 调用大模型，防止大模型不按照要求输出内容，会尝试两遍
        for i in range(2):
            response = self.client_annotate.chat.completions.create(
                model=GLOBAL_CONF.OPENAI_API_MODEL_NAME_ANNOTATE,
                messages=messages,
            )
            if verbose:
                logger.info(f"GPT返回的结果为: {response.choices[0].message.content}")
            try:
                s = response.choices[0].message.content
                d = loads(s)  # 防止GPT返回结果在```中
                annotation = GPTAnnotation(**d)
                return annotation
            except Exception as e:
                logger.error(
                    f"解析GPT返回的结果出错: {e}, 尝试重新调用GPT 返回结果: {response.choices[0].message.content}"
                )
                continue
        return None

    def annotate_creator(
        self, videos_info: List[Dict[str, str]], verbose: bool = True
    ) -> GPTCreatorAnnotation:
        """
        对某位抖音创作者的多个视频进行分析标注

        :param videos_info: 包含多个视频信息的列表，每个视频信息是一个字典，包含 'desc', 'content' 和 'video_local_path' 键。
        :param verbose: 是否输出日志
        """
        video_images = []
        for video_info in videos_info:
            # 拼成的图片包含多少帧，以及图片的最大尺寸
            num_per_row, num_per_col = 6, 6
            num_per_image = num_per_row * num_per_col
            max_width, max_height = 1536, 1536

            # 通过视频的宽高以及输入给GPT的图片宽高，计算出图片resize后的宽高
            cap = cv2.VideoCapture(video_info["video_local_path"])
            if not cap.isOpened():
                if verbose:
                    logger.info(
                        "无法打开视频文件{}".format(video_info["video_local_path"])
                    )
                    return None
            w = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            h = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            if h / max_height > w / max_width:
                resize_h = int(max_height / num_per_col)
                resize_w = int(w / h * resize_h)
            else:
                resize_w = int(max_width / num_per_row)
                resize_h = int(h / w * resize_w)

            # 根据视频的长度来确定视频取帧的频率
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = min(int(fps * 10), frame_count)
            duration_seconds = frame_count / fps
            num_images = num_per_row * num_per_col
            interval = frame_count // num_images + 1
            frame_interval = interval / fps
            video_info["frame_interval"] = frame_interval
            if verbose:
                logger.info("视频时长为{}秒".format(int(duration_seconds)))
                logger.info("每{:.02f}秒取1帧".format(frame_interval))
            # 读取视频图片，存入images中
            images = []
            fps = cap.get(cv2.CAP_PROP_FPS)
            interval = int(fps * frame_interval)  # 每多少帧取一帧
            for i in range(0, frame_count, interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                if not ret:
                    break
                resized_frame = cv2.resize(frame, (resize_w, resize_h))
                images.append(resized_frame)
            cap.release()
            if verbose:
                logger.info(f"共提取了 {len(images)} 帧")

            # 把小图拼成大图
            combined_images = []
            for i in range((len(images) + num_per_image - 1) // num_per_image):
                image_w, image_h = resize_w * num_per_row, resize_h * num_per_col
                combined_image = np.zeros((image_h, image_w, 3)).astype("uint8")
                for j, img in enumerate(
                    images[i * num_per_image : i * num_per_image + num_per_image]
                ):
                    row, col = j // num_per_row, j % num_per_col
                    combined_image[
                        row * resize_h : (row + 1) * resize_h,
                        col * resize_w : (col + 1) * resize_w,
                    ] = img
                combined_images.append(combined_image)
            if verbose:
                logger.info(f"共拼成了 {len(combined_images)} 张大图")

            video_images += combined_images

        # 构建GPT的messages
        prompt = TEMPLATES["annotation"]["creator"]
        for i, video_info in enumerate(videos_info):
            prompt += TEMPLATES["annotation"]["single_video"].format(
                video_i=i + 1,
                frame_interval=video_info["frame_interval"],
                video_desc=video_info["desc"],
                video_content=video_info["content"],
            )
        messages = [{"role": "user", "content": [{"type": "text", "text": prompt}]}]
        if verbose:
            logger.info(f"message:\n{messages[0]['content'][0]['text']}")

        def encode_image(image_in):
            _, buffer = cv2.imencode(".jpg", image_in)
            return base64.b64encode(buffer).decode("utf-8")

        for image in video_images:
            messages[0]["content"].append(
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{encode_image(image)}"
                    },
                }
            )

        # 调用大模型，防止大模型不按照要求输出内容，会尝试两遍
        for i in range(2):
            response = self.client_annotate.chat.completions.create(
                model=GLOBAL_CONF.OPENAI_API_MODEL_NAME_ANNOTATE,
                messages=messages,
            )
            if verbose:
                logger.info(f"GPT返回的结果为: {response.choices[0].message.content}")
            try:
                s = response.choices[0].message.content
                d = loads(s)  # 防止GPT返回结果在```中
                annotation = GPTCreatorAnnotation(**d)
                return annotation
            except Exception as e:
                logger.error(
                    f"解析GPT返回的结果出错: {e}, 尝试重新调用GPT 返回结果: {response.choices[0].message.content}"
                )
                continue
        return None

    async def read_one_book(
        self,
        file_name,
        file_id,
        result_type,
        book_type,
        knowledge_base_name,
        doc_info_list,
        chat_response,
        read_result,
        yield_delta,
        chat_id: int = 0,
        conversation_id: str = "",
        user_id: int = 0,
    ):
        doc_match = next(
            (
                doc
                for doc in doc_info_list
                if file_name in doc.file_name or file_id == doc.file_id
            ),
            None,
        )
        if doc_match:
            file_name = doc_match.file_name
        else:
            file_not_exist_content = f"未找到需要总结的文档: {file_name}"
            yield file_not_exist_content
            read_result["collected_content"] = file_not_exist_content
            return  # 终止函数，避免继续执行

        file_details = knowledge_file_repository.get_file_detail(
            knowledge_base_name, file_name
        )
        oss_url_from_db = file_details.get("oss_url")  # Use .get() for safety

        if not oss_url_from_db:
            logger.warning(
                f"OSS URL not found in DB for file: {file_name} in KB: {knowledge_base_name}. File access or cover generation might fail if file not cached."
            )

        book_file = KnowledgeFile.new(
            filename=file_name,
            knowledge_base_name=knowledge_base_name,
            knowledge_base_type=KnowledgeBaseType.user,
            oss_url=oss_url_from_db,
        )
        book_content = book_file.file2chunks()[0].page_content
        if result_type == "report":
            prompt_name = f"read_report_{book_type}"
            prompt = langfuse_utils.get_prompt(
                prompt_name,
                TEMPLATES[AssistantType.read.value][result_type][book_type],
            )
        else:
            prompt = TEMPLATES[AssistantType.read.value][result_type][book_type]
        read_messages = [
            {
                "role": "user",
                "content": TEMPLATES[AssistantType.read.value]["book_content"].format(
                    book_name=file_name, book_content=book_content
                )
                + prompt,
            }
        ]
        open_ai_client = MyOpenAiLLMClient(
            self.client_read_async,
            model=self.read_model_name,
            stream=True,
        )
        response = await open_ai_client(
            messages=read_messages,
            json_convert_args=JsonConvertArgs(),
            progress_args=ProgressArgs(),
        )
        chat_response.append(response)
        read_result["collected_content"] = response

        if chat_id and conversation_id and user_id:
            chat_repository.updater(user_id, conversation_id)(
                chat_id, content=response, status=ChatStatus.done
            )

    @exception_logger
    async def generate_book_report(
        self,
        file_name: str,
        file_id: int,
        book_type: str,
        result_type: str,
        doc_info_list: list,
        knowledge_base_name: str,
        chat_id: str = "",
        verbose: bool = False,
        user_id: int = 0,
        conversation_id: str = "",
    ) -> tuple[str, str]:
        logger.info(f"generate_book_report start background task")
        update_chat = chat_repository.updater(user_id, conversation_id)
        doc_match = next(
            (
                doc
                for doc in doc_info_list
                if file_name in doc.file_name or file_id == doc.file_id
            ),
            None,
        )
        if doc_match:
            file_name = doc_match.file_name
        else:
            file_not_exist_content = "未找到需要总结的文档: {}".format(file_name)
            update_chat(
                chat_id, content=file_not_exist_content, status=ChatStatus.error
            )
            return "", ""

        task = task_repository.create_task_simple(
            task_type=TaskType.read_report, user_id=user_id
        )
        update_chat(
            chat_id,
            status=ChatStatus.ongoing,
            task_id=task.task_id,
            progress=0,
            content_type=ContentType.disasm_report,
        )

        file_stem = Path(file_name).stem.replace("《", "").replace("》", "")
        doc_title = f"《{file_stem}》的读书报告"
        doc_id = ""
        doc_url = ""

        def save_report_to_doc(
            chat_response: str, is_markdown: bool, is_final: bool
        ) -> str:
            nonlocal doc_id, doc_url
            try:
                if not chat_response:
                    return ""
                if is_markdown:
                    markdown = chat_response
                else:
                    markdown = read_assistant_utils.partial_json_to_book_report_str(
                        chat_response
                    )
                markdown = f"# {doc_title}\n\n" + markdown
                # 提示：这里可能是新建文档，也可能是更新文档
                doc_url, workspace_id, doc_id = document_service.update_md_doc(
                    user_id, doc_id, markdown
                )
                if not is_final:
                    final_answer_str = f"""
正在为您撰写读书报告...

[{doc_title}]({doc_url})

<span style='color: lightgray;'>（{len(markdown)}字）</span><loading-img-1></loading-img-1>
"""
                else:
                    final_answer_str = f"""
以下是为您撰写的读书报告，请点击查看：

[{doc_title}]({doc_url})
"""
                return final_answer_str
            except StopIteration:
                logger.warning(f"解析json失败: {chat_response}")
                return ""
            except Exception as e:
                logger.error(f"处理阅读报告失败: {e}", exc_info=True)
                return "生成阅读报告失败"

        updater = progress_onupdater(
            user_id,
            conversation_id,
            chat_id,
            chunk_handler=lambda resp: save_report_to_doc(resp, False, False),
        )

        try:
            logger.info(f"generate_book_report start generating {task.task_id}")
            # 生成读书报告

            # Fetch file details to get oss_url
            file_details = knowledge_file_repository.get_file_detail(
                knowledge_base_name, file_name
            )
            oss_url_from_db = file_details.get("oss_url")  # Use .get() for safety

            if not oss_url_from_db:
                logger.warning(
                    f"OSS URL not found in DB for file: {file_name} in KB: {knowledge_base_name}. File access or cover generation might fail if file not cached."
                )

            knowledge_file = KnowledgeFile.new(
                filename=file_name,
                knowledge_base_name=knowledge_base_name,
                knowledge_base_type=KnowledgeBaseType.user,
                oss_url=oss_url_from_db,
            )
            book_content = knowledge_file.file2chunks()[0].page_content
            updater(0.2)
            if result_type == "report":
                prompt_name = f"read_report_{book_type}"
                prompt = langfuse_utils.get_prompt(
                    prompt_name,
                    TEMPLATES[AssistantType.read.value][result_type][book_type],
                )
            else:
                prompt = TEMPLATES[AssistantType.read.value][result_type][book_type]
            read_messages = [
                {
                    "role": "user",
                    "content": TEMPLATES[AssistantType.read.value][
                        "book_content"
                    ].format(book_name=file_name, book_content=book_content)
                    + prompt,
                }
            ]

            open_ai_client = MyOpenAiLLMClient(
                self.client_read_async,
                model=self.read_model_name,
                stream=True,
            )

            json_response, report_str = await open_ai_client(
                messages=read_messages,
                json_convert_args=JsonConvertArgs(
                    expect_json=True,
                    json_result_converter=read_assistant_utils.dict_to_book_report_str,
                ),
                progress_args=ProgressArgs(
                    updater_func=updater,
                ),
            )

            # 如果是用的模型api挂了，换回gpt试试
            if json_response is None:
                book_content = knowledge_file.file2chunks()[0].page_content
                read_messages = [
                    {
                        "role": "user",
                        "content": TEMPLATES[AssistantType.read.value][
                            "book_content"
                        ].format(book_name=file_name, book_content=book_content)
                        + prompt,
                    }
                ]

                open_ai_client = MyOpenAiLLMClient(
                    self.client_chat_async,
                    model=self.model_name,
                    stream=True,
                )
                json_object_report, report_str = await open_ai_client(
                    messages=read_messages,
                    json_convert_args=JsonConvertArgs(
                        expect_json=True,
                        json_result_converter=read_assistant_utils.dict_to_book_report_str,
                    ),
                    progress_args=ProgressArgs(
                        updater_func=updater,
                    ),
                )

            if verbose:
                logger.info(f"json_response: {json_response}")
                logger.info(f"report_str: {report_str}")

            # 生成options
            options = await self.generate_options(
                messages=[{"role": "assistant", "content": report_str}],
                assistant=AssistantType.read,
            )

            book_report_thumbernail_url = (
                read_assistant_utils.generate_book_report_cover(knowledge_file, user_id)
            )
            logger.info(f"book_report_thumbernail_url: {book_report_thumbernail_url}")
            task_repository.update_task(
                task_id=task.task_id,
                user_id=user_id,
                affine_url=doc_url,
                report={"type": TaskType.read_report, "content": report_str},
                cover_file_path=book_report_thumbernail_url,
                status=TaskStatus.done,
            )

            book_report_finish_message = save_report_to_doc(report_str, True, True)
            update_chat(
                chat_id,
                status=ChatStatus.done,
                content=book_report_finish_message,
                options=options,
                task_report={"type": TaskType.read_report, "content": json_response},
                content_json={
                    "read_report_cover": book_report_thumbernail_url,
                    "read_report": json_response,
                },
                progress=1.0,
            )
            logger.info(f"generate_book_report finish background task {task.task_id}")

            return json_response, options
        except Exception as e:
            logger.error(f"generate_book_report失败，error: {e}", exc_info=True)
            update_chat(
                chat_id, status=ChatStatus.error, content="处理失败，请稍后再试。"
            )

    @exception_logger
    async def generate_product_report(
        self,
        docs: [],
        chat_id: str,
        user_id: int,
        conversation_id: str,
    ) -> tuple[str, str]:
        logger.info(f"generate_product_report start background task")
        update_chat = chat_repository.updater(user_id, conversation_id)

        if not docs:
            file_not_exist_content = "总结产品报告前，未找到能参考的的文档"
            logger.error(file_not_exist_content)
            update_chat(
                chat_id, content=file_not_exist_content, status=ChatStatus.error
            )
            return "", ""
        upload_waiting_message = TEMPLATES[AssistantType.product.value]["report"][
            "waiting"
        ]
        task = task_repository.create_task_simple(
            task_type=TaskType.product_report, user_id=user_id
        )
        update_chat(
            chat_id,
            content=upload_waiting_message,
            status=ChatStatus.ongoing,
            task_id=task.task_id,
            progress=0.1,
            content_type=ContentType.product_report,
        )
        updater = progress_onupdater(user_id, conversation_id, chat_id, False)

        logger.info(f"generate_product_report start generating {task.task_id}")
        updater(0.2)
        try:
            # 获取所有docs的全部内容
            context = ""
            doc_set = set()
            for doc in docs:
                filename = doc.filename
                if filename in doc_set:
                    continue
                with open(doc.filepath, "r") as f:
                    context += f.read()
                doc_set.add(filename)

            product_report_prompt = langfuse_utils.get_prompt(
                "product_report",
                TEMPLATES[AssistantType.product.value]["report"]["product"],
            )
            messages = [
                {
                    "role": "user",
                    "content": TEMPLATES[AssistantType.product.value][
                        "docs_content"
                    ].format(docs_content=context)
                    + product_report_prompt,
                }
            ]
            open_ai_client = MyOpenAiLLMClient(
                self.client_chat_async,
                model=self.model_name,
                stream=True,
            )

            json_object_report, report_str = await open_ai_client(
                messages=messages,
                json_convert_args=JsonConvertArgs(
                    expect_json=True,
                    json_result_converter=tasks_utils.convert_product_report_to_markdown,
                ),
                progress_args=ProgressArgs(
                    updater_func=updater,
                ),
            )
            logger.info(f"generate_product_report finish generating {task.task_id}")

            # todo (GSQ) 封面图 先hardcode一张默认产品报告封面
            report_thumbnail_url = "https://gptcloud.oss-jiaxing.sihe.cloud/knowledge_base/image/product_report_cover.png"
            logger.info(f"report_thumbnail_url: {report_thumbnail_url}")

            doc_url = document_service.import_markdown_doc(report_str, user_id=user_id)
            task_repository.update_task(
                task_id=task.task_id,
                user_id=user_id,
                affine_url=doc_url,
                report={"type": TaskType.product_report, "content": report_str},
                cover_file_path=report_thumbnail_url,
                status=TaskStatus.done,
            )
            report_finish_message = (
                '👉 产品报告已生成：<span class="chat-btn">点击查看</span>'
            )
            docs_dicts = [
                json.loads(json.dumps(d.__dict__, cls=DateTimeEnumEncoder))
                for d in docs
            ]
            update_chat(
                chat_id,
                status=ChatStatus.done,
                content=report_finish_message,
                docs=docs_dicts,
                content_json={
                    "product_report_cover": report_thumbnail_url,
                    "product_report_url": doc_url,
                },
                progress=1.0,
            )
            logger.info(
                f"generate_product_report finish background task {task.task_id}"
            )
            inc_generate_product_report_counter("success")
            return json_object_report, ""
        except Exception as e:
            logger.error(f"generate_product_report失败，error: {e}", exc_info=True)
            inc_generate_product_report_counter("failed")
            update_chat(
                chat_id, status=ChatStatus.error, content="处理失败，请稍后再试。"
            )

    async def request_claude(self, prompt: str, images: list = [], expect_json=True):
        messages = [
            {
                "role": "user",
                "content": [{"type": "text", "text": prompt}],
            },
            {
                # TODO：这个强制json的写法太诡异了
                # 预填充数据，确保claude返回json数据,ref:
                "role": "assistant",
                "content": "{",
            },
        ]

        for image in images:
            messages[0]["content"].append(
                {
                    "type": "image",
                    "source": {
                        "type": "base64",
                        "media_type": "image/jpeg",
                        "data": video_utils.encode_image_to_base64(image),
                    },
                }
            )

        claude_client = MyClaudeLLMClient(
            self.async_anthropic_client,
            model=self.claude_model_name,
            stream=False,
            max_tokens=8192,
            temperature=0,
        )
        response = await claude_client(
            messages=messages,
            json_convert_args=JsonConvertArgs(expect_json=expect_json),
            retry=3,
        )
        return response

    @exception_logger
    def test(self):
        read_messages = [{"role": "user", "content": "你好"}]
        response = self.client_read.chat.completions.create(
            model=self.read_model_name, messages=read_messages
        )
        print(response.choices[0].message.content)


# 懒加载全局 OpenAI 客户端
_openai_client = None
_openai_client_lock = threading.Lock()


def get_openai_client_instance() -> OpenAIClient:
    """获取 OpenAIClient 实例，使用延迟初始化和线程安全"""
    global _openai_client
    if _openai_client is None:
        with _openai_client_lock:
            if _openai_client is None:
                _openai_client = OpenAIClient()
    return _openai_client


def get_openai_client():
    yield get_openai_client_instance()


if __name__ == "__main__":
    chclient = ChClient()
    client = OpenAIClient(chclient)
    # result = asyncio.run(client.get_hot_video_by_keyword("白酒", ""))
    # print(result)
    # result = asyncio.run(client.get_hot_video_by_keyword("白酒", ""))
    # print(result)
    # result = asyncio.run(client.get_marketing_video(""))
    # print(result)
    # result = client.generate_options_sync(messages=[{
    #                             "role": "assistant",
    #                             "content": "你好"}],
    #                             assistant=AssistantType.general)
    client.test()
    result = client.generate_book_report(
        file_name="抖音新手入门全攻略(1).pdf",
        book_type="knowledge",
        result_type="report",
        doc_list=["抖音新手入门全攻略(1).pdf"],
        knowledge_base_name="user_4265_2c76f212eec044a1a2408b5f674ec391",
        user_id=4265,
        conversation_id="2c76f212eec044a1a2408b5f674ec391",
    )
    print(result)
