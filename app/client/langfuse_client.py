from langfuse.utils.langfuse_singleton import Langfuse<PERSON>ingleton
from app.config import langfuse_config
from app.logger import logger
from langfuse.decorators import langfuse_context


class LangfuseClient:
    def __init__(self):
        # 配置 langfuse_context，这样 @observe 就可以正常工作了
        # 它内部有一个 Singleton 对象。所以理论上与 self.client 是不同的对象
        langfuse_context.configure(
            public_key=langfuse_config["public_key"],
            secret_key=langfuse_config["secret_key"],
            host=langfuse_config["host"],
            environment=langfuse_config["environment"],
        )
        self.client = LangfuseSingleton().get()
        self.client.auth_check()

    def get_prompt(self, prompt_name, **kwargs):
        # 从 Langfuse 获取 prompt
        prompt = self.client.get_prompt(prompt_name, label="latest")
        if prompt:
            # 使用 variables 中的值替换 prompt 中的变量
            prompt = prompt.compile(**kwargs)
            return prompt

        logger.info(f"Prompt {prompt_name} not found")
        return None

    def create_trace(self, **kwargs):
        raise NotImplementedError(
            "请使用 langfuse_context 代替！文档： https://cybercore.feishu.cn/wiki/C1lZwQlbEiblF6kxZ5zcG4dDnqb#share-LYXgdvdscoxdraxEXNXcC5D5nAg"
        )

    def score(self, **kwargs):
        raise NotImplementedError(
            "请使用 langfuse_context 代替！文档： https://cybercore.feishu.cn/wiki/C1lZwQlbEiblF6kxZ5zcG4dDnqb#share-LYXgdvdscoxdraxEXNXcC5D5nAg"
        )

    def log_prompt(self, trace, name, prompt, completion=None, metadata=None):
        raise NotImplementedError(
            "请使用 langfuse_context 代替！文档： https://cybercore.feishu.cn/wiki/C1lZwQlbEiblF6kxZ5zcG4dDnqb#share-LYXgdvdscoxdraxEXNXcC5D5nAg"
        )


langfuse_client = LangfuseClient()
observe = langfuse_context.observe
