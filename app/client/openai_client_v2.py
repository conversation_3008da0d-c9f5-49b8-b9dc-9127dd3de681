import inspect
import json
from typing import Callable

from openai import NOT_GIVEN

from app.client.openai_client import OpenAIClient, AppendStringIO
from langchain_core.messages import BaseMessage
from langchain_core.messages.utils import convert_to_openai_messages
from app.logger import logger
from app.utils.common_utils import remove_reference_format

openai_client = OpenAIClient()


# 约定 tool call function 要么是 async generator（yield 流式内容给前端），
# 要么返回值作为 tool call result 再次发给 openai（if not None）
# 可以在 tool call function 中更新响应消息内容，传入 response_chat 即可操作响应内容
# 一个 tool call function 的定义应该是这样的
# def foo_function(processor, response_chat, messages):
#   async def foo(**kwargs):
#     input = kwargs["input"]
#     print(input)
#   return foo
class ToolCall:
    def __init__(
        self,
        name: str,
        function: Callable,
        definition,
        priority: int = 0,
    ):
        self.name = name
        self.function = function
        self.definition = definition
        self.priority = priority


async def call_openai(messages, tools, tool_choice="auto", result={}, **kwargs):
    if not tools:
        tool_choice = NOT_GIVEN
    for i in range(3):
        try:
            logger.info(f"call openai attempt {i}")
            for m in messages:
                logger.info(m)
            response = await openai_client.client_chat_async.chat.completions.create(
                model=openai_client.model_name,
                messages=messages,
                tools=tools or NOT_GIVEN,
                tool_choice=tool_choice,
                stream=True,
                **kwargs,
            )
            logger.info(f"openai responded attempt {i}")
            async for item in openai_client.collect_and_yield_chunks(
                response, AppendStringIO(), result, False
            ):
                yield item

            collected_content = result["collected_content"]
            logger.info(f"collected content: {collected_content}")
            return
        except Exception as e:
            logger.error(f"call openai attempt {i} error, {e}")
    raise Exception(f"call openai error")


# 调用 tool call，如果 tool call 需要 second call，会将 second response 的内容流式返回
async def call_tool_functions(
    functions: list[ToolCall], tool_calls, messages, **kwargs
):
    # 如果调用了 priority 为 0 的方法，则只调用这个方法，忽略其他 tool_call
    functions.sort(key=lambda x: x.priority)

    do_second_call = False
    for f in functions:
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            if f.name == function_name:
                tool_call_message = {
                    "role": "assistant",
                    "tool_calls": [
                        {
                            "id": tool_call.id,
                            "type": "function",
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments,
                            },
                        },
                    ],
                }
                function_args = json.loads(tool_call.function.arguments)
                logger.info(f"调用 {function_name}, 参数 {function_args}")
                try:
                    if isinstance(function_args, list):
                        result = f.function(function_args)
                    else:
                        result = f.function(**function_args)
                    if inspect.isasyncgen(result):
                        async for item in result:
                            yield item
                    else:
                        if inspect.isawaitable(result):
                            tool_call_result = await result
                        else:
                            tool_call_result = result
                        logger.info(f"{function_name} 返回了 {tool_call_result}")
                        if tool_call_result is not None:
                            do_second_call = True
                            messages.append(tool_call_message)
                            messages.append(
                                {
                                    "role": "tool",
                                    "tool_call_id": tool_call.id,
                                    "content": tool_call_result,
                                }
                            )
                except Exception as e:
                    logger.error("调用{}出错，{}".format(function_name, e))
                    messages.append(tool_call_message)
                    messages.append(
                        {
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": "调用出错，{}".format(e),
                        }
                    )
                break
        else:
            # 没有调用这个方法，继续匹配下一个
            continue
        if f.priority == 0:
            break

    if do_second_call:
        logger.info(f"do second call: {messages[-2:]}")
        # 这里第二次请求没有再传入 tools 了，如果以后出现第二次请求也要传入 tools 的情况，再进行修改吧
        async for item in call_openai(messages, None, **kwargs):
            yield item


async def auto_chat(
    messages: list[BaseMessage], functions: list[ToolCall], tool_choice, **kwargs
):
    tools = [f.definition for f in functions]
    messages = convert_to_openai_messages(messages)  # type: ignore

    result = {}
    async for item in call_openai(messages, tools, tool_choice, result, **kwargs):
        # 对于第一次调用返回的结果，必然不包含引用，如果有引用,在search_knowledge_base后的call_openai返回
        # 在这里如果content是str，删除无意义的引用格式
        if isinstance(item, str):
            item = remove_reference_format(item)
        yield item

    tool_calls = result["tool_calls"]
    if tool_calls:
        # openai 选择调用 tool calls
        logger.info(f"function callings: {tool_calls}")
        try:
            async for item in call_tool_functions(
                functions,
                tool_calls,
                messages,
            ):
                yield item
        except Exception as e:
            logger.error(f"call tool function error, {e}")
            yield "处理失败，请稍后再试"
