"""
文本嵌入服务客户端

本模块提供与远程文本嵌入服务（model_server）进行通信的客户端功能。
现在项目统一使用远程嵌入服务，不再使用本地模型。

主要功能：
- 文本向量化：将文本转换为高维向量表示
- 图像向量化：将图像转换为高维向量表示  
- 批量文本向量化：同时处理多个文本

所有功能都通过HTTP请求调用远程的model_server服务。
"""

import time
import json
import requests
import os
import asyncio
import hashlib
from typing import Any, Dict, List, Optional, Union


from app.logger import logger
from app.config import EMBEDDING_SERVER_ADDRESS
from app.client.redis_client import redis_client

TIMEOUT_IN_SECOND = 30
EMBEDDING_TEXT_URI = EMBEDDING_SERVER_ADDRESS + "/embedding/text"
EMBEDDING_DOCUMENTS_URI = EMBEDDING_SERVER_ADDRESS + "/embedding/documents"
EMBEDDING_IMAGE_URI = EMBEDDING_SERVER_ADDRESS + "/embedding/image"

# 缓存过期时间：3个月（秒）
CACHE_EXPIRE_TIME = 3 * 30 * 24 * 60 * 60  # 7776000 秒


def make_embedding_request(
    url: str, data: Dict[str, Any], files: Optional[Dict[str, Any]] = None
) -> Union[List[float], List[List[float]]]:
    headers = {"accept": "application/json"}
    if not files:
        headers["content-type"] = "application/json"
        json_data = json.dumps(data)
    else:
        json_data = data

    start_time = time.time()
    try:
        response = requests.post(
            url, headers=headers, data=json_data, files=files, timeout=TIMEOUT_IN_SECOND
        )
        response.raise_for_status()
        result = response.json()
        return result["data"]
    except Exception as e:
        logger.error(f"Embedding request failed: {str(e)}")
        raise


def _get_cache_key_and_field(text: str, model_name: str) -> tuple[str, str]:
    """生成缓存的key和field

    Args:
        text: 待嵌入的文本
        model_name: 模型名称

    Returns:
        tuple: (redis_key, hash_field)
    """
    # Redis hash key
    redis_key = f"embedding_cache:text:{model_name}"

    # Hash field: 如果文本长度小于32字符则使用原文，否则使用sha1
    if len(text) < 32:
        hash_field = text
    else:
        hash_field = hashlib.sha1(text.encode("utf-8")).hexdigest()

    return redis_key, hash_field


def embedding_image(image_path: str) -> List[float]:
    with open(image_path, "rb") as image_file:
        files = {"file": (os.path.basename(image_path), image_file)}
        result = make_embedding_request(EMBEDDING_IMAGE_URI, {}, files)
        return result  # type: ignore


def embedding_text(text: str, model_name: str = "bge") -> List[float]:
    """获取文本嵌入向量，带Redis缓存

    Args:
        text: 待嵌入的文本
        model_name: 模型名称，默认为"bge"

    Returns:
        List[float]: 嵌入向量
    """
    logger.debug(
        f"app/client/embedding_service_client.py: embedding_text ({model_name})"
    )

    # 生成缓存key
    redis_key, hash_field = _get_cache_key_and_field(text, model_name)

    try:
        # 尝试从缓存获取
        cached_result = redis_client.get_client().hget(redis_key, hash_field)
        if cached_result and isinstance(cached_result, str):
            logger.debug(f"从缓存获取embedding: {hash_field[:50]}...")
            return json.loads(cached_result)
    except Exception as e:
        logger.warning(f"从Redis缓存读取embedding失败: {e}")

    # 缓存未命中，调用远程服务
    result = make_embedding_request(
        EMBEDDING_TEXT_URI, {"text": text, "name": model_name}
    )

    try:
        # 将结果存入缓存
        redis_client.get_client().hset(redis_key, hash_field, json.dumps(result))
        # 设置过期时间（只在首次创建hash时生效）
        redis_client.get_client().expire(redis_key, CACHE_EXPIRE_TIME)
        logger.debug(f"已缓存embedding: {hash_field[:50]}...")
    except Exception as e:
        logger.warning(f"将embedding结果写入Redis缓存失败: {e}")

    return result  # type: ignore


def embedding_documents(texts: List[str]) -> List[List[float]]:
    result = make_embedding_request(EMBEDDING_DOCUMENTS_URI, {"texts": texts})
    return result  # type: ignore


async def main():
    # 这个函数仅用于测试，实际上embedding_text是同步函数
    print(f"start time {time.time()}")
    results = []
    for i in range(10):
        result = embedding_text("你好", "bge")
        results.append(result)
    print(f"end time {time.time()}")
    return results


if __name__ == "__main__":
    # asyncio.run(main())
    result = embedding_image("/root/kevin/test.jpg")
    print(result)
