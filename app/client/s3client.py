import botocore.exceptions
import re
from botocore.config import Config
from pydantic import BaseModel
from PIL import Image
import io
import boto3
import os
import mimetypes
import cv2
import fitz
import tempfile
import subprocess
from urllib.parse import urljoin, quote
from pathvalidate import sanitize_filename

from app.utils.common_utils import get_file_type
from app.config import (
    USER_BUCKET_TEST,
    USER_BUCKET_PROD,
    ASSISTANT_DATA,
    GLOBAL_CONF,
    S3_URL_EXPIRATION_TIME,
)
from app.env import SIHEGPT_STAGE
from app.logger import logger
from enum import Enum

stage = SIHEGPT_STAGE

boto3_endpoint = os.getenv("BOTO3_ENDPOINT", "https://oss-jiaxing.sihe.cloud")
boto3_access_key = os.getenv("BOTO3_ACCESS_KEY", "G71EUHzXPMGnaF7Y9uGXYklO")
boto3_secret_key = os.getenv("BOTO3_SECRET_KEY", "fuggiAMmTsXWPerkeLONP8x0hvHEJfsM")
USER_BUCKET_ID = USER_BUCKET_PROD if stage == "PROD" else USER_BUCKET_TEST
CRAWLER_BUCKET_ID = os.getenv("BOTO3_CRAWLER_BUCKET_ID", "gptcloud")


class FilePathType(Enum):
    FILEPATH_FILE = 1
    FILEPATH_FOLDER = 2


class FileItem(BaseModel):
    filepath: str
    last_modified: str
    size: int
    filetype: str
    thumbnail_url: str = ""


class S3Client:
    def __init__(self):
        self.client = boto3.client(
            "s3",
            aws_access_key_id=boto3_access_key,
            aws_secret_access_key=boto3_secret_key,
            use_ssl=True,
            region_name="cn",
            endpoint_url=boto3_endpoint,
            config=Config(signature_version="s3v4", s3={"addressing_style": "virtual"}),
        )

        # v2版本的签名的client，专门用来生成签名有效期超过7天的url。
        self.url_client_v2 = boto3.client(
            "s3",
            aws_access_key_id=boto3_access_key,
            aws_secret_access_key=boto3_secret_key,
            use_ssl=True,
            region_name="cn",
            endpoint_url=boto3_endpoint,
            config=Config(signature_version="s3", s3={"addressing_style": "path"}),
        )

    def is_result_ok(self, return_obj):
        ifcode = return_obj["ResponseMetadata"]["HTTPStatusCode"]
        if ifcode == 200 or ifcode == 204:
            return True
        logger.error(f"s3返回失败 {return_obj}")
        return False

    def combine_user_path(self, filepath: str, user_path: str):
        if user_path == None:
            return filepath
        if filepath.startswith("/"):
            filepath = filepath[1:]
        if filepath != "/":
            return os.path.join(user_path, filepath)
        return user_path + "/"

    def put_obj(
        self, filepath, body, user_path=None, content_type=None, bucket_id=None
    ):
        bucket_id = bucket_id or USER_BUCKET_ID
        real_path = self.combine_user_path(filepath, user_path)
        if content_type != None:
            result = self.client.put_object(
                Bucket=bucket_id,
                Body=body,
                Key=real_path,
                ContentType=content_type,
            )
        else:
            result = self.client.put_object(Bucket=bucket_id, Body=body, Key=real_path)
        if self.is_result_ok(result):
            # 上传成功后更新缩略图
            ext = filepath.split(".")[-1]
            thumbnail_name = self.get_thumbnail_name(real_path)
            thumbnail_path = os.path.join(os.path.dirname(real_path), thumbnail_name)
            self.get_and_put_thumbnail_to_s3(body, ext, thumbnail_path=thumbnail_path)

            url = f"https://{bucket_id}.oss-jiaxing.sihe.cloud/{real_path}"
            return url
        return False

    def get_thumbnail_name(self, filepath):
        # 缩略图文件名为.__sihe_thumb.$原文件名.jpg
        return f".__sihe_thumb.{os.path.basename(filepath)}.jpg"

    # 通过原文件路径获取其缩略图路径
    def get_thumbnail_path(self, filepath):
        dir_path, filename = os.path.split(filepath)
        thumbnail_path = f"{dir_path}/{self.get_thumbnail_name(filename)}"
        return thumbnail_path

    def get_file_path_by_thumbnail_path(self, thumbnail_path: str):
        # 如 user_19/.__sihe_thumb.test.mp4.jpg -> user_19/test.mp4
        return thumbnail_path.replace(".__sihe_thumb.", "")[:-4]

    def create_folder(self, filepath, user_path=None):
        real_path = filepath
        if not filepath.endswith("/"):
            real_path = filepath + "/"
        result = self.put_obj(real_path, "", user_path)
        return result

    def get_obj_list(self, filepath, user_path=None):
        real_path = self.combine_user_path(filepath, user_path)
        if not real_path.endswith("/"):
            real_path = real_path + "/"

        continuation_token = None
        result = []
        while True:
            kwargs = {
                "Bucket": USER_BUCKET_ID,
                "Prefix": real_path,
                "MaxKeys": 1000,  # s3每次最多取1000个文件
            }

            if continuation_token:
                kwargs["ContinuationToken"] = continuation_token

            objects = self.client.list_objects_v2(**kwargs)

            if "Contents" not in objects:
                return result

            thumbnail_map = {}
            for obj in objects["Contents"]:
                filepath = obj["Key"]
                if user_path != None:
                    filepath = filepath.removeprefix(user_path + "/")
                if filepath == "":
                    continue
                file_type = get_file_type(filepath)
                # 如果是缩略图，保存在map中，等待后续处理
                if os.path.basename(filepath).startswith(".__sihe_thumb"):
                    # 直接给出可以直接访问的带有效期的链接
                    thumbnail_map[self.get_file_path_by_thumbnail_path(filepath)] = (
                        self.get_obj_download_url(
                            filepath=filepath,
                            user_path=user_path,
                            expiration=S3_URL_EXPIRATION_TIME,
                            preview=True,
                        )
                    )
                    continue
                item = FileItem(
                    filepath=filepath,
                    last_modified=obj["LastModified"].isoformat(),
                    size=obj["Size"],
                    filetype=file_type,
                    thumbnail_url=(
                        thumbnail_map[filepath]
                        if filepath in thumbnail_map and file_type != "folder"
                        else ""
                    ),
                )
                result.append(item)

            # 检查是否有更多对象需要列出
            if objects.get("IsTruncated"):
                continuation_token = objects.get("NextContinuationToken")
            else:
                break
        return result

    def get_objs_total_size(self, user_path=None):
        real_path = user_path
        if not real_path.endswith("/"):
            real_path = real_path + "/"

        # 分页器配置
        paginator = self.client.get_paginator("list_objects_v2")
        pagination_config = {"PageSize": 1000}  # 每页最多返回1000个对象
        page_iterator = paginator.paginate(
            Bucket=USER_BUCKET_ID, Prefix=real_path, PaginationConfig=pagination_config
        )

        total_size = 0
        for page in page_iterator:
            for obj in page.get("Contents", []):
                total_size += obj["Size"]

        return total_size

    def check_obj_exists(self, filepath, user_path=None) -> bool:
        real_path = self.combine_user_path(filepath, user_path)
        try:
            self.client.get_object(Bucket=USER_BUCKET_ID, Key=real_path)
            return True
        except botocore.exceptions.ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                return False
            else:
                raise e

    def get_obj(self, filepath, user_path=None):
        real_path = self.combine_user_path(filepath, user_path)
        result = self.client.get_object(Bucket=USER_BUCKET_ID, Key=real_path)
        return self.is_result_ok(result)

    def get_obj_size(self, filepath, user_path=None) -> int:
        real_path = self.combine_user_path(filepath, user_path)
        try:  # 目标文件可能不存在(如缩略图)
            result = self.client.head_object(Bucket=USER_BUCKET_ID, Key=real_path)
            return result["ContentLength"]
        except Exception as e:
            return None

    def get_obj_with_result(self, filepath, user_path=None):
        real_path = self.combine_user_path(filepath, user_path)
        result = self.client.get_object(Bucket=USER_BUCKET_ID, Key=real_path)
        return result

    def get_obj_with_url(self, url: str):
        match = re.search(r"^https://(.*?)\.oss-jiaxing\.sihe\.cloud/(.*?)$", url)
        if not match:
            match = re.search(
                r"^https://oss-jiaxing\.sihe\.cloud/(.*?)/(.*?)\?.*$", url
            )
        try:
            bucket = match.group(1)
            key = match.group(2)
            result = self.client.get_object(Bucket=bucket, Key=key)
            return result
        except:
            return None

    def download_obj(
        self, filepath, work_folder="tmp", user_path=None, bucket=USER_BUCKET_ID
    ):
        real_path = self.combine_user_path(filepath, user_path)
        result = self.client.get_object(Bucket=bucket, Key=real_path)
        if not self.is_result_ok(result):
            return None
        download_path = os.path.join(work_folder, real_path)
        os.makedirs(os.path.dirname(download_path), exist_ok=True)
        file = open(download_path, mode="wb")
        file.write(result["Body"].read())
        file.close()
        return download_path

    # copy from other bucket to user bucket
    def copy_from_crawler_to_user(self, src_path, dest_path, user_path=None):
        # src_path: 源文件路径，非用户目录
        dest_real_path = self.combine_user_path(dest_path, user_path)
        result = self.client.copy_object(
            Bucket=USER_BUCKET_ID,
            CopySource={"Bucket": CRAWLER_BUCKET_ID, "Key": src_path},
            Key=dest_real_path,
        )
        return self.is_result_ok(result)

    def del_obj(self, filepath, user_path=None) -> bool:
        real_path = self.combine_user_path(filepath, user_path)
        result = self.client.delete_object(Bucket=USER_BUCKET_ID, Key=real_path)
        if not self.is_result_ok(result):
            return False

        # 对应操作缩略图
        thumbnail_real_path = self.combine_user_path(
            self.get_thumbnail_path(filepath), user_path
        )
        # 缩略图存在
        if self.check_obj_exists(self.get_thumbnail_path(filepath), user_path):
            result = self.client.delete_object(
                Bucket=USER_BUCKET_ID, Key=thumbnail_real_path
            )
            if not self.is_result_ok(result):
                return False

        return True

    def copy_user_obj(
        self,
        filepath,
        new_filepath,
        user_path=None,
        src_bucket=None,
        copy_thumbnail=True,
    ) -> bool:
        src_user_path = None if src_bucket else user_path
        real_old_path = self.combine_user_path(filepath, src_user_path)
        real_new_path = self.combine_user_path(new_filepath, user_path)
        result = self.client.copy_object(
            Bucket=USER_BUCKET_ID,
            Key=real_new_path,
            CopySource={"Bucket": src_bucket or USER_BUCKET_ID, "Key": real_old_path},
        )
        if not self.is_result_ok(result):
            return False
        if not copy_thumbnail:
            return True

        # 对应操作缩略图
        thumbnail_real_old_path = self.combine_user_path(
            self.get_thumbnail_path(filepath), src_user_path
        )
        thumbnail_real_new_path = self.combine_user_path(
            self.get_thumbnail_path(new_filepath), user_path
        )
        # 缩略图存在
        if self.check_obj_exists(self.get_thumbnail_path(filepath), user_path):
            result = self.client.copy_object(
                Bucket=USER_BUCKET_ID,
                Key=thumbnail_real_new_path,
                CopySource={
                    "Bucket": src_bucket or USER_BUCKET_ID,
                    "Key": thumbnail_real_old_path,
                },
            )
            if not self.is_result_ok(result):
                return False

        return True

    def is_file_or_folder(self, filepath, user_path=None):
        real_path = self.combine_user_path(filepath, user_path)

        # 尝试列出以路径为前缀的对象
        response = self.client.list_objects_v2(
            Bucket=USER_BUCKET_ID, Prefix=real_path, Delimiter="/"
        )

        # 如果 CommonPrefixes 不为空，表示该路径是文件夹
        if "CommonPrefixes" in response and response["CommonPrefixes"]:
            return FilePathType.FILEPATH_FOLDER

        # 如果 Contents 不为空，进一步检查
        if "Contents" in response and response["Contents"]:
            for obj in response["Contents"]:
                # 如果有对象键与路径完全匹配，且路径不以斜杠结尾，则是文件
                if obj["Key"] == real_path and not real_path.endswith("/"):
                    return FilePathType.FILEPATH_FILE
                # 如果有对象键以路径为前缀，且路径以斜杠结尾，则是文件夹
                if obj["Key"].startswith(real_path) and real_path.endswith("/"):
                    return FilePathType.FILEPATH_FOLDER

        # 默认情况下，如果没有匹配的对象，则认为路径无效或是文件
        return None

    def delete_folder(self, filepath, user_path):
        real_path = self.combine_user_path(filepath, user_path)
        if not real_path.endswith("/"):
            real_path += "/"

        response = self.client.list_objects_v2(Bucket=USER_BUCKET_ID, Prefix=real_path)
        if "Contents" not in response:
            return False, []
        failed_files = []
        deleted_size = 0
        for obj in response["Contents"]:
            key = obj["Key"]
            file_size = obj["Size"]
            result = self.client.delete_object(Bucket=USER_BUCKET_ID, Key=key)
            if not self.is_result_ok(result):
                failed_files.append(key)
                continue
            deleted_size += file_size
        if len(failed_files) == 0:
            return True, [], deleted_size
        else:
            return False, failed_files, deleted_size

    def copy_folder(self, filepath, user_path, new_filepath):
        source_real_path = self.combine_user_path(filepath, user_path)
        if not source_real_path.endswith("/"):
            source_real_path += "/"
        dest_real_path = self.combine_user_path(new_filepath, user_path)
        dest_real_path = dest_real_path.rstrip("/") + "/"

        response = self.client.list_objects_v2(
            Bucket=USER_BUCKET_ID, Prefix=source_real_path
        )
        if "Contents" not in response:
            return False, []

        failed_files = []

        for obj in response["Contents"]:
            key = obj["Key"]
            destination_key = dest_real_path + key[len(source_real_path) :]
            copy_source = {"Bucket": USER_BUCKET_ID, "Key": key}

            result = self.client.copy_object(
                CopySource=copy_source, Bucket=USER_BUCKET_ID, Key=destination_key
            )
            if not self.is_result_ok(result):
                failed_files.append(key)
                continue

        return (len(failed_files) == 0), failed_files

    def rename_folder(self, filepath, user_path, new_folder_name):
        source_real_path = self.combine_user_path(filepath, user_path)
        if not source_real_path.endswith("/"):
            source_real_path += "/"

        # 获取父路径
        parent_path = os.path.dirname(source_real_path.rstrip("/")) + "/"
        # 构造新的目标路径，保持父路径不变，仅修改最内层目录名
        dest_real_path = os.path.join(parent_path, new_folder_name) + "/"
        if source_real_path == dest_real_path:
            return True, []

        response = self.client.list_objects_v2(
            Bucket=USER_BUCKET_ID, Prefix=source_real_path
        )
        if "Contents" not in response:
            return False, []

        failed_files = []

        # 复制文件到新位置并删除旧文件
        for obj in response["Contents"]:
            key = obj["Key"]
            destination_key = dest_real_path + key[len(source_real_path) :]
            copy_source = {"Bucket": USER_BUCKET_ID, "Key": key}

            # 复制对象
            result = self.client.copy_object(
                CopySource=copy_source, Bucket=USER_BUCKET_ID, Key=destination_key
            )
            if not self.is_result_ok(result):
                failed_files.append(key)
                continue

            # 删除原对象
            result = self.client.delete_object(Bucket=USER_BUCKET_ID, Key=key)
            if not self.is_result_ok(result):
                failed_files.append(key)
                continue

        if failed_files:
            return False, failed_files

        return True, []

    def get_obj_download_url(
        self,
        filepath,
        user_path=None,
        expiration=S3_URL_EXPIRATION_TIME,
        preview=False,
        mime_type=None,
        bucket=USER_BUCKET_ID,
        download_as_name=None,
    ):
        real_path = self.combine_user_path(filepath, user_path)
        if not mime_type:
            mime_type, _ = mimetypes.guess_type(real_path)
            if mime_type is None:
                # 如果无法识别文件类型,使用默认的 octet-stream
                mime_type = "application/octet-stream"
        try:
            params = {
                "Bucket": bucket,
                "Key": real_path,
                "ResponseContentType": mime_type,
            }

            content_disposition = "inline" if preview else "attachment"
            if download_as_name:
                download_as_name = quote(download_as_name)
                content_disposition = (
                    f'{content_disposition}; filename="{download_as_name}"'
                )
            params["ResponseContentDisposition"] = content_disposition

            return self.url_client_v2.generate_presigned_url(
                "get_object",
                Params=params,
                ExpiresIn=expiration,
            )
        except Exception as e:
            logger.error(f"s3获取下载链接失败 {e}")
            return ""

    def get_named_download_url(
        self,
        video_oss_url: str,
        video_title: str,
    ):
        match = re.search(
            r"^https://(.*?)\.oss-jiaxing\.sihe\.cloud/(.*?)$", video_oss_url
        )
        if not match:
            logger.warning(f"{video_oss_url} not match bucket oss url")
            return video_oss_url
        try:
            bucket = match.group(1)
            key = match.group(2)
            download_as_name = ""
            if video_title:
                video_title = sanitize_filename(video_title)
                download_as_name = f"{video_title}.mp4"
            return self.get_obj_download_url(
                filepath=key,
                bucket=bucket,
                preview=False,
                download_as_name=download_as_name,
            )
        except Exception as e:
            logger.error(f"get named download url failed for {video_oss_url}: {e}")
            return video_oss_url

    def add_copy_suffix(
        self,
        file_path: str,
        user_path: str,
        suffix: str = " 的副本",
        check_self: bool = False,
    ):
        def file_names():
            base, ext = os.path.splitext(file_path)
            if check_self:
                yield f"{base}{ext}"
            if suffix:
                yield f"{base}{suffix}{ext}"
            counter = 2 if suffix else 1
            while True:
                yield f"{base}{suffix} {counter}{ext}"
                counter += 1

        # 检查路径是否已经存在
        for new_file_path in file_names():
            if self.is_file_or_folder(new_file_path, user_path) is None:
                return new_file_path

    def put_artificial_video(self, filepath, aweme_id):
        key = f"videodata/{aweme_id}.mp4"
        result = self.client.put_object(
            Bucket=CRAWLER_BUCKET_ID, Body=open(filepath, "rb"), Key=key
        )
        if self.is_result_ok(result):
            url = f"https://{CRAWLER_BUCKET_ID}.oss-jiaxing.sihe.cloud/{key}"
            return url
        return ""

    def get_artificial_video_play_url(self, aweme_id):
        key = f"videodata/{aweme_id}.mp4"
        return self.url_client_v2.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": CRAWLER_BUCKET_ID,
                "Key": key,
                "ResponseContentType": "video/mp4",
            },
        )

    def put_model_adjust_image(self, filepath, image_id):
        key = f"model_adjust_data/{image_id}.png"
        result = self.client.put_object(
            Bucket=CRAWLER_BUCKET_ID, Body=open(filepath, "rb"), Key=key
        )
        if self.is_result_ok(result):
            url = f"https://{CRAWLER_BUCKET_ID}.oss-jiaxing.sihe.cloud/{key}"
            return key, url
        return "", ""

    # 这里的thumbnail_path已经包含了用户路径，即：user_path + file_path
    def get_and_put_thumbnail_to_s3(
        self, file_bytes, file_type: str, thumbnail_size=(600, 600), thumbnail_path=None
    ):
        try:
            file_type = file_type.lower()
            if file_type == "mp4":
                thumbnail_body = self.get_mp4_thumbnail_body(file_bytes, thumbnail_size)
            elif file_type in ["png", "jpg", "jpeg", "bmp"]:
                thumbnail_body = self.get_image_thumbnail_body(
                    file_bytes=file_bytes, thumbnail_size=thumbnail_size
                )
            elif file_type in ["pdf", "epub"]:
                thumbnail_body = self.get_pdf_epub_thumbnail_body(
                    file_type=file_type,
                    file_bytes=file_bytes,
                    thumbnail_size=thumbnail_size,
                )
            elif file_type in ["doc", "docx", "ppt", "pptx", "xls", "xlsx"]:
                thumbnail_body = self.get_office_file_thumbnail_body(
                    file_bytes=file_bytes,
                    file_type=file_type,
                    thumbnail_size=thumbnail_size,
                )
            else:
                return

            if thumbnail_body:
                # 上传到s3
                result = self.client.put_object(
                    Bucket=USER_BUCKET_ID,
                    Body=thumbnail_body,
                    Key=thumbnail_path,
                    ContentType="image/jpeg",
                )
                if not self.is_result_ok(result):
                    logger.error(f"Failed to upload thumbnail to S3: {thumbnail_path}")
        except Exception as e:
            logger.error(
                f"Failed to generate and upload thumbnail to S3: {e}", exc_info=True
            )

    def get_mp4_thumbnail_body(self, file_bytes, thumbnail_size=(100, 72)):
        cap = None
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
                temp_file.write(file_bytes)
                temp_file_path = temp_file.name

            if not temp_file_path:
                logger.error("Failed to write MP4 to temporary file")
                return ""

            # 使用 OpenCV 读取视频文件
            cap = cv2.VideoCapture(temp_file_path)
            if not cap.isOpened():
                logger.error("Failed to open video file")
                return ""

            # 使用 OpenCV 读取视频文件
            ret, frame = cap.read()
            if not ret:
                cap.release()
                return ""

            # OpenCV 读取的帧为 BGR 颜色空间，需转换为 RGB 以避免颜色偏差
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            image.thumbnail(thumbnail_size)
            thumbnail_bytes = io.BytesIO()
            # 保存缩略图
            image.save(thumbnail_bytes, format="JPEG")

            return thumbnail_bytes.getvalue()
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for MP4: {e}", exc_info=True)
            return ""
        finally:
            if cap:
                cap.release()
            if temp_file_path:
                os.remove(temp_file_path)

    def get_image_thumbnail_body(
        self, file=None, file_bytes=None, thumbnail_size=(100, 72)
    ):
        try:
            # 打开图片字节流
            if file:
                image = Image.open(file)
            else:
                image = Image.open(io.BytesIO(file_bytes))
            # 打开图像并调整大小
            image.thumbnail(thumbnail_size)
            # RGBA存不成JPEG，需要转成RGB
            if image.mode in ("RGBA", "P"):
                image = image.convert("RGB")
            # 返回缩略图的filebytes
            thumbnail_bytes = io.BytesIO()
            image.save(thumbnail_bytes, format="JPEG")
            return thumbnail_bytes.getvalue()
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for image: {e}", exc_info=True)
            return None

    def get_pdf_epub_thumbnail_body(
        self, file_type, file_bytes=None, thumbnail_size=(100, 72)
    ):
        # 打开PDF文件
        try:
            pdf_document = fitz.open(stream=io.BytesIO(file_bytes), filetype=file_type)
            # 获取第一页
            page = pdf_document.load_page(0)
            # 获取页面的矩形区域
            rect = page.rect
            # 对于epub, 需要处理去掉下方的一段固定白边
            if file_type == "epub":
                rect = fitz.Rect(rect.x0, rect.y0, rect.x1, rect.y1 - 85)
            # 计算缩放比例
            scale = min(thumbnail_size[0] / rect.width, thumbnail_size[1] / rect.height)
            mat = fitz.Matrix(scale, scale)
            # 渲染页面为图像, 但对于epub不需要考虑下方的白边
            pix = page.get_pixmap(matrix=mat, clip=rect)
            # 将图像转换为PIL图像对象
            image = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)
            image.thumbnail((rect.width * scale, rect.height * scale))
            thumbnail_bytes = io.BytesIO()
            image.save(thumbnail_bytes, format="JPEG")

            return thumbnail_bytes.getvalue()
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for PDF: {e}", exc_info=True)
            return ""

    def get_office_file_thumbnail_body(
        self, file_bytes=None, file_type=None, thumbnail_size=(100, 72)
    ):
        input_file = None
        output_file = None
        try:
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=f".{file_type}"
            ) as temp_file:
                temp_file.write(file_bytes)
                input_file = temp_file.name

            # 会输出同名的png文件
            output_file = "/tmp/" + os.path.basename(
                input_file.replace(f".{file_type}", ".png")
            )

            # libreoffice --headless --convert-to png --outdir . {file}
            command = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "png",
                "--outdir",
                "/tmp/",
                input_file,
            ]

            result = subprocess.run(command, check=True)

            if result.returncode == 0:
                # 读取生成的首页png文件，压缩为缩略图
                return self.get_image_thumbnail_body(
                    file=output_file, thumbnail_size=thumbnail_size
                )
            else:
                logger.error(f"Failed to convert {input_file} to PDF")
                return ""
        except Exception as e:
            logger.error(f"Failed to convert {input_file} to PDF: {e}", exc_info=True)
            return ""
        finally:
            if input_file:
                os.remove(input_file)
            if output_file:
                os.remove(output_file)

    # 上传视频的封面图到用户拆解数据下
    def put_disassemble_video_cover_image(self, file_path, user_path):
        cap = None
        cover_file_path = os.path.join(
            ASSISTANT_DATA, f"{os.path.basename(file_path)}.png"
        )
        try:

            # 使用 OpenCV 读取视频文件
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                logger.error("Failed to open video file")
                return ""
            ret, frame = cap.read()
            if not ret:
                cap.release()
                return ""
            # 转换颜色通道从 BGR 到 RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            # 保存封面图
            image = Image.fromarray(frame_rgb)
            cover_bytes = io.BytesIO()
            image.save(cover_bytes, format="PNG")

            self.put_obj(cover_file_path, cover_bytes.getvalue(), user_path)
            return cover_file_path
        except Exception as e:
            logger.error(f"Failed to generate thumbnail for MP4: {e}", exc_info=True)
            return ""
        finally:
            if cap:
                cap.release()

    def put_video_frame(self, filepath, aweme_id, frame_id):
        key = f"videoframedata/{aweme_id}/{frame_id}.jpg"
        result = self.client.put_object(
            Bucket=CRAWLER_BUCKET_ID, Body=open(filepath, "rb"), Key=key
        )
        if self.is_result_ok(result):
            url = f"https://{CRAWLER_BUCKET_ID}.oss-jiaxing.sihe.cloud/{key}"
            return url
        return ""

    def put_public_obj(self, key, body, content_type):
        public_bucket = CRAWLER_BUCKET_ID
        result = self.client.put_object(
            Bucket=public_bucket,
            Body=body,
            Key=key,
            ContentType=content_type,
            ACL="public-read",
        )
        if not self.is_result_ok(result):
            raise Exception(f"Failed to put public object: {key}")
        return f"https://{CRAWLER_BUCKET_ID}.oss-jiaxing.sihe.cloud/{key}"

    def get_public_obj_bytes(self, key) -> bytes:
        public_bucket = CRAWLER_BUCKET_ID
        result = self.client.get_object(
            Bucket=public_bucket,
            Key=key,
        )
        if not self.is_result_ok(result):
            raise Exception(f"Failed to get public object: {key}")
        return result["Body"].read()

    def list_public_obj(self, path, delimiter=None):
        """列出公共存储桶中的对象

        Args:
            path: 对象路径前缀，如 "preset_cover/"
            delimiter: 分隔符，用于模拟文件夹结构，如 "/"

        Returns:
            S3 列表对象响应

        Raises:
            Exception: 列表获取失败时抛出异常
        """
        public_bucket = CRAWLER_BUCKET_ID
        try:
            params = {"Bucket": public_bucket, "Prefix": path}
            if delimiter:
                params["Delimiter"] = delimiter

            response = self.client.list_objects_v2(**params)
            return response
        except Exception as e:
            logger.error(
                f"Failed to list public objects with path {path}: {e}", exc_info=True
            )
            raise


if __name__ == "__main__":
    s3 = S3Client()
    result = s3.get_obj_list("user_2206/")
    with open("抖音新手入门全攻略.pdf", "rb") as file:
        f = file.read()
        result = s3.put_obj("user-14118/抖音新手入门全攻略.pdf", f)
    result = s3.download_obj("user-14118/抖音新手入门全攻略.pdf")
    print(s3.get_obj_download_url("user-14118/抖音新手入门全攻略.pdf"))
