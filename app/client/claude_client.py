from anthropic import Anthropic, AsyncAnthropic
import app.utils.video_utils as video_utils
from app.logger import logger
from app.config import GL<PERSON>BAL_CONF
from langfuse.openai import OpenAI, AsyncOpenAI


class ClaudeClient:
    def __init__(self, model_name: str, verbose: bool = False):
        self.model_name = model_name
        if ClaudeClient.use_openai_api():
            self.client = OpenAI(
                api_key=GLOBAL_CONF.ANTHROPIC_API_KEY,
                base_url=GLOBAL_CONF.ANTHROPIC_BASE_URL,
            )
            self.async_client = AsyncOpenAI(
                api_key=GLOBAL_CONF.ANTHROPIC_API_KEY,
                base_url=GLOBAL_CONF.ANTHROPIC_BASE_URL,
            )
        else:
            self.client = Anthropic(
                api_key=GLOBAL_CONF.ANTHROPIC_API_KEY,
                base_url=GLOBAL_CONF.ANTHROPIC_BASE_URL,
            )
            self.async_client = AsyncAnthropic(
                api_key=GLOBAL_CONF.ANTHROPIC_API_KEY,
                base_url=GL<PERSON>BAL_CONF.ANTHROPIC_BASE_URL,
            )
        self.verbose = verbose

    @staticmethod
    def use_openai_api() -> bool:
        return "zhizengzeng.com" in GLOBAL_CONF.ANTHROPIC_BASE_URL

    async def request_chat(self, prompt: str, images: list = []):
        messages = [
            {
                "role": "user",
                "content": [{"type": "text", "text": prompt}],
            },
            {
                # TODO：这个强制json的写法太诡异了
                # 预填充数据，确保claude返回json数据,ref:
                "role": "assistant",
                "content": "{",
            },
        ]

        for image in images:
            if ClaudeClient.use_openai_api():
                messages[0]["content"].append(
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "data:image/jpeg;base64,"
                            + video_utils.encode_image_to_base64(image),
                        },
                    }
                )
            else:
                messages[0]["content"].append(
                    {
                        "type": "image",
                        "source": {
                            "type": "base64",
                            "media_type": "image/jpeg",
                            "data": video_utils.encode_image_to_base64(image),
                        },
                    }
                )

        if self.verbose:
            logger.info(f"message:\n{messages[0]['content'][0]['text']}")

        logger.info(f"call claude")

        if ClaudeClient.use_openai_api():
            response = self.client.chat.completions.create(
                model=self.model_name, max_tokens=8192, temperature=0, messages=messages
            )
        else:
            response = self.client.messages.create(
                model=self.model_name, max_tokens=8192, temperature=0, messages=messages
            )

        logger.info(f"claude response:\n{response}")

        if ClaudeClient.use_openai_api():
            result = response.choices[0].message.content
        else:
            result = response.content[0].text

        # TODO：这个强制json的写法太诡异了
        if not result.startswith("{"):
            result = "{" + result

        if self.verbose:
            logger.info(f"[{self.model_name}] 返回的结果为:\n{result}")

        return result


if __name__ == "__main__":
    import asyncio
    import numpy as np

    async def test():
        claude_client = ClaudeClient(
            model_name="claude-3-5-sonnet-20240620",
            verbose=True,
        )
        image = np.zeros((5, 5, 3), dtype=np.uint8)
        # BGR通道，配置为蓝色
        image[:] = (255, 0, 0)
        await claude_client.request_chat(
            "请判断该图像是什么颜色的，以json格式回复。回复直接以 `{` 开头，不需要解释。",
            [image],
        )

        image = np.zeros((5, 5, 3), dtype=np.uint8)
        # BGR通道，配置为绿色
        image[:] = (0, 255, 0)
        await claude_client.request_chat(
            "请判断该图像是什么颜色的，以json格式回复。回复直接以 `{` 开头，不需要解释。",
            [image],
        )

    asyncio.run(test())
