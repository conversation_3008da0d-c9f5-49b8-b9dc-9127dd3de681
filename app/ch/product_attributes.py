import re
from functools import cache

from typing_extensions import List

from app.category_config import CategoryWhitelistConfig, CategoryInfo
from app.ch.model import DictProductAttributes
from app.ch.orm import ChClient

WINE_ATTRIBUTES_BLACK_LIST = [
    "浓香型",
    "参香型",
    "茶香型",
    "陈酿香",
    "陈酿型",
    "陈香",
    "陈香型",
    "纯粮兼香型",
    "纯粮浓香型",
    "纯粮清香型",
    "纯粮香",
    "醇家香",
    "醇酱香",
    "醇爽型",
    "醇香",
    "董香型",
    "凤香型",
    "凤香型纯粮",
    "馥合香型",
    "馥郁香",
    "馥郁香型",
    "高度清香型",
    "高香",
    "国井香型",
    "花果香",
    "槐花香",
    "兼香型",
    "兼香型白酒",
    "兼香型典范",
    "兼香型优级",
    "酱香",
    "酱香型",
    "酱香型白酒",
    "酱香型纯粮",
    "酱香型工艺",
    "酱香型国标",
    "酱香型酒",
    "酱香型口粮酒",
    "酱香型老酒",
    "酱香型名酒",
    "酱香型酿造",
    "酱香型优级",
    "焦糊香",
    "窖蒸香",
    "九粮香型",
    "酒清香",
    "酒香",
    "客香",
    "口三香",
    "老八大兼香型",
    "老白干香型",
    "老酱香",
    "老粮香",
    "老香型",
    "领香型曲酒",
    "茅香",
    "茅香香型",
    "米香型代表",
    "绵柔 浓香型",
    "绵柔凤香型",
    "绵柔浓香型",
    "绵柔香型",
    "绵柔型",
    "绵柔型白酒",
    "绵柔型纪念",
    "绵柔型酒",
    "绵柔型口感",
    "绵柔型酿造",
    "绵柔型浓香",
    "棉柔型",
    "明绿香型",
    "浓酱兼香型",
    "浓香型",
    "浓香型",
    "浓香型白酒",
    "浓香型发酵",
    "浓香型工艺",
    "浓香型酒",
    "浓香型老酒",
    "浓香型酿造",
    "浓香型酿制",
    "浓香型曲酒",
    "浓香型优级",
    "浓香型自调",
    "清香型",
    "清香型",
    "清香型白酒",
    "清香型发酵",
    "清香型国标",
    "清香型酒",
    "清香型粮食酒",
    "清香型优级",
    "清香型原浆",
    "清型出酒",
    "曲香",
    "柔醇香",
    "柔和型开创者",
    "柔雅型",
    "沙河香",
    "十里香",
    "水果香",
    "四香",
    "陶融香型",
    "特级酱香型",
    "特香",
    "特香型",
    "特香型标准样",
    "特香型开创者",
    "特香型老酒",
    "特香型优级",
    "甜香",
    "五粮浓香型",
    "五粮香",
    "五星棉柔型",
    "稀米香",
    "小窖绵柔型",
    "小窖绵柔型",
    "小曲清香型",
    "优级酱香型白酒",
    "优级清香型",
    "优级特香型",
    "元窖香",
    "元窖香型",
    "元窖香型",
    "元窖型",
    "原香",
    "匀香型",
    "糟香型",
    "真藏香型",
    "真酱香",
    "正宗酱香型",
    "芝麻香",
    "滋清香",
    "自然香",
    "醉酱香",
]

degree_re = re.compile(r"^\d{1,2}(\.\d+)?度?$")


@cache
def load_cmm_product_attributes() -> list[DictProductAttributes]:
    """
    查询产品属性词典,并按照 CategoryWhitelistConfig 过虑
        符合条件的产品属性记录列表
    """
    ch_client = ChClient()
    # 过滤掉 count < 10 的记录、
    # TODO: 要增加聚类标注流程，以支持查询发散的产品属性
    queryset = DictProductAttributes.objects_in(ch_client.dw_db).filter(
        DictProductAttributes.count >= 10
    )

    category_whitelist = CategoryWhitelistConfig.get_instance()

    result = []
    item_list: List[DictProductAttributes] = (
        queryset  # pyright: ignore [reportAssignmentType]
    )
    for item in item_list:
        category_info = CategoryInfo(
            category1=str(item.category1),
            category2=str(item.category2),
            category3=str(item.category3),
            category4=str(item.category4),
        )
        if not category_whitelist.is_category_in_whitelist(category_info):
            continue
        if category_info.category4 == "白酒":
            if item.key != "taste" and item.value in WINE_ATTRIBUTES_BLACK_LIST:
                # 过滤白酒中黑名单的属性
                continue
            if item.key != "degree" and degree_re.match(str(item.value)):
                # 过滤非度数属性中的度数信息
                continue
        result.append(item)
    return result
