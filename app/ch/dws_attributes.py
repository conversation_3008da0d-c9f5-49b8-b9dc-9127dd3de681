import json
from functools import cache
from typing import List, Optional, Any

from infi.clickhouse_orm import F
from app.logger import logger
from app.category_config import CategoryWhitelistConfig, CategoryInfo
from app.ch.model import (
    DwsAttributesMapping,
    DwsAttributesMappingWithCosineDistance,
    JSONExtractInt,
    JSONExtractString,
)
from app.ch.orm import ChClient


# 内容助手专用，用于获取视频属性
@cache
def load_dws_video_attributes() -> list[DwsAttributesMapping]:
    dw_db = ChClient().dw_db

    # 不返回 embedding_vector 。加速加载时间
    queryset = DwsAttributesMapping.objects_in(dw_db).only(
        DwsAttributesMapping.json_metadata,
        DwsAttributesMapping.mapped_text,
        DwsAttributesMapping.text,
        DwsAttributesMapping.text_type,
    )
    result = []
    category_config = CategoryWhitelistConfig()
    for item in queryset:
        try:
            json_metadata = json.loads(str(item.json_metadata))
        except json.JSONDecodeError:
            logger.error(
                f"json_metadata 解析失败: {item.text_type} {item.text} {item.json_metadata}"
            )
            continue
        video_category = CategoryInfo(
            category1=json_metadata.get("category1", ""),
            category2=json_metadata.get("category2", ""),
        )
        if video_category in category_config.video_category_v2_whitelist:
            result.append(item)
    return result


def search_dws_attributes(
    embedding: List[float],
    threshold: float = 0.96,
    limit=3,
    metadata_dict: Optional[dict[str, Any]] = None,
) -> List[DwsAttributesMappingWithCosineDistance]:
    dw_db = ChClient().dw_db

    qs = (
        DwsAttributesMapping.objects_in(dw_db)
        .aggregate(
            DwsAttributesMapping.text,
            DwsAttributesMapping.mapped_text,
            DwsAttributesMapping.json_metadata,
            cosine_distance=1
            - F("cosineDistance", DwsAttributesMapping.embedding_vector, embedding),
        )
        .filter(DwsAttributesMappingWithCosineDistance.cosine_distance > threshold)
        .order_by("-cosine_distance")[0:limit]
    )

    if metadata_dict:
        for k, v in metadata_dict.items():
            qs = qs.filter(
                JSONExtractString(DwsAttributesMapping.json_metadata, k) == str(v)
            )

    qs._grouping_fields = None  # pyright: ignore
    # print(qs.as_sql())

    return [item for item in qs]
