from typing import Optional

from app.ch.model import DimShop
from app.ch.orm import ChClient


class ShopClient:
    def __init__(self):
        self.ch_client = ChClient()

    def get_shop_by_name(self, name: str) -> Optional[DimShop]:
        queryset = DimShop.objects_in(self.ch_client.dw_db).filter(
            DimShop.shop_name == name
        )
        if queryset.count() == 0:
            return None
        return queryset[0]  # type: ignore
