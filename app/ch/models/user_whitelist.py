"""
用户白名单表模型
存储飞书同步的用户白名单数据
"""
from infi.clickhouse_orm import Model, StringField, DateTimeField, UInt8Field
from infi.clickhouse_orm.engines import ReplacingMergeTree


class DimUsersWhitelist(Model):
    """用户白名单表 - datawarehouse.dim_users_whitelist"""
    phone = StringField()                    # 手机号（用户标识）
    customer_name = StringField()            # 客户名称（看板显示用）
    added_by = StringField()                 # 添加人
    is_internal_user = UInt8Field()          # 是否内部人员（1=是，统计时排除）
    additional = StringField()               # 扩展信息JSON: {"add_date": "2025-01-01", "ecommerce_category": "服装", "content_category": "美妆", "reading_category": "小说"}
    created_at = DateTimeField()
    updated_at = DateTimeField()

    engine = ReplacingMergeTree(
        "updated_at",
        order_by=("phone",)
    )

    @classmethod
    def table_name(cls):
        return "dim_users_whitelist"