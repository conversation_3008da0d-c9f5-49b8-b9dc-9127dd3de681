"""
用户活动事件表模型
存储用户行为事件数据
"""
from infi.clickhouse_orm import Model, StringField, DateTimeField
from infi.clickhouse_orm.engines import MergeTree


class OdsUserActivityEvents(Model):
    """用户活动事件表 - datawarehouse.ods_user_activity_events"""
    event_time = DateTimeField()         # 事件发生时间，用于按日分区
    event_name = StringField()           # 事件名称（如：SEND_MESSAGE, EXPORT_PDF等），作为索引
    user_id = StringField()              # 用户ID（手机号）
    source = StringField()               # 事件来源：web（前端） | backend（后端）
    event_args = StringField()           # 事件参数JSON，存储具体业务数据
    user_agent = StringField()           # 用户代理信息
    client_ip = StringField()            # 客户端IP地址
    created_at = DateTimeField()         # 记录创建时间

    engine = MergeTree(
        date_col="event_time",
        order_by=("event_name", "event_time", "user_id"),
    )

    @classmethod
    def table_name(cls):
        return "ods_user_activity_events"
