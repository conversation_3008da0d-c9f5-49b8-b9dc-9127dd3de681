"""
事件类型注册机制
定义所有支持的用户活动事件类型
"""

from enum import Enum
from typing import Set


class ActivityEventType(str, Enum):
    """用户活动事件类型枚举"""

    # 后端事件（必须实现）
    HOUSEKEEPER_QUERY = "housekeeper:query"  # 助手查询事件
    HOUSEKEEPER_CONTENT_MODULE_PARAMS = (
        "housekeeper:content_module_params"  # 内容模块参数事件
    )

    # 登录相关
    SEND_CODE = "SEND_CODE"  # 发送验证码
    SUBMIT_LOGIN = "SUBMIT_LOGIN"  # 提交登录

    # 点击相关
    CLICK_VIDEO = "CLICK_VIDEO"  # 点击视频
    CLICK_BIAKE = "CLICK_BIAKE"  # 点击流联百科

    # 搜索相关
    SUBMIT_SEARCH = "SUBMIT_SEARCH"  # 提交搜索

    # 会话相关
    CREATE_CONVERSATION = "CREATE_CONVERSATION"  # 创建会话
    DELETE_CONVERSATION = "DELETE_CONVERSATION"  # 删除会话
    TOP_CONVERSATION = "TOP_CONVERSATION"  # 置顶会话

    # 会话记录相关
    SEND_MESSAGE = "SEND_MESSAGE"  # 发送消息（核心查询事件）
    COMMAND_MESSAGE = "COMMAND_MESSAGE"  # 发送命令行消息
    CLEAR_CONTEXT = "CLEAR_CONTEXT"  # 清除上下文
    VISIT_HISTORY = "VISIT_HISTORY"  # 访问会话历史

    # 文档相关
    CREATE_DOCUMENT = "CREATE_DOCUMENT"  # 创建文档
    RENAME_DOCUMENT = "RENAME_DOCUMENT"  # 重命名文档
    TRASH_DOCUMENT = "TRASH_DOCUMENT"  # 删除或恢复文档
    DELETE_DOCUMENT = "DELETE_DOCUMENT"  # 彻底删除文档
    CLEAR_DOCUMENT = "CLEAR_DOCUMENT"  # 清除文档
    REMOVE_DOCUMENT = "REMOVE_DOCUMENT"  # 彻底清除文档
    COPYLINK_DOCUMENT = "COPYLINK_DOCUMENT"  # 复制链接
    VISIT_DOCUMENT = "VISIT_DOCUMENT"  # 访问文档
    LEAVE_DOCUMENT = "LEAVE_DOCUMENT"  # 离开文档
    FAVORITE_DOCUMENT = "FAVORITE_DOCUMENT"  # 收藏文档
    EXPORT_PDF = "EXPORT_PDF"  # 导出PDF
    EXPORT_WORD = "EXPORT_WORD"  # 导出Word
    EXPORT_MARKDOWN = "EXPORT_MARKDOWN"  # 导出Markdown
    PUSH_DOCUMENT = "PUSH_DOCUMENT"  # 推送文档更新

    # 操作相关
    TOGGLE_SIDEBAR = "TOGGLE_SIDEBAR"  # 切换侧边栏
    TOGGLE_SPLITEVIEW = "TOGGLE_SPLITEVIEW"  # 切换子窗口


class EventValidator:
    """事件类型验证器"""

    @staticmethod
    def is_valid_event_type(event_name: str) -> bool:
        """验证事件类型是否有效"""
        try:
            ActivityEventType(event_name)
            return True
        except ValueError:
            return False

    @staticmethod
    def get_all_event_types() -> Set[str]:
        """获取所有支持的事件类型"""
        return {event.value for event in ActivityEventType}

    @staticmethod
    def get_backend_events() -> Set[str]:
        """获取后端事件类型"""
        return {
            ActivityEventType.HOUSEKEEPER_QUERY.value,
            ActivityEventType.HOUSEKEEPER_CONTENT_MODULE_PARAMS.value,
        }

    @staticmethod
    def get_frontend_events() -> Set[str]:
        """获取前端事件类型"""
        all_events = EventValidator.get_all_event_types()
        backend_events = EventValidator.get_backend_events()
        return all_events - backend_events
