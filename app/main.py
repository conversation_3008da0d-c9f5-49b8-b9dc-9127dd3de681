import os

from app.client.redis_client import redis_client

# 设置OMP_NUM_THREADS=1以禁用OpenMP多线程
os.environ["OMP_NUM_THREADS"] = "1"

from opentelemetry.instrumentation.threading import ThreadingInstrumentor

from app.env import SIHEGPT_STAGE

from app.service.feed_service import feed_service

# 让子线程继承当前线程的 trace context
# XXX: HousekeeperThread 是靠它是无效的，还是手动处理了。也许其他线程池可生效？
ThreadingInstrumentor().instrument()

import logging
import functools
from typing import Any, Callable, Coroutine

from fastapi.routing import APIRoute
import socketio
import socket
from anyio import CapacityLimiter
import anyio.to_thread
from anyio.lowlevel import RunVar
from fastapi import FastAPI, Request, Response, HTTPException, status
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from fastapi.responses import JSONResponse, PlainTextResponse
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from fastapi.staticfiles import StaticFiles
from fastapi_utilities import repeat_every, repeat_at
from matplotlib import font_manager, pyplot as plt
from opentelemetry import trace
from opentelemetry.context import set_value, attach, detach, get_current
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace import TracerProvider
from prometheus_fastapi_instrumentator import Instrumentator
from prometheus_client import push_to_gateway, REGISTRY
from prometheus_client.exposition import basic_auth_handler
from starlette.requests import ClientDisconnect

from app.config import GLOBAL_CONF
from app.db.database import SessionLocal
from app.executor import clean_up as clean_up_executors
from app.internal import admin, inner, pool
from app.logger import logger, handler
from app.middleware.api_logging import api_logging
from app.middleware.middleware import get_current_user_allow_unlogin
from app.response import BaseResponse
from app.routers import (
    chat,
    document,
    langfuse_endpoint,
    user,
    common,
    douyin,
    tools,
    cloud,
    social,
    metrics,
    task,
    evaluate,
    ai_writing,
    baike,
    baike_admin,
    rpa,
    charts,
    data_query,
    images,
    openai_compatible,
    feed,
    question_completion,
    entity,
    activity,
)
from app.routers.document_editor import (
    doc_socketio_path,
    sio as doc_sio,
    auto_merge_updates as doc_auto_merge,
)
from app.service.ner import ner_service
from app.utils.async_utils import to_sync
from app.utils.install_pre_commit import install_pre_commit
from app.conversation_processor.base_processor import active_counter

# 解决在 async 调用的 sync 方法中无法 asyncio.run 另一个 async 方法的问题
import nest_asyncio

nest_asyncio.apply()

# 设置 matplotlib 中文字体
current_dir = os.path.dirname(os.path.abspath(__file__))
font_path = os.path.join(current_dir, "../fonts/NotoSansSC-Regular.ttf")
font_manager.fontManager.addfont(font_path)
plt.rcParams["font.sans-serif"] = ["Noto Sans SC"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题


WEB_WORKER_THREAD_POOL_SIZE = 500


class ToSyncRoute(APIRoute):
    """将所有路由转换为同步路由，会在anyio的默认线程池中执行"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_route_handler(self) -> Callable[[Request], Coroutine[Any, Any, Response]]:
        original_handler = super().get_route_handler()
        return to_sync(original_handler)  # type: ignore


app = FastAPI(dependencies=[], docs_url=None, redoc_url=None)
app.router.route_class = ToSyncRoute
Instrumentator().instrument(app, latency_lowr_buckets=(0.1, 0.5, 1, 5, 10, 30, 60, 300))
app.mount("/static", StaticFiles(directory="static"), name="static")
# 控制 readiness 状态
is_ready = True

app.include_router(chat.router)
app.include_router(cloud.router)
app.include_router(common.router)
app.include_router(document.router)
app.mount(doc_socketio_path, socketio.ASGIApp(doc_sio, socketio_path=doc_socketio_path))
app.include_router(douyin.router)
app.include_router(social.router)
app.include_router(tools.router)
app.include_router(user.router)
app.include_router(inner.router)
app.include_router(metrics.router)
app.include_router(evaluate.router)
app.include_router(ai_writing.router)
app.include_router(baike.router)
app.include_router(baike_admin.router)
app.include_router(rpa.router)
app.mount(
    rpa.rpa_socketio_path,
    socketio.ASGIApp(rpa.sio, socketio_path=rpa.rpa_socketio_path),
)
app.include_router(charts.router)
app.include_router(data_query.router)
app.include_router(images.router)

app.include_router(admin.router)
app.include_router(task.router)
app.include_router(pool.router)
app.include_router(openai_compatible.router)
app.include_router(langfuse_endpoint.router)
app.include_router(feed.router)
app.include_router(question_completion.router)
app.include_router(entity.router)
app.include_router(activity.router)


trace.set_tracer_provider(
    TracerProvider(resource=Resource.create({"service.name": "sihegpt"}))
)


# 在程序启动时自动执行后台任务
@app.on_event("startup")
async def start_background_tasks():
    logger = logging.getLogger("uvicorn.access")
    logger.addHandler(handler)
    size = GLOBAL_CONF.FASTAPI_THREAD_POOL_SIZE
    rv = RunVar("_default_thread_limiter")
    rv.set(CapacityLimiter(size))
    print(f"set fastapi thread pool size: {size}")

    # 在非阻塞的情况下尝试安装 pre-commit
    import threading

    threading.Thread(target=install_pre_commit, daemon=True).start()

    # recommender_executor.submit(recommender.run_in_time)


@app.on_event("startup")
@repeat_every(seconds=60)
def document_cron_job():
    try:
        doc_auto_merge()
    except Exception as e:
        logger.warning(f"doc_auto_merge failed {e}, retry next turn")
    except:
        logger.error(f"doc_auto_merge failed", exc_info=True)


@app.on_event("startup")
@repeat_every(seconds=20)
def push_metrics():
    try:
        assert GLOBAL_CONF.PUSH_GATEWAY_ADDRESS
        stage = SIHEGPT_STAGE.name.lower()
        push_to_gateway(
            GLOBAL_CONF.PUSH_GATEWAY_ADDRESS,
            job=f"sihegpt-{stage}",  # 任务名称
            grouping_key={
                "instance": f"{socket.gethostname()}:{os.getpid()}",
            },
            registry=REGISTRY,  # 使用默认的 Registry
            handler=functools.partial(
                basic_auth_handler,
                username=GLOBAL_CONF.PUSH_GATEWAY_USERNAME,
                password=GLOBAL_CONF.PUSH_GATEWAY_PASSWORD,
            ),
        )
        logger.info(f"pushed {stage} metrics to {GLOBAL_CONF.PUSH_GATEWAY_ADDRESS}")
    except Exception as e:
        logger.error(f"push metrics failed {e}")


# 每分钟检查一次信息流推送
@app.on_event("startup")
@repeat_at(cron="* * * * *")
def push_cards_periodic():
    if SIHEGPT_STAGE.name not in ["STAGING", "PROD"]:
        return
    try:
        with redis_client.lock("lock:feed:push-periodic", 300):
            feed_service.push_cards()
    except RuntimeError as e:
        # lock timeout
        return
    except Exception as e:
        logger.error(f"push cards failed {e}")


@app.on_event("shutdown")
async def shutdown_event():
    clean_up_executors()
    logger.info("wait for processor completion")
    await active_counter.aswait_until_idle()  # type: ignore
    logger.info("all processors are complete")
    os._exit(0)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    msg = f"请求参数格式错误：{exc.errors()}"
    return JSONResponse(
        status_code=400,
        content=BaseResponse(code=400, msg=msg).model_dump(),
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        headers=exc.headers,
        content=BaseResponse(code=exc.status_code, msg=exc.detail).model_dump(),
    )


@app.middleware("http")
async def db_session_middleware(request: Request, call_next):
    try:
        request.state.db = SessionLocal()
        # TODO(kevin): fix this
        request.state.user_id = 1
        return await call_next(request)
    except HTTPException as e:
        return await http_exception_handler(request, e)
    except Exception as e:
        msg, exc_info = "服务代码执行异常", True
        if isinstance(e, ResponseValidationError):
            msg, exc_info = "应答数据格式错误", False
        logger.error(f"{msg}: {e}", exc_info=exc_info)
        return JSONResponse(
            status_code=500,
            content=BaseResponse(code=500, msg=msg).model_dump(),
        )
    finally:
        request.state.db.close()


@app.middleware("http")
async def api_logging_middleware(request: Request, call_next):
    return await api_logging(request, call_next)


def get_remote_client_host(request: Request):
    for key in [
        "X-Forwarded-For",
        "X-Real-IP",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_CLIENT_IP",
    ]:
        if request.headers.get(key):
            return request.headers.get(key)

    return request.client.host  # type: ignore


@app.middleware("http")
async def set_extra_fields_in_ctx_middleware(request: Request, call_next):
    user_id: int = await get_current_user_allow_unlogin(request)
    http_request_uri = request.url.path
    http_client_ip = get_remote_client_host(request)
    device_id = request.headers.get("x-device-id")

    ctx = get_current()
    ctx = set_value("user_id", user_id, ctx)
    ctx = set_value("http_request_uri", http_request_uri, ctx)
    ctx = set_value("http_client_ip", http_client_ip, ctx)
    ctx = set_value("device_id", device_id, ctx)
    token = attach(ctx)
    try:
        return await call_next(request)
    finally:
        detach(token)


# A preflight request is an OPTIONS request with an Origin header.
def is_preflight_request(request):
    return (
        request.headers.get("Origin") is not None
        and request.method.upper() == "OPTIONS"
    )


# 判断是否是 会产生 network error 异常的操作系统类型，主要是 ios12 ,和老的 安卓机型 (6~9)
# 这些操作系统，不支持 Access-Control-Allow-Origin: *， 因此需要手动添加header白名单
def is_network_error_os(user_agent):
    return (
        "iPhone OS 12" in user_agent
        or "Android 6" in user_agent
        or "Android 7" in user_agent
        or "Android 8" in user_agent
        or "Android 9" in user_agent
    )


@app.middleware("http")
async def tracing_response_middleware(request: Request, call_next):
    try:
        response: Response = await call_next(request)
    except ClientDisconnect:
        raise HTTPException(status_code=499, detail="Client disconnected")
    # 为了方便调试，将 trace_id 和 span_id 放到 response header 中
    current_span = trace.get_current_span()
    if current_span:
        trace_id = current_span.get_span_context().trace_id
        span_id = current_span.get_span_context().span_id
        response.headers["X-B3-TraceId"] = format(trace_id, "032x")
        response.headers["X-B3-SpanId"] = format(span_id, "016x")

    # 全局增加跨域支持
    if not request.headers.get("Origin"):
        return response

    if is_preflight_request(request):
        response.headers["Access-Control-Allow-Credentials"] = "true"
        response.headers["Access-Control-Allow-Methods"] = (
            "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH"
        )
    response.headers["Access-Control-Expose-Headers"] = ", ".join(
        ["Connection", "X-Accel-Buffering", "X-B3-TraceId", "X-B3-SpanId"]
    )

    if "User-Agent" in request.headers and is_network_error_os(
        request.headers["User-Agent"]
    ):
        allow_headers = (
            "X-QM-System-Type, "
            "X-QM-System-Version, "
            "X-QM-APP-Version, "
            "X-QM-APP-Channel, "
            "User-Agent, "
            "IP, "
            "X-QM-H5-Source, "
            "X-QM-Device-FingerPrint, "
            "X-QM-ViewPage, "
            "X-QM-Inviter-Id, "
            "content-type, "
            "authorization"
        )
        allow_origin = request.headers.get("Origin")
        response.headers["Access-Control-Allow-Headers"] = allow_headers
        if allow_origin:
            response.headers["Access-Control-Allow-Origin"] = allow_origin
    else:
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Headers"] = "*"

    return response


# 这一行要放在 middleware 后面，否则 middleware 中的 trace_id/span_id 会是 0
FastAPIInstrumentor.instrument_app(app)


# 处理所有 OPTIONS 请求
@app.options("/{path_name:path}")
async def handle_options(path_name: str):
    return PlainTextResponse("ok")


@app.get("/")
async def root_handler():
    # not ready时返回5xx错误码，使k8s判断为NotReady。
    return JSONResponse(
        status_code=200 if is_ready else 503,
        content={"message": "Hello Bigger Applications!", "is_ready": is_ready},
    )


@app.post("/shutdown-prepare")
def mark_not_ready():
    global is_ready
    is_ready = False
    logger.info("Marked as NotReady")
    return {"status": "marked not ready"}


@app.get("/test/sleep100")
async def sleep100():
    import time

    time.sleep(100)
    return {"message": "sleep 100"}


@app.post("/app/log", summary="客户端日志收集接口")
async def upload_app_log(request: Request):
    # 限制请求大小为 1MB
    max_size = 1 * 1024 * 1024
    content_length = request.headers.get("content-length")
    if content_length is None or int(content_length) > max_size:
        raise HTTPException(status_code=413, detail="Request Entity Too Large")

    # 打印 payload 即可收集
    payload = await request.body()
    logger.info(payload.decode("utf-8"))

    return PlainTextResponse("Log received", status_code=200)


security = HTTPBasic()
username = "sihe"
password = "sih365536!"


@app.middleware("http")
async def basic_auth_middleware(request: Request, call_next):
    # 需要认证的路径（这些页面均需要认证）
    protected_paths = {"/docs", "/openapi.json", "/redoc", "/metrics"}

    if request.url.path in protected_paths:
        try:
            credentials: HTTPBasicCredentials | None = await security(request)
            if credentials is None or not (
                credentials.username == username and credentials.password == password
            ):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials",
                    headers={"WWW-Authenticate": "Basic"},
                )
        except HTTPException as e:
            # 捕获认证失败的异常并返回适当的响应
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail},
                headers=e.headers,
            )

    try:
        response = await call_next(request)
    except Exception as e:
        logger.error(
            f"Error in basic_auth_middleware: {e}, request: {request.url.path}",
            exc_info=True,
        )
        raise e
    return response


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,  # type: ignore
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )


@app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)  # type: ignore
async def swagger_ui_redirect():
    return get_swagger_ui_oauth2_redirect_html()


@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    return get_redoc_html(
        openapi_url=app.openapi_url,  # type: ignore
        title=app.title + " - ReDoc",
        redoc_js_url="/static/redoc.standalone.js",
    )


@app.on_event("startup")
def es_update_worker():
    # 启动ES更新工作进程
    from app.service.document_service import start_es_update_workers

    start_es_update_workers(num_workers=1)

    # 初始化 NerService - 在STAGING和PROD环境中预热，其他环境懒加载
    if SIHEGPT_STAGE.name in ["STAGING", "PROD"]:
        ner_service.get_ner_service()

    # 初始化 content_modules.attribution_analysis 模型
    from app.service.content_modules.attribution_analysis.model.model_loader import (
        get_attribution_model,
    )

    get_attribution_model()


@app.on_event("startup")
async def configure_threadpool_size():
    # 配置线程池为500线程（同步后台任务，默认为40）
    anyio.to_thread.current_default_thread_limiter().total_tokens = (
        WEB_WORKER_THREAD_POOL_SIZE
    )


def run_app():
    print("list all env vars", flush=True)
    for key, value in sorted(os.environ.items()):
        print(f"env {key}: {value}", flush=True)
    import uvicorn

    server_ip = os.getenv("SIHEGPT_LOCAL_SERVER_HOST", "0.0.0.0")
    server_port = int(os.getenv("SIHEGPT_LOCAL_SERVER_PORT", 8081))
    uvicorn.run(app, host=server_ip, port=server_port)


if __name__ == "__main__":
    run_app()
