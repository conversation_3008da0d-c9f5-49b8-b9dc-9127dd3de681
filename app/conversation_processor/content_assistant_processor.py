import json
import time
import uuid
from pathlib import Path

from fastapi import UploadFile, BackgroundTasks
from langfuse.decorators import observe
from langgraph.types import Command

from app.client.openai_client_v2 import ToolCall
from app.client.prompts import TEMPLATES
from app.client.s3client import S3<PERSON>lient, USER_BUCKET_ID, CRAWLER_BUCKET_ID
from app.config import ASSISTANT_DATA, S3_URL_EXPIRATION_TIME, KNOWLEDGE_BASE_NAME
from app.conversation_processor.base_processor import BaseProcessor
from app.conversation_processor.tool_functions import ChoiceQuestion
from app.conversation_processor.utils import upload_user_file_to_s3
from app.db.common_type import ContentType, ChatStatus, TaskType
from app.db.models import ChatEntity
from app.db.repository import (
    chat_repository,
    chat_conversation_repository,
    task_repository,
)
from app.logger import logger
from app.conversation_type import CONTENT_ASSISTANT_INIT_CONTENT
from app.routers.common import get_user_path
from app.service import disassemble_service
from app.service.common_service import RateLimiter
from app.service.content_assistant.assistant import (
    new_content_assistant_graph,
)
from app.service.content_assistant.video_imitate import storyboards_to_kling_prompt
from app.service.search_agent.utils import Question
from app.utils import langfuse_utils, tasks_utils

s3client = S3Client()


class ContentAssistantProcessor(BaseProcessor):
    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        # content 是用户这一次输入的消息
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        # background_tasks 是 fastapi 的组件，用于如：上传文件内容助手进行 RAG ，视频拆解，问 GPT
        background_tasks: BackgroundTasks,
        # knowledge_base_name 可以不用
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
    ):
        super().__init__(
            user_id,
            conversation_id,
            content,
            content_type,
            content_json,
            ref_chat_id,
            upload_file,
            background_tasks,
            knowledge_base_name,
        )
        self.related_video = None

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return CONTENT_ASSISTANT_INIT_CONTENT, []

    def pre_process(self):
        if self.upload_file:
            file_path = f"{ASSISTANT_DATA}/{uuid.uuid4()}.mp4"
            url = upload_user_file_to_s3(self.user_id, file_path, self.upload_file.file)
            # 这些字段必须要设置，否则前端无法预览上传的视频
            self.content_json["file_size"] = self.upload_file.size
            self.content_json["video_url"] = url
            self.content_json["video_path"] = file_path
            self.content_json["video_url_expiration_time"] = (
                time.time() + S3_URL_EXPIRATION_TIME
            )
            # self.svideo_path = svideo_path

    def show_progress_default(self) -> bool:
        return False

    async def do_reply(self):
        update_chat = chat_repository.updater(self.user_id, self.conversation_id)
        try:
            if "from_option" in self.content_json and self.content_json["from_option"]:
                if "可灵" in self.content:
                    self.export_last_imitation_storyboards_to_kling(update_chat)
                    return
            await self._do_reply(update_chat)
        except Exception as e:
            logger.warning(e, exc_info=True)
            update_chat(
                self.response_chat_id,
                content="出错了，请稍候重试。。。",
                progress=1,
                status=ChatStatus.error,
            )

    # 将上一次仿拍的分镜头脚本转换成可灵格式，这里是临时实现方案
    def export_last_imitation_storyboards_to_kling(self, update_chat):
        chats = chat_repository.get_latest_messages(
            self.user_id,
            self.conversation_id,
            0,
            limit=3,
            order_desc=True,
            include_hidden=True,
        )
        if (
            len(chats) < 3
            or "imitate" not in chats[2].content_json
            or "storyboards" not in chats[2].content_json["imitate"]
        ):
            raise Exception("没有找到拍同款报告")
        storyboards = chats[2].content_json["imitate"]["storyboards"]
        response_content = storyboards_to_kling_prompt(storyboards)
        update_chat(
            self.response_chat_id,
            content=response_content,
            progress=1,
            status=ChatStatus.done,
        )

    async def disassemble_video(self, update_chat):
        video_path = None
        video_title = ""
        content_type = "视频"
        if "link_previews" in self.chat.content_json:
            for _, preview in self.chat.content_json["link_previews"].items():
                if "video_id" in preview:
                    video_path = f"videodata/{preview['video_id']}.mp4"
                    video_title = preview["title"]
                    break
                # 图文类型
                if "og_type" in preview and preview["og_type"] == "article":
                    video_path = preview["image"]
                    video_title = preview["title"]
                    content_type = "图文"
                    break

        if "video_path" in self.content_json:
            video_path = self.content_json["video_path"]

        if video_path is None and content_type != "图文":
            return None

        if content_type == "视频":
            user_path = (
                None
                if video_path.startswith("videodata")
                else get_user_path(self.user_id)
            )
            bucket = USER_BUCKET_ID if user_path else CRAWLER_BUCKET_ID
            video_filepath = s3client.download_obj(
                filepath=video_path, user_path=user_path, bucket=bucket
            )
        update_chat(
            self.response_chat_id,
            content=f"正在分析 {content_type} <loading-img-1></loading-img-1>",
            progress=0,
        )
        await disassemble_service.disassemble_file(
            self.user_id,
            self.conversation_id,
            svideo_path=video_path,
            chat=self.chat,
            response_chat_id=self.response_chat_id,
            file_path=Path(video_filepath) if content_type != "图文" else None,
        )

        disassemble_task = task_repository.get_disassemble_task_by_video_path(
            video_path, self.user_id
        )
        disassemble_output = tasks_utils.convert_video_summarize_report_to_markdown(
            disassemble_task.report
        )
        video = {
            "title": video_title,
            "oss_url": video_path,
            "subtitles": disassemble_task.subtitles or [],
            "disassemble_url": disassemble_task.affine_url,
            "disassemble_output": disassemble_output,
        }
        return video

    @observe(name="content_assistant")
    async def _do_reply(self, update_chat):
        conversation = chat_conversation_repository.get_conversation(
            self.user_id, self.conversation_id
        )
        resume = (
            conversation.langgraph_thread_id is not None
            and conversation.langgraph_thread_id != ""
        )
        langgraph_thread_id = (
            conversation.langgraph_thread_id if resume else self.response_chat_id
        )

        video = await self.disassemble_video(update_chat)

        if resume:
            messages = [self.content] if self.content else []
        else:
            messages = self.get_message_list()

        graph_config = {
            "configurable": {
                "conversation_id": self.conversation_id,
                "thread_id": langgraph_thread_id,
                "user_id": self.user_id,
            },
        }

        if self.content_type == ContentType.decision:
            graph_input = Command(resume=self.content_json["decision"])
        elif video is not None:
            if resume:
                graph_input = Command(resume=video)
            else:
                graph_input = {
                    # "user_input": self.content,
                    "messages": messages,
                    "video": video,
                }
        else:
            if resume:
                graph_input = Command(resume=self.content)
            else:
                graph_input = {
                    # "user_input": self.content,
                    "messages": messages,
                    "video": self.related_video,
                }

        try:
            last_message = None
            imitate_result = None
            limiter = RateLimiter(0.2)
            for stream_mode, event in new_content_assistant_graph().stream(
                graph_input, graph_config, stream_mode=["updates", "custom"]
            ):
                if stream_mode == "custom":
                    if "videos" in event:
                        update_chat(
                            self.response_chat_id,
                            videos=event["videos"],
                        )
                    if "content" in event:
                        limiter.set(event["content"])
                        value = limiter.get()
                        if value:
                            update_chat(
                                self.response_chat_id,
                                content=value,
                            )
                    continue
                if "__interrupt__" in event:
                    content = event["__interrupt__"][0].value
                    if isinstance(content, Question):
                        choice_question = ChoiceQuestion(
                            question=content.question_message,
                            options=[
                                opt
                                for opt in content.options
                                if not opt.startswith("其他")
                            ],
                            is_single_choice=False,
                            allow_other=True,
                        )
                        update_chat(
                            self.response_chat_id,
                            content_type=ContentType.choice,
                            content_json={
                                "choice": choice_question.to_json(),
                                "following_questions": [],
                            },
                            status=ChatStatus.done,
                        )
                    else:
                        update_chat(
                            self.response_chat_id,
                            content=event["__interrupt__"][0].value,
                            status=ChatStatus.done,
                        )
                    chat_conversation_repository.update_conversation_langgraph_thread_id(
                        self.user_id, self.conversation_id, langgraph_thread_id
                    )
                    return
                for k in event.keys():
                    value = event[k]
                    if value is None:
                        continue
                    if "imitate_storyboards" in value:
                        imitate_result = value["imitate_storyboards"] | {
                            "report_url": value["imitate_report_url"]
                        }
                    if "messages" in value:
                        last_message = value["messages"][-1]
            if imitate_result is not None:
                content_json = {"imitate": imitate_result}
                update_chat(
                    self.response_chat_id,
                    content=last_message.content if last_message else "处理完成",
                    content_json=content_json,
                    status=ChatStatus.done,
                    options=["重新仿拍", "导出「可灵 AI」格式"],
                )
            else:
                content = last_message.content if last_message else "处理完成"
                if content.startswith("Error:"):
                    logger.error(content)
                    update_chat(
                        self.response_chat_id,
                        content="出错了，请稍后再试。。。",
                        status=ChatStatus.error,
                    )
                else:
                    update_chat(
                        self.response_chat_id,
                        content=content,
                        status=ChatStatus.done,
                    )
            chat_conversation_repository.update_conversation_langgraph_thread_id(
                self.user_id, self.conversation_id, ""
            )
            if imitate_result is None:
                await self.generate_response_options(messages)

        except Exception as e:
            logger.warning(e, exc_info=True)
            update_chat(
                self.response_chat_id,
                content="处理失败",
                status=ChatStatus.error,
            )
            chat_conversation_repository.update_conversation_langgraph_thread_id(
                self.user_id, self.conversation_id, ""
            )

    async def generate_response_options(self, messages):
        response_chat = chat_repository.get_chat(self.response_chat_id)
        if response_chat.options:
            return
        chat = chat_repository.get_chat(self.chat.chat_id)
        if (
            chat.content_json.get("task_type") == TaskType.select
            and response_chat.status == ChatStatus.done  # 回答的状态
            and (
                not chat.content
                or (
                    len(chat.content_json.get("link_previews", [])) == 1
                    and "需求" in response_chat.content
                )
            )
        ):
            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                options=[
                    "帮我提取台词",
                    "获取视频拆解报告",
                    "仿拍该视频",
                ],
            )
            return
        await super().generate_response_options(messages)

    def get_response_processing_content(self) -> (str, ContentType):
        return "正在处理 <loading-img-1></loading-img-1>", ContentType.normal

    def get_video_from_chat_content_json(self, content_json):
        video_title = ""
        video_path = ""
        if "video_path" in content_json:
            video_path = content_json["video_path"]
        elif "link_previews" in content_json:
            for _, preview in content_json["link_previews"].items():
                if "video_id" in preview:
                    video_path = f"videodata/{preview['video_id']}.mp4"
                    video_title = preview["title"]
                    break
                if "og_type" in preview and preview["og_type"] == "article":
                    video_path = preview["image"]
                    video_title = preview["title"]
                    break

        disassemble_task = task_repository.get_disassemble_task_by_video_path(
            video_path, self.user_id
        )
        disassemble_output = tasks_utils.convert_video_summarize_report_to_markdown(
            disassemble_task.report
        )
        return {
            "title": video_title,
            "oss_url": video_path,
            "subtitles": disassemble_task.subtitles or [],
            "disassemble_url": disassemble_task.affine_url,
            "disassemble_output": disassemble_output,
        }

    def get_top_chat(self) -> ChatEntity | None:
        top_chat = None
        # 先找到最近一条包含拆解报告的消息
        task_chats = (
            chat_repository.get_chat_list_by_conversation_id_and_task_id_not_null(
                self.conversation_id
            )
        )
        for c in task_chats:
            if "task_type" not in c.content_json:
                continue
            if c.content_json["task_type"] == TaskType.select:
                top_chat = c
                self.related_video = self.get_video_from_chat_content_json(
                    c.content_json
                )
                break
        if not top_chat:
            return None
        # 找一下这之后还有没有被引用的拆解报告
        ref_chats = chat_repository.get_chat_list_by_conversation_id_and_ref_chat_id_not_null_and_create_time_after(
            self.conversation_id,
            top_chat.create_time,
        )
        for c in ref_chats:
            if "task_type" in c.ref_chat and c.ref_chat["task_type"] == TaskType.select:
                top_chat = c
                self.related_video = self.get_video_from_chat_content_json(c.ref_chat)
                break
        return top_chat
