import datetime
from abc import ABC, abstractmethod
from typing import Callable
import time
import socket
from contextlib import contextmanager

from fastapi import UploadFile, BackgroundTasks, HTTPException
from langfuse.decorators import langfuse_context, observe
from openai import NotGiven, NOT_GIVEN
from opentelemetry import trace
import asyncio

from pydantic import BaseModel

from app.client.openai_client import fast_llm, long_context_llm
from app.client.openai_client import MyOpenAiLLMClient
from app.client.openai_client import OpenA<PERSON>lient
from app.client.openai_client_v2 import auto_chat, ToolCall
from app.client.redis_client import RedisEvent
from app.config import KNOWLEDGE_BASE_NAME, PRODUCT_KB_NAME
from langchain_core.messages import BaseMessage, AIMessage, SystemMessage
from langchain_core.messages.utils import convert_to_messages
from app.client.prompts import TEMPLATES
from app.conversation_processor.chat_convertor import (
    convert_chat_to_messages,
    convert_chats_to_messages,
)
from app.conversation_processor.tool_functions import ChoiceQuestion
from app.db.common_type import (
    SenderType,
    ContentType,
    ChatStatus,
    Chat<PERSON>ommand,
    ChatType,
    ChatVoting,
)
from app.db.models import ChatEntity, TasksEntity
from app.db.vo import Chat as ChatVo
from app.db.repository import (
    chat_repository,
    task_repository,
    chat_conversation_repository,
)
from app.logger import logger, exception_logger
from app.service import document_service
from app.service.common_service import RateLimiter
from app.service.voting_service import record_voting
from app.utils.prometheus_metrics import inc_copy_affine_docs_counter
from app.utils.async_utils import to_sync, to_async
from app.utils.link_utils import replace_links, find_all_links
from app.utils.langfuse_utils import delete_score
from app.utils.langfuse_utils import get_prompt
from app.service.abort_manager import get_abort_manager
from app.service.crawler.xiaohongshu_notes_crawler import (
    handle_xiaohongshu_abort,
)

openai_client = OpenAIClient()


# 记录进行中的活跃任务数，仅当活跃数为0时，允许shutdown。
# 同一host的多个uvicorn worker共享同一个redis counter。
class ActiveCounter:
    def __init__(self):
        self.host_id = socket.gethostname()
        self.redis_event = RedisEvent("active-counter-event")
        self.counter_key = f"active-counter:{self.host_id}"
        self.redis_client = self.redis_event.redis_client
        self.counter_expires = 5 * 60  # 5min

    @contextmanager
    def counter_manager(self):
        try:
            # increase counter by 1
            self.redis_client.get_client().incr(self.counter_key)
            self.redis_client.get_client().expire(
                self.counter_key, self.counter_expires
            )
            yield
        finally:
            # decrease counter by 1, send event if counter is 0
            counter = int(self.redis_client.get_client().decr(self.counter_key))  # type: ignore
            if counter <= 0:
                # 如果计数器小于等于0，确保计数器为0并发送事件
                if counter < 0:
                    self.redis_client.get_client().set(self.counter_key, 0)
                self.redis_event.set(self.host_id, "-", "idle")
                logger.info(f"send {self.counter_key} event")
            else:
                self.redis_client.get_client().expire(
                    self.counter_key, self.counter_expires
                )

    def wait_until_idle(self, timeout: float | None = None) -> bool:
        # 如果counter为0则直接返回，否则等待event
        counter = self.redis_client.get_client().get(self.counter_key)
        if not counter or int(counter) <= 0:  # type: ignore
            logger.info(f"{self.counter_key} is idle, skip event: {counter}")
            return True

        # 等待计数器变为0的事件
        with self.redis_event.subscriber(self.host_id, "") as subscriber:
            res = subscriber.wait(timeout)
            logger.info(f"receive {self.counter_key} event")
            return res is not None

    aswait_until_idle = to_async(wait_until_idle)


active_counter = ActiveCounter()


# 每次请求创建一个 processor 子类实例
class BaseProcessor(ABC):
    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        # content 是用户这一次输入的消息
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        # background_tasks 是 fastapi 的组件，用于如：上传文件内容助手进行 RAG ，视频拆解，问 GPT
        background_tasks: BackgroundTasks,
        # knowledge_base_name 可以不用
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
    ):
        self.chat: ChatEntity | None = None
        self.response_chat_id = None
        self.user_id = user_id
        self.conversation_id = conversation_id
        self.content = content
        self.content_type = ContentType(content_type)
        self.content_json = content_json or {}
        self.ref_chat_id = ref_chat_id
        self.options = []
        self.upload_file = upload_file
        self.background_tasks = background_tasks
        self.knowledge_base_name = knowledge_base_name
        self.chat_command = self.content_json.get("command", "")
        self.at_list = self.content_json.get("at_list", [])
        self.latest_chat = chat_repository.get_latest_chat_by_conversation_id(
            self.user_id, self.conversation_id
        )
        self.__update_ref_chat()
        self.__load_prev_chat()

        # tool call 注册的 hook，例如给消息附加 videos/docs，在 package response content 之前调用
        self.before_package_response_content_hooks = []

    def get_at_list(self) -> list[dict]:
        return self.at_list

    def register_package_response_content_hook(self, f: Callable):
        self.before_package_response_content_hooks.append(f)

    def __update_ref_chat(self):
        self.task_id = None
        self.ref_chat = {}
        if not self.ref_chat_id:
            return
        if self.content_type in [ContentType.decision, ContentType.command]:
            return

        ref_chat = chat_repository.get_chat(self.ref_chat_id)
        if not ref_chat:
            logger.error(f"no chat found for {self.ref_chat_id}")
            return
        self.task_id = ref_chat.task_id

        # 与最近的聊天项对比
        if not self.latest_chat:
            logger.error(
                f"found no chat history for {self.user_id}:{self.conversation_id}"
            )
            return
        ref_adjacent = self.ref_chat_id == self.latest_chat.chat_id
        if ref_adjacent and self.content_json.get("from_option", False):
            # 从临近消息的option点选，不添加ref_chat
            return
        self.ref_chat = {
            "content": ref_chat.content,
            "sender_type": ref_chat.sender_type,
            "content_json": ref_chat.content_json,
            "ref_adjacent": ref_adjacent,
        }
        if ref_chat.content_json and "decision" in ref_chat.content_json:
            self.ref_chat["decision"] = ref_chat.content_json["decision"]
        if ref_chat.task_id:
            task = task_repository.get_task(ref_chat.task_id, self.user_id)
            if not task:
                logger.error(
                    f"no task found for {ref_chat.task_id} for chat {self.ref_chat_id}"
                )
                return
            self.ref_chat["task_id"] = task.task_id
            self.ref_chat["task_type"] = task.task_type
            self.ref_chat["video_path"] = task.svideo_path

    def __load_prev_chat(self):
        if self.content_type != ContentType.command:
            return
        if self.chat_command == ChatCommand.voting:
            if not self.ref_chat_id:
                raise HTTPException(status_code=400, detail=f"需要提供 ref_chat_id")
            self.chat = chat_repository.get_chat(chat_id=self.ref_chat_id)  # type: ignore
            if not self.chat:
                raise HTTPException(status_code=404, detail=f"无效的 ref_chat_id")
            if (
                self.chat.status != ChatStatus.done
                or self.chat.sender_type != SenderType.agent
                or not self.chat.trace_id
            ):
                raise HTTPException(status_code=400, detail=f"该消息不支持点赞/点踩")
            return
        if self.chat_command not in [ChatCommand.retry, ChatCommand.abort]:
            raise HTTPException(
                status_code=400, detail=f"命令不支持: {self.chat_command}"
            )
        if (
            not self.latest_chat
            or self.latest_chat.sender_type != SenderType.agent
            or self.latest_chat.content_type == ContentType.init
        ):
            raise HTTPException(status_code=400, detail="无应答信息")
        if self.ref_chat_id and self.ref_chat_id != self.latest_chat.chat_id:
            raise HTTPException(status_code=400, detail="只支持处理最近的回答")

        # 加载前续消息的content等信息，用于pre_process等处理
        prev_chat = chat_repository.get_latest_chat_by_conversation_id(
            self.user_id, self.conversation_id, index=1
        )
        if not prev_chat:
            raise HTTPException(status_code=400, detail="首条消息不能重新生成")
        self.content = prev_chat.content
        self.content_json = prev_chat.content_json or {}
        # content_type不能修改
        self.chat = prev_chat

    def abort_user_message(self):
        if not self.latest_chat:
            return False
        time_diff = datetime.datetime.now() - self.latest_chat.create_time
        if self.latest_chat.status == ChatStatus.ongoing and time_diff.seconds < 5 * 60:
            return True
        return False

    # 外部入口
    @observe(name="process_message", capture_input=False, capture_output=True)
    def process_message(self, **kwargs) -> ChatEntity | None:
        langfuse_context.update_current_trace(
            user_id=str(self.user_id),
            session_id=self.conversation_id,
            metadata={
                "content": self.content,
                "content_type": self.content_type,
                "content_json": self.content_json,
                "ref_chat_id": self.ref_chat_id,
                "upload_file": self.upload_file,
            },
        )
        abort_command = (
            self.content_type == ContentType.command
            and self.chat_command == ChatCommand.abort
        )
        clear_context_message = self.content_type == ContentType.clear_context
        if (
            not abort_command
            and not clear_context_message
            and self.abort_user_message()
        ):
            raise HTTPException(
                status_code=400, detail="正在处理上一个请求，请稍后再试。"
            )
        self.pre_process()
        if self.content_type == ContentType.decision:
            self.chat = self.__process_decision()
        elif self.content_type == ContentType.derive_confirm:
            self.chat = self.__process_form_submit()
        elif self.content_type == ContentType.command:
            # self.chat已在__load_prev_chat设置
            self.__process_command()
            if self.chat_command in [ChatCommand.abort, ChatCommand.voting]:
                return self.chat
        elif self.content_type == ContentType.clear_context:
            self.chat = self.__process_clear_context()
            return self.chat  # 直接返回，不调用post_process
        else:
            if not self.content and not self.content_json:
                raise HTTPException(status_code=400, detail="消息内容为空")
            self.chat = chat_repository.insert_chat(
                self.user_id,
                SenderType.user,
                self.content,
                self.conversation_id,
                content_type=self.content_type,
                content_json=self.content_json,
                options=self.options,
                ref_chat_id=self.ref_chat_id,
                ref_chat=self.ref_chat,
                chat_id=langfuse_context.get_current_trace_id(),  # 生成的 chat_id 与 trace_id、langfuse trace_id 一致
                trace_id=langfuse_context.get_current_trace_id(),
            )

        self.background_tasks.add_task(
            to_sync(self.post_process),
            **{"langfuse_observation_id": langfuse_context.get_current_trace_id()},
        )
        return self.chat

    def __process_decision(self):
        if not self.ref_chat_id or "decision" not in self.content_json:
            raise HTTPException(status_code=400, detail="缺少ref_chat_id或选择结果")
        if not self.content_json["decision"]:
            raise HTTPException(status_code=400, detail="选择结果值为空")
        decision = self.content_json["decision"]
        chat = chat_repository.get_chat(self.ref_chat_id)
        if not chat:
            raise HTTPException(status_code=400, detail="无效的ref_chat_id")
        if not chat.content_json:
            chat.content_json = {}
        if "decision" in chat.content_json:
            raise HTTPException(status_code=400, detail="已有选择结果，不能重复选择")
        if isinstance(decision, list):
            chat.content_json["decision"] = decision
        else:
            chat.content_json["decision"] = [decision]
        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            self.ref_chat_id,
            content_json=chat.content_json,
        )
        return chat

    def __post_process_decision(self):
        if (
            not self.chat
            or not self.chat.content_json
            or not self.chat.content_json.get("following_questions")
        ):
            return False
        questions = self.chat.content_json["following_questions"]

        next_choice_question = ChoiceQuestion.from_json(questions[0])
        content_json = {
            "choice": next_choice_question.to_json(),
            "following_questions": questions[1:],
            "subsequent": True,  # 不是首条选项，设置此值为真
        }
        assert self.response_chat_id
        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            self.response_chat_id,
            content_type=ContentType.choice,
            content_json=content_json,
            trace_id=self.chat.trace_id,  # 选择题在前一事件中生成，复制其id
            status=ChatStatus.done,
        )
        return True

    def __process_form_submit(self):
        if not self.ref_chat_id or "submit" not in self.content_json:
            raise HTTPException(status_code=400, detail="缺少ref_chat_id或填写内容")
        chat = chat_repository.get_chat(self.ref_chat_id)
        if not chat:
            raise HTTPException(status_code=400, detail="无效的ref_chat_id")
        if not chat.content_json:
            chat.content_json = {}
        if "submit" in chat.content_json:
            raise HTTPException(status_code=400, detail="不能重复填写")
        chat.content_json["submit"] = self.content_json["submit"]
        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            self.ref_chat_id,
            content_json=chat.content_json,
        )
        return chat

    def do_clear_context(self):
        pass

    def __process_clear_context(self) -> ChatEntity:
        current_time = datetime.datetime.now()

        # 如果有消息正在回答中，则停止回答
        latest_chat = self.latest_chat
        if latest_chat and not ChatStatus.iscomplete(latest_chat.status):
            logger.info(
                f"Before clearing context, abort the latest chat {latest_chat.chat_id} of conversation {self.conversation_id}"
            )
            try:
                chat_repository.update_chat(
                    sender_id=self.user_id,
                    conversation_id=self.conversation_id,
                    chat_id=latest_chat.chat_id,
                    status=ChatStatus.abort,
                    force_update=True,
                )
            except Exception as e:
                logger.error(
                    f"Failed to abort the latest chat {latest_chat.chat_id} of conversation {self.conversation_id}: {e}"
                )

        self.do_clear_context()

        # 清空 langgraph 的会话状态
        conversation = chat_conversation_repository.get_conversation(
            self.user_id, self.conversation_id
        )
        if conversation and conversation.langgraph_thread_id:
            logger.info(
                f"clear langgraph thread ID for conversation {self.conversation_id}"
            )
            chat_conversation_repository.update_conversation_langgraph_thread_id(
                self.user_id, self.conversation_id, ""
            )

        # 插入消息，提示用户"上下文已清除"
        chat = chat_repository.insert_chat(
            sender_id=self.user_id,
            type=SenderType.user,
            content="上下文已清除",
            conversation_id=self.conversation_id,
            content_type=self.content_type,
            create_time=current_time,
        )

        # 更新会话的 last_clear_time
        chat_conversation_repository.update_conversation_last_clear_time(
            user_id=self.user_id,
            conversation_id=self.conversation_id,
            last_clear_time=current_time,
        )

        return chat

    @staticmethod
    def get_response_default_content_json() -> dict:
        return {"start_time": time.time()}

    @staticmethod
    def get_score_id(trace_id: str, vote: ChatVoting, subtype: str = "") -> str:
        id = f"{trace_id}-voting"
        if subtype:
            id += f"-{subtype}"
        if vote != ChatVoting.idle:
            id += f"-{vote}"
        return id

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        如果子类未实现此方法，则使用此基类方法作为fallback

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return "您好，欢迎使用良智助手，有什么可以帮您的吗？", []

    def __process_command(self):
        # __load_prev_chat已检查，确认latest_chat为agent发送的信息
        assert self.latest_chat
        self.response_chat_id = self.latest_chat.chat_id
        if self.chat_command == ChatCommand.retry:
            logger.info(f"chat {self.response_chat_id} 重新回答")
            content, content_type = self.get_response_processing_content()
            # 重置最近消息的内容
            # TODO: 待优化：若停止回答后重新回答，可能前一个任务还没停止，导致两次回答的内容同时更新。
            trace_id = self.latest_chat.trace_id
            if trace_id:
                delete_score(self.get_score_id(trace_id, ChatVoting.idle))
                delete_score(
                    self.get_score_id(trace_id, ChatVoting.up, subtype="video")
                )
                delete_score(
                    self.get_score_id(trace_id, ChatVoting.down, subtype="video")
                )
            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                force_update=True,
                content=content,
                content_type=content_type,
                content_json=self.get_response_default_content_json(),  # 包含选择题等
                trace_id=langfuse_context.get_current_trace_id(),
                videos=[],
                docs=[],
                options=[],
                status=ChatStatus.ongoing,
                progress=self.regulate_progress(0),
            )
        elif self.chat_command == ChatCommand.abort:
            if ChatStatus.iscomplete(self.latest_chat.status):
                logger.warning(f"chat {self.response_chat_id} 已完成，忽略停止命令")
                return
            logger.info(f"chat {self.response_chat_id} 准备停止")

            # 设置 Abort Manager 中的会话状态
            get_abort_manager().set_aborted(self.conversation_id)
            handle_xiaohongshu_abort(self.conversation_id)
            logger.info(
                f"会话 {self.conversation_id} 已在 Abort Manager 中设置为中止状态"
            )

            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                status=ChatStatus.abort,
            )
        elif self.chat_command == ChatCommand.voting:
            # 针对self.chat进行操作，而不是self.latest_chat
            value = self.content_json.get("value", ChatVoting.idle)
            if value not in set(ChatVoting):
                raise HTTPException(
                    status_code=400, detail=f"value值应为：{set(ChatVoting)}"
                )
            assert self.chat
            if not self.chat.content_json:
                self.chat.content_json = {}
            video_id = self.content_json.get("video_id", "")
            reason = self.content_json.get("reason") or []
            trace_id = self.chat.trace_id
            assert trace_id
            if not video_id:  # 回答的点赞/点踩
                id = self.get_score_id(trace_id, ChatVoting.idle)
                if value != ChatVoting.idle:
                    langfuse_context.client_instance.score(
                        id=id,
                        trace_id=trace_id,
                        name="user_voting",
                        value=1 if value == ChatVoting.up else 0,
                        data_type="BOOLEAN",
                        comment=str(reason),
                    )
                else:
                    delete_score(id)
                self.chat.content_json["voting"] = value
                self.chat.content_json["voting_reason"] = reason
                chat_repository.update_chat(
                    self.user_id,
                    self.conversation_id,
                    self.chat.chat_id,
                    content_json=self.chat.content_json,
                    update_time=self.chat.update_time,  # 不改变消息时间
                )
            else:  # 视频的点赞/点踩
                videos = self.chat.videos or []
                found = False
                up_videos, down_videos = [], []
                for i in range(len(videos)):
                    if videos[i].get("aweme_id") == video_id:
                        found = True
                        videos[i]["voting"] = value
                        videos[i]["voting_reason"] = reason
                    match videos[i].get("voting"):
                        case ChatVoting.up:
                            up_videos.append(videos[i].get("aweme_id"))
                        case ChatVoting.down:
                            down_videos.append(videos[i].get("aweme_id"))
                if not found:
                    raise HTTPException(status_code=404, detail=f"video_id无效")
                langfuse_context.client_instance.score(
                    id=self.get_score_id(trace_id, ChatVoting.up, subtype="video"),
                    trace_id=trace_id,
                    name="user_upvote_videos",
                    value=len(up_videos),
                    comment=str(up_videos),  # 每个视频原因不一，此处不添加原因
                )
                langfuse_context.client_instance.score(
                    id=self.get_score_id(trace_id, ChatVoting.down, subtype="video"),
                    trace_id=trace_id,
                    name="user_downvote_videos",
                    value=len(down_videos),
                    comment=str(down_videos),  # 每个视频原因不一，此处不添加原因
                )
                chat_repository.update_chat(
                    self.user_id,
                    self.conversation_id,
                    self.chat.chat_id,
                    videos=videos,
                    update_time=self.chat.update_time,  # 不改变消息时间
                )
            record_voting(
                self.user_id,
                self.conversation_id,
                trace_id,
                value,
                video_id,
                reason=reason,
                chat_id=self.chat.chat_id,
            )

    def pre_process(self):
        pass

    def show_progress_default(self) -> bool:
        """
        控制是否默认展示进度条。
        如果默认展示进度条，则新增的消息进度为0，在do_reply中递增进度；
        如果默认不展示进度条，则新增的消息进度为1，在需要时再更新进度为小于1的值。
        """
        return True

    def regulate_progress(self, progress: float) -> float:
        if self.show_progress_default():
            return progress
        return 1.0

    def get_response_chat(self) -> ChatEntity:
        assert self.response_chat_id
        response_chat = chat_repository.get_chat(self.response_chat_id)
        assert response_chat
        return response_chat

    @exception_logger
    @observe(name="chat", capture_input=False, capture_output=False)
    async def post_process(self, **kwargs):
        with active_counter.counter_manager():
            langfuse_context.update_current_trace(
                user_id=str(self.user_id), session_id=self.conversation_id
            )
            self.insert_response_chat()
            if self.__post_process_decision():
                return
            # 处理链接
            await self.add_link_preview()
            digest_task = asyncio.create_task(self.digest_chat())
            await self.do_reply()
            await digest_task

    async def do_reply(self):
        update_chat = chat_repository.updater(self.user_id, self.conversation_id)

        # 将用户消息发送给 openai
        messages = self.get_message_list()
        functions = self.get_functions(messages)
        tool_choice = self.get_tool_choice()
        # 如果 openai 有返回内容，则每 0.2s 更新一次 response chat，实现对用户的流式响应
        response_chat = self.get_response_chat()
        progress = response_chat.progress
        limiter = RateLimiter(0.2)
        try:
            logger.info(f"call auto_chat for chat: {self.response_chat_id}")
            async for item in auto_chat(
                messages, functions, tool_choice, user=str(self.user_id)
            ):
                limiter.set(item)
                progress = min(0.99, progress + 0.002)
                value = limiter.get()
                if value:
                    logger.info(f"get result len: {len(value)} {value}")
                    update_chat(
                        response_chat.chat_id,
                        content_type=ContentType.normal,
                        content=value,
                        progress=self.regulate_progress(progress),
                    )
            value = limiter.get(flush=True)
            if value:
                update_chat(
                    response_chat.chat_id,
                    content=value,
                )

            for f in self.before_package_response_content_hooks:
                f()

            # 封装消息内容，例如产品助手添加引用
            self.package_response_content()

            # 生成给用户的一系列选项
            await self.generate_response_options(messages)

            update_chat(response_chat.chat_id, status=ChatStatus.done)
        except chat_repository.ChatAbortError:
            logger.warning(f"chat {response_chat.chat_id} 已停止")
        except Exception as e:
            logger.error(f"{self.content}失败，error: {e}", exc_info=True)
            content = f"处理失败，请稍后再试。"
            update_chat(
                response_chat.chat_id,
                status=ChatStatus.error,
                content=content,
                content_type=ContentType.normal,
            )

    # 插入响应消息，之后会更新这条消息的内容
    def insert_response_chat(self):
        if self.response_chat_id:
            # 重新生成时，复用原chat_id
            return
        content, content_type = self.get_response_processing_content()
        response_chat = chat_repository.insert_chat(
            self.user_id,
            SenderType.agent,
            content,
            self.conversation_id,
            content_type=content_type,
            content_json=self.get_response_default_content_json(),
            trace_id=langfuse_context.get_current_trace_id(),
            status=ChatStatus.ongoing,
            progress=self.regulate_progress(0),
        )
        self.response_chat_id = response_chat.chat_id

    @exception_logger
    async def digest_chat(self):
        if self.content_type not in [ContentType.normal]:
            return
        history_chats = self.get_history_chats()
        messages = convert_chats_to_messages(history_chats)
        if not messages:
            return

        # 构建提示词
        prompt = "以上是用户与AI的对话记录，请用简略的话提炼当前正在谈论的话题，重点关注用户最近一次提问的意图。返回的话题要求20个字以内，不要啰嗦。"
        langchain_messages = [
            SystemMessage(content="你是一个提炼对话主题的专家。"),
            *messages,
            SystemMessage(content=prompt),
        ]
        content = await fast_llm.ainvoke(langchain_messages, name="提炼对话主题")

        # 从 langchain 响应中提取文本内容
        digest_response = content.content

        if not digest_response or not isinstance(digest_response, str):
            logger.error(f"提炼的会话主题为空或类型不匹配:【{digest_response}】")
            return

        logger.info(f"提炼的会话主题:【{digest_response}】")
        if digest_response.endswith("。") or digest_response.endswith("."):
            digest_response = digest_response[:-1]

        chat_conversation_repository.rename_conversation_name_and_avatar(
            self.user_id, self.conversation_id, digest_response
        )

    async def add_link_preview(self):
        assert self.chat and self.response_chat_id
        if not self.chat.content_json:
            self.chat.content_json = {}
        if "link_previews" in self.chat.content_json:
            logger.info(f"已处理过链接，跳过{self.chat.chat_id}: {self.chat.content}")
            return
        urls = find_all_links(self.chat.content)
        if not urls:
            return
        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            self.response_chat_id,
            content="正在浏览您提供的链接 <loading-img-1></loading-img-1>",
        )

        content, link_previews = await replace_links(self.chat.content, urls)
        if not link_previews:
            return
        # 视频oss链接保存在content_json["link_previews"]["video_oss_url"]中。
        self.chat.content_json["link_previews"] = link_previews
        logger.info(
            f"发现链接({len(link_previews)}个)，更新消息 {self.chat.chat_id}: {content}"
        )
        chat_type = ChatType.link
        for preview in link_previews.values():
            if preview.get("video_oss_url") or preview.get("page_type") == "video":
                chat_type = ChatType.video
                break
            if preview.get("page_type") == "image":
                chat_type = ChatType.image
        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            self.chat.chat_id,
            content=content,
            content_json=self.chat.content_json,
            chat_type=chat_type,
        )

    # 如果某个助手需要定制 处理中 的消息内容，可以重写这个方法
    def get_response_processing_content(self) -> tuple[str, ContentType]:
        return "处理中，请稍等……", ContentType.normal

    # 获取接下来要发送给 openai 的消息列表，包括 system message，最近 10 条聊天记录，及这一次的用户消息
    def get_message_list(self):
        # 先获取历史消息，system message 可能会依赖历史消息
        history_chats = self.get_history_chats()
        messages = []
        # 首先在message中添加system角色的内容，再将用户历史加入
        system_message_content = self.get_system_message_content()
        if system_message_content:
            messages.append(SystemMessage(content=system_message_content))
        messages += convert_chats_to_messages(
            history_chats, self.get_latest_user_content_template()
        )
        return messages

    # 各个助手定义自己的 system message，可以在这里加入对话上下文信息，如文档列表、视频列表
    def get_system_message_content(self) -> str:
        return ""

    def get_latest_user_content_template(self) -> str | None:
        return None

    def get_max_history(self) -> int:
        return 20

    # 置顶消息，如策划助手中会将视频/拆解报告置顶
    def get_top_chat(self) -> ChatEntity | None:
        return None

    def get_history_chats(self) -> list[ChatVo]:
        top_chat = self.get_top_chat()

        limit = self.get_max_history() + 1
        chats = chat_repository.get_latest_messages(
            self.user_id,
            self.conversation_id,
            start_time="2000-01-01 00:00:00",
            limit=limit,
            order_desc=True,
            include_hidden=True,
            filter_cleared=True,
        )

        history = chats[1:][::-1]  # 跳过最新那条（最新的回复消息），并按升序排列
        if top_chat is not None:
            for i in range(len(history)):
                if history[i].chat_id == top_chat.chat_id:
                    # 只返回置顶消息之后的历史
                    return history[i:]
            vo = chat_repository.to_chat_schema(top_chat)
            history = [vo] + history
        return history

    # 各个助手定义自己支持的 tool call
    def get_functions(self, messages) -> list[ToolCall]:
        return []

    def get_tool_choice(self) -> str | NotGiven:
        return NOT_GIVEN

    # 根据业务场景处理返回的消息内容，实现消息气泡的定制化
    def package_response_content(self):
        response_chat = self.get_response_chat()
        docs = response_chat.docs
        if not docs:
            return

        for doc in docs:
            doc["doc_name"] = doc["title"]

        chat_repository.update_chat(
            self.user_id,
            self.conversation_id,
            response_chat.chat_id,
            docs=docs,
        )

    async def generate_response_options(self, messages: list[BaseMessage]):
        class OptionsResponse(BaseModel):
            options: list[str]

        response_chat = self.get_response_chat()
        if response_chat.options:
            return
        vo = chat_repository.to_chat_schema(response_chat)
        messages += convert_chat_to_messages(vo)
        try:
            prompt = self.get_option_prompt_content()
            if prompt is None:
                return
            additional_prompt = {
                "role": "user",
                "content": prompt,
            }
            messages.extend(convert_to_messages([additional_prompt]))
            option_response: OptionsResponse = (  # type: ignore
                await long_context_llm.with_structured_output(OptionsResponse).ainvoke(
                    input=messages, name="生成选项"
                )
            )
            options = option_response.options

            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                response_chat.chat_id,
                options=options,
            )
        except Exception as e:
            logger.error(f"generate options error: {e}")

    # 各个助手定义自己的 option prompt
    def get_option_prompt_content(self) -> str | None:
        return get_prompt("default_option", TEMPLATES["option"]["default"])
