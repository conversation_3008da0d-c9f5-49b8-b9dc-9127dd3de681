import uuid
import time

from langfuse.decorators import observe

from app.conversation_processor.housekeeper_processor import HousekeeperProcessor
from app.conversation_type import CONTENT_ASSISTANT_INIT_CONTENT
from app.service.search_agent.housekeeper_agent import (
    HousekeeperAgentBase,
)
from app.service.content_assistant.content_assistent_v5_agent import (
    CONTENT_ASSISTANT_V5_HK_INSTANCE,
)
from app.config import ASSISTANT_DATA, S3_URL_EXPIRATION_TIME
from app.service.content_assistant.data_def import VideoInfo, ArticleInfo
from app.db.repository import chat_repository
from app.conversation_processor.utils import upload_user_file_to_s3
from app.service.search_agent.data_def import AssistantName


class ContentAssistantV5Processor(HousekeeperProcessor):
    """
    内容助手V5处理器，继承自HousekeeperProcessor，行为与后者完全相同。
    只是 agent instnace 不同。
    """

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return CONTENT_ASSISTANT_INIT_CONTENT, []

    def get_hk_agent(self) -> HousekeeperAgentBase:
        return CONTENT_ASSISTANT_V5_HK_INSTANCE

    def pre_process(self):
        if self.upload_file:
            file_path = f"{ASSISTANT_DATA}/{uuid.uuid4()}.mp4"
            url = upload_user_file_to_s3(self.user_id, file_path, self.upload_file.file)
            # 这些字段必须要设置，否则前端无法预览上传的视频
            self.content_json["file_size"] = self.upload_file.size
            self.content_json["video_url"] = url
            self.content_json["video_path"] = file_path
            self.content_json["video_url_expiration_time"] = (
                time.time() + S3_URL_EXPIRATION_TIME
            )
            self.content_json["link_previews"] = {
                url: {
                    "title": self.upload_file.filename,
                    "desc": self.upload_file.filename,
                    "media_meta": {},
                    "site_name": "用户上传",
                    "og_type": "video",
                    "video_oss_url": url,
                }
            }

    def get_history_chat_content_json(self) -> str:
        """
        获取当前对话的历史content_json信息, 用来提取视频和图文链接信息补全上下文

        Returns:
            str: 系统消息内容
        """
        try:
            if not hasattr(self, "conversation_id") or not self.conversation_id:
                return ""

            # 使用chat_repository获取最新的会话消息
            history_chats = self.get_history_chats()
            if not history_chats:
                return ""

            # 从历史消息中提取视频和文章信息
            videos: list[VideoInfo] = []
            articles: list[ArticleInfo] = []

            for chat in history_chats:
                if not chat.content_json:
                    continue

                # 提取视频信息
                if "video_info" in chat.content_json:
                    video_info = chat.content_json["video_info"]
                    if isinstance(video_info, dict):
                        videos.append(VideoInfo(**video_info))

                # 提取文章信息
                if "article_info" in chat.content_json:
                    article_info = chat.content_json["article_info"]
                    if isinstance(article_info, dict):
                        articles.append(ArticleInfo(**article_info))

            # 构建系统消息
            system_msg = ""
            if videos:
                system_msg += f"\n当前对话中包含以下视频:\n"
                for video in videos:
                    system_msg += f"- {video.video_title}\n"

            if articles:
                system_msg += f"\n当前对话中包含以下文章:\n"
                for article in articles:
                    system_msg += f"- {article.article_title}\n"

            return ""
        except Exception as e:
            import traceback

            print(f"获取content_json时出错: {e}")
            print(traceback.format_exc())
            return ""

    def get_assistant_name(self) -> AssistantName:
        return AssistantName.content_assistant_v5

    @observe(name="content_assistant_v5")
    async def _do_reply(self, update_chat):
        await super()._do_reply(update_chat)

    async def generate_response_options(self, messages):
        # https://cybercore.feishu.cn/record/I5G7r9TvBeEWbJcIPP0cezWNnLd
        pass
