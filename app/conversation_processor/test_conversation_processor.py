import pytest
from httpx import AsyncClient
from app.utils.asgi_utils import ASGITransport
import asyncio
import json
from typing import Callable, Coroutine
from datetime import datetime
from app.main import app
from app.db.common_type import ChatCommand, ContentType, ChatStatus, SenderType


TEST_USER_ID = 1234
TEST_CONVERSATION_ID = "3860479934944a5abb78fdffaa71e179"


aclient = AsyncClient(
    transport=ASGITransport(app=app),
    base_url="http://sihegpt-testserver",
    headers={"FAKE_USER_ID": str(TEST_USER_ID)},
)


async def get_stream(
    message_sender: Callable[[], Coroutine], chat_checker: Callable[[dict], bool] = None
):
    """
    chat_checker: 返回True表示获取到想要的结果，接收stream的循环可终止。
    """
    # 延迟1s，避免取到前面会话更新的消息
    await asyncio.sleep(1)
    async with aclient.stream(
        "GET",
        f"/chat/conversation/{TEST_CONVERSATION_ID}/message_stream",
        params={
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        },
    ) as stream_resp:
        assert stream_resp.status_code == 200
        assert "text/event-stream" in stream_resp.headers["content-type"]

        await message_sender()

        async for line in stream_resp.aiter_lines():
            if not line.startswith("data:"):
                continue
            # print(f"get stream line: {line}")
            data = line[len("data: ") :]
            chat = json.loads(data)
            if not chat_checker:
                if (
                    chat.get("sender_type") == SenderType.agent
                    and chat.get("status") != ChatStatus.ongoing
                ):
                    break
                continue
            res = chat_checker(chat)
            if res:
                break
        print("finish")


def command_sender(command: ChatCommand, expect_code: int):
    async def sender():
        response = await aclient.post(
            f"/chat/conversation/{TEST_CONVERSATION_ID}/message",
            data={
                "content": "",
                "content_type": ContentType.command,
                "content_json": json.dumps(
                    {
                        "command": command,
                    }
                ),
            },
        )
        assert response.status_code == expect_code
        print(f"get response: {response.text}")

    return sender


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_retry_command_perform():
    # 假设前面的命令都正常完成
    sender = command_sender(ChatCommand.retry, 200)
    await get_stream(sender)


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_abort_command_perform():
    async def sender():
        response = await aclient.post(
            f"/chat/conversation/{TEST_CONVERSATION_ID}/message",
            data={
                "content": "来一段古文",
            },
        )
        assert response.status_code == 200
        print(f"get response: {response.text}")

        await asyncio.sleep(1)
        abort = command_sender(ChatCommand.abort, 200)
        await abort()

    def checker(chat: dict) -> bool:
        if chat.get("sender_type") == SenderType.user:
            return False
        if chat.get("status") == ChatStatus.ongoing:
            return False
        assert chat.get("status") == ChatStatus.abort
        return True

    await get_stream(sender, checker)


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_abort_command_skip():
    # 对已经完成的消息，跳过abort命令
    sender = command_sender(ChatCommand.abort, 200)
    await sender()


async def request_link_preview(url: str, deep_checker: Callable[[dict], bool] = None):
    async def sender():
        response = await aclient.post(
            f"/chat/conversation/{TEST_CONVERSATION_ID}/message",
            data={
                "content": f"分析这个链接：{url}",
                "content_json": json.dumps(
                    {
                        "instant": True,
                    }
                ),
            },
        )
        assert response.status_code == 200
        print(f"get response: {response.text}")

    link_converted = False

    def checker(chat: dict) -> bool:
        nonlocal link_converted
        if chat.get("sender_type") == SenderType.user:
            if "link_previews" not in chat.get("content_json"):
                return False
            link_converted = True
            print(f"converted content: {chat.get('content')}")
            if deep_checker:
                link_previews = chat.get("content_json").get("link_previews")
                deep_checker(link_previews)
            return False
        assert link_converted, f"get chat: {chat}"
        return chat.get("status") != ChatStatus.ongoing

    await get_stream(sender, checker)


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_link_preview_html_blog():
    def deep_checker(link_previews: dict):
        for detail in link_previews.values():
            assert detail.get("page_type") == "html"
            assert detail.get("site_name") == "博客园"

    await request_link_preview(
        "https://www.cnblogs.com/wang_yb/p/18081105", deep_checker
    )


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_link_preview_html_video():
    def deep_checker(link_previews: dict):
        for detail in link_previews.values():
            assert detail.get("page_type") == "html"
            assert detail.get("site_name") == "抖音"
            assert detail.get("video")

    await request_link_preview(
        "https://www.douyin.com/video/7446326511310769423", deep_checker
    )


@pytest.mark.e2e_test
@pytest.mark.asyncio
async def test_link_preview_image():
    def deep_checker(link_previews: dict):
        for detail in link_previews.values():
            assert detail.get("page_type") == "image"
            assert detail.get("site_name") == ""
            assert detail.get("image")

    await request_link_preview(
        "https://img-blog.csdnimg.cn/20200927140309460.png", deep_checker
    )
