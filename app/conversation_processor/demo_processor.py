import datetime
import random
import asyncio
from typing import Sequence, List, Optional

from app.client.prompts import TEMPLATES
from app.conversation_processor.tool_functions import ChoiceQuestion
from app.db.common_type import ContentType, SenderType, ChatStatus, ChatType
from app.db.repository import chat_repository
from app.logger import exception_logger, logger
from app.utils import langfuse_utils
from .base_processor import BaseProcessor, ToolCall
import app.service.document_service as document_service

DEMO_CONTENT: Sequence[str] = (
    "豫章故郡，洪都新府。星分翼轸，地接衡庐。|襟三江而带五湖，控蛮荆而引瓯越。|物华天宝，龙光射牛斗之墟；人杰地灵，徐孺下陈蕃之榻。|雄州雾列，俊采星驰。台隍枕夷夏之交，宾主尽东南之美。|\
都督阎公之雅望，棨戟遥临；宇文新州之懿范，襜帷暂驻。|十旬休假，胜友如云；千里逢迎，高朋满座。|腾蛟起凤，孟学士之词宗；紫电青霜，王将军之武库。|家君作宰，路出名区；童子何知，躬逢胜饯。|\
时维九月，序属三秋。潦水尽而寒潭清，烟光凝而暮山紫。|俨骖騑于上路，访风景于崇阿。临帝子之长洲，得仙人之旧馆。|层峦耸翠，上出重霄；飞阁流丹，下临无地。|鹤汀凫渚，穷岛屿之萦回；桂殿兰宫，即冈峦之体势。|\
披绣闼，俯雕甍，山原旷其盈视，川泽纡其骇瞩。|闾阎扑地，钟鸣鼎食之家；舸舰迷津，青雀黄龙之舳。|云销雨霁，彩彻区明。落霞与孤鹜齐飞，秋水共长天一色。|渔舟唱晚，响穷彭蠡之滨，雁阵惊寒，声断衡阳之浦。".split(
        "|"
    )
)


class DemoProcessor(BaseProcessor):
    def insert_response_chat(self):
        pass

    def abort_user_message(self):
        return False

    async def do_reply(self):
        if self.content_type == ContentType.decision:
            # 处理选择题回复
            chat = chat_repository.insert_chat(
                self.user_id,
                SenderType.agent,
                f"好的，你选择了{self.content_json['decision']}",  # type: ignore
                self.conversation_id,
                status=ChatStatus.done,
            )
            return
        if self.chat and self.chat.content == "doc":
            await self.demo_create_doc_with_tengwangge(
                self.user_id,
                self.conversation_id,
            )
        elif self.chat and self.chat.content == "doc2":
            await self.create_doc_with_sample_content(
                self.user_id,
                self.conversation_id,
            )
        elif self.chat and self.chat.content == "选择题":
            chat = chat_repository.insert_chat(
                self.user_id,
                SenderType.agent,
                content=f"这是个选择题",
                conversation_id=self.conversation_id,
                status=ChatStatus.done,
                content_type=ContentType.choice,
                content_json={
                    "choice": ChoiceQuestion(
                        question="你最喜欢什么颜色？",
                        options=["红色", "蓝色", "绿色"],
                        is_single_choice=True,
                        allow_other=False,
                    ).to_json(),
                    "following_questions": [],
                },
            )
            print(chat)

        else:
            await self.demo_talk(
                self.user_id,
                self.conversation_id,
            )

    @staticmethod
    def get_demo_line(idx: int = -1) -> str:
        if idx < 0:
            idx = random.randint(0, len(DEMO_CONTENT) - 1)
        idx = idx % len(DEMO_CONTENT)
        return DEMO_CONTENT[idx]

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        message = f"""这是 demo processor。我的功能有：
1. 随机展示《滕王阁序》的片段。默认情况下会分6次逐步生成完整回复，每次间隔0.5秒。
2. 发送消息时如果包含 "instant" 会立即返回一段文本而不是逐步生成
3. 如果发送 "doc", "doc2"，则会创建一个文档，在聊天气泡中返回文档链接，然后增量地向文档中添加内容
4. 如果发送 "选择题"，则会生成一个选择题，在聊天气泡中返回选择题，然后增量地向文档中添加内容
    
> {DemoProcessor.get_demo_line()}
"""
        options = ["doc", "doc2", "instant", "选择题"]
        return message, options

    @exception_logger
    async def demo_create_doc_with_tengwangge(self, user_id, conversation_id):
        """
        创建一个包含《滕王阁序》内容的文档
        """
        # 准备《滕王阁序》的内容
        tengwangge_content = []
        for i in range(5):  # 5段
            for idx, s in enumerate(DEMO_CONTENT):
                content = (
                    f"[{i+1}/5] [🔗](https://www.baidu.com/s?wd=我是谁) [google](https://www.google.com) [1.cn](https://1.cn/) "
                    if idx == 0
                    else ""
                )
                content += s
                if idx == len(DEMO_CONTENT) - 1:
                    content += "\n\n"
                tengwangge_content.append(content)

        # 调用通用文档创建函数
        doc_title = "滕王阁序" + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        await self.create_doc_with_incremental_content(
            user_id=user_id,
            conversation_id=conversation_id,
            content_list=tengwangge_content,
            doc_title=doc_title,
            delay_seconds=1.0,
        )

    async def create_doc_with_sample_content(self, user_id, conversation_id):
        """
        创建一个包含示例内容的文档，每20个字符切分为一段
        """
        # 示例内容
        sample_content = """## 1 营销目标与受众定位

### 营销目标  
1. **提高品牌知名度**：
通过达人推广，提升目标消费群体对白酒品牌的认知度。  
2. 带动实际销售：通过精准投放，提高300-500元价位白酒的销量。  
3. 建立品牌形象：塑造白酒的高品质和适合年轻人群或商务场景的调性。  

[2024年Q1酒类线上消费趋势洞察.pdf](https://gptcloud.oss-jiaxing.sihe.cloud/pdf/2025-01/%E8%BF%90%E8%90%A5%E7%A0%94%E7%A9%B6%E7%A4%BE-%E3%80%90%E9%A3%9E%E7%93%9C%E6%95%B0%E6%8D%AE%C3%97%E9%A3%9E%E7%93%9C%E5%93%81%E7%AD%96%E3%80%912024%E5%B9%B4Q1%E9%85%92%E7%B1%BB%E7%BA%BF%E4%B8%8A%E6%B6%88%E8%B4%B9%E8%B6%8B%E5%8A%BF%E6%B4%9E%E5%AF%9F.pdf)


### 受众定位  
1. **核心消费群体**：年龄在28-45岁之间，有一定消费能力的中高收入群体，注重生活品质，接受新型消费理念。  [🔗](https://www.google.com)
2. **场景需求**：商务宴请、朋友聚会、节日送礼等场景，对白酒品质和品牌有较高要求。   [🔗](https://www.baidu.com/s?wd=我是谁)
3. **消费习惯**：常使用社交媒体（如抖音、小红书、微博等）获取消费信息，容易受达人推荐和社交口碑影响。  [🔗](https://1.cn/)


### 带货列表

- 测试列表中的表格
- | No. | 度数 | 销量 |
| --- | --- | --- |
| 1 | 53.00 | 362.86万 |
| 2 | 52.00 | 341.91万 |
| 3 | 42.00 | 18.69万 |
| 4 | 45.00 | 12.19万 |
| 5 | 50.00 | 10.84万 |



| 商品名称                                             | 规格     |   销量 | 销售额      | 平均件单价    |
|:-------------------------------------------------|:-------|-----:|:---------|:---------|
| 筑春2021年筑春水墨酱香型白酒礼盒装53度500ml                      | 酱香，53度 |  831 | ¥38.25万  | ¥460.33  |
| 国台天禧鸿福-酱香型白酒3斤坛高端宴席团建摆店神器收藏53%Vol                | 酱香，53度 |  146 | ¥10.96万  | ¥750.78  |
| 【酒博士专属】夜郎老酒御酿督造2500ml 送礼宴请自饮酱香型白酒53%Vol          | 酱香，53度 |   94 | ¥9.39万   | ¥999     |
| MOUTAI PRINCE;53%V/V; 500ML/茅台王子酒珍品王子53度53度500   | 酱香，53度 |  213 | ¥8.29万   | ¥389     |
| 筑春 【酒博士专属】金字陈酿  酱香型白酒53度500毫升                    | 酱香，53度 |  213 | ¥7.86万   | ¥369     |
| 碧春碧春五星简装版500ml×6瓶酱香型白酒整箱53度500                   | 酱香，53度 |   58 | ¥5.57万   | ¥960.84  |
| 习酒1314 酱香型白酒 送礼摆柜53度1.314L                       | 酱香，53度 |   85 | ¥5.52万   | ¥649     |
| 摘要 品鉴小酒【非卖品】 53度酱香型白酒 新老包装随机发53度100ml            | 酱香，53度 |  275 | ¥5.47万   | ¥199     |
| 【酒博士专属】国台2013年老酒 金字国台酒  贵州名酒酱香酒 53度500ml         | 酱香，53度 |   53 | ¥5.24万   | ¥989     |

### 词云demo

```chart
{
      "type": "wordcloud",
      "title": "核心指标热度图",
      "details": {
        "unit_suffix": "",
        "displayType": "value"
      },
      "data": [
        { "name": "播放量", "value": 10000 },
        { "name": "投稿数", "value": 8000 },
        { "name": "完播率", "value": 7600 },
        { "name": "UV", "value": 7000 },
        { "name": "PV", "value": 6500 },
        { "name": "粉丝净增", "value": 5800 },
        { "name": "互动指数", "value": 5400 },
        { "name": "评论率", "value": 5000 },
        { "name": "日活", "value": 4800 }
      ]
}
```

### tab demo

<tab_container>
<tab title="图表1">
```chart
{
      "type": "wordcloud",
      "title": "核心指标热度图",
      "details": {
        "unit_suffix": "",
        "displayType": "value"
      },
      "data": [
        { "name": "播放量", "value": 10000 },
        { "name": "投稿数", "value": 8000 },
        { "name": "完播率", "value": 7600 },
        { "name": "UV", "value": 7000 },
        { "name": "PV", "value": 6500 },
        { "name": "粉丝净增", "value": 5800 },
        { "name": "互动指数", "value": 5400 },
        { "name": "评论率", "value": 5000 },
        { "name": "日活", "value": 4800 },
        { "name": "测试词1", "value": 1000 },
        { "name": "测试词2", "value": 800 },
        { "name": "测试词3", "value": 760 },
        { "name": "测试词4", "value": 700 },
        { "name": "测试词5", "value": 650 },
        { "name": "测试词6", "value": 580 },
        { "name": "测试词7", "value": 540 },
        { "name": "测试词8", "value": 500 },
        { "name": "测试词9", "value": 480 },
        { "name": "测试词10", "value": 450 },
        { "name": "测试词11", "value": 400 },
        { "name": "测试词12", "value": 380 },
        { "name": "测试词13", "value": 350 },
        { "name": "测试词14", "value": 300 },
        { "name": "测试词15", "value": 280 }
      ]
}
```
</tab>
<tab title="图表2">
| No. | 商品名称 | 近7日销量 |
| --- | --- | --- |
| 1 | 舍得品味舍得 浓香型白酒 2018年52度250mL×1瓶 | 182.00 |
| 2 | 华位品鉴牡丹 宜宾5粮液股份出品仙林生态绿豆酒高端送礼浓香 | 62.00 |
| 3 | LUZHOU/泸州世纪荣耀酒悦坛 【酒水节】浓香型白酒52度999ml×2 | 44.00 |
| 4 | 贵州小金猪集团出品 艺术小酒 浓香型白酒猪宝宝52度800mL51-60度 | 43.00 |
| 5 | 金六福【四瓶礼盒】52度固态纯粮浓香白酒手提宴请官方正品52度2L | 40.00 |
</tab>
<tab title="表格1">
| No. | 商品名称 | 近7日销量 |
| --- | --- | --- |
| 1 | 舍得品味舍得 浓香型白酒 2018年52度250mL×1瓶 | 182.00 |
| 2 | 华位品鉴牡丹 宜宾5粮液股份出品仙林生态绿豆酒高端送礼浓香 | 62.00 |
| 3 | LUZHOU/泸州世纪荣耀酒悦坛 【酒水节】浓香型白酒52度999ml×2 | 44.00 |
| 4 | 贵州小金猪集团出品 艺术小酒 浓香型白酒猪宝宝52度800mL51-60度 | 43.00 |
| 5 | 金六福【四瓶礼盒】52度固态纯粮浓香白酒手提宴请官方正品52度2L | 40.00 |
</tab>
<tab title="表格2">
| No. | 商品名称 | 近7日销量 |
| --- | --- | --- |
| 1 | 舍得品味舍得 浓香型白酒 2018年52度250mL×1瓶 | 182.00 |
| 2 | 华位品鉴牡丹 宜宾5粮液股份出品仙林生态绿豆酒高端送礼浓香 | 62.00 |
| 3 | LUZHOU/泸州世纪荣耀酒悦坛 【酒水节】浓香型白酒52度999ml×2 | 44.00 |
| 4 | 贵州小金猪集团出品 艺术小酒 浓香型白酒猪宝宝52度800mL51-60度 | 43.00 |
| 5 | 金六福【四瓶礼盒】52度固态纯粮浓香白酒手提宴请官方正品52度2L | 40.00 |
| 6 | 五粮醇红淡雅50度500mL×6瓶整箱×10箱畅享装浓香型【官方直营】50度 | 25.00 |
| 7 | 沱牌金质壹号浓香型纯粮食白酒整箱500ML×6瓶送礼自饮固态法52度 | 22.00 |
| 8 | 古井贡【百大专属】年份原浆 献礼版浓香型白酒50度500ml | 19.00 |
| 9 | 泸州老窖六年窖头曲浓香白酒52度500ml×4瓶 | 18.00 |
| 10 | 金徽【买一送一】52度嘉宾浓香型优级宴请正宗纯粮酒52度500ml×4 | 9.00 |
| 11 | 鸭溪窖42度小美人浓香型白酒裸瓶125ml纯粮酿造小酒42度125ml | 4.00 |
| 12 | 刘伶醉千年古酒高端收藏酒版纯粮酒浓香型白酒52度100mL | 3.00 |
| 13 | 五粮液【20-24年】八代普五 浓香型白酒 随机发货52度500ml | 2.00 |
| 14 | 楚宣王楚宣王原酿42度浓香型白酒光瓶纯粮酒 | 2.00 |
| 15 | 叙府酒 三瑞呈祥 生肖纪念酒礼盒装 纯粮酿造52度268ml | 1.0000 |
| 16 | 舍得沱牌六粮酒  浓香型白酒【DB】50度500ml×6瓶 | 1.0000 |
| 17 | 乘风邀月帝王尊 (至尊版）黑金尊浓香型白酒500mlX02SH52度51-60度 | 1.0000 |
| 18 | 剑南春水晶剑 52度【浓香型】白酒500ml×6瓶 （原箱） | 1.0000 |
| 19 | 双沟君坊整箱装52度/41.8度 含原浆浓香型白酒52度520ml×6 | 1.0000 |
| 20 | 46度柔和金徽H6 500ml×4瓶+50度山水金徽-山500ml×4瓶 （达人） | 1.0000 |
</tab>
</tab_container>
"""

        # 将内容按每10 ~ 20个字符切分
        rand_char_count = random.randint(10, 20)
        content_list = []
        for i in range(0, len(sample_content), rand_char_count):
            content_list.append(sample_content[i : i + rand_char_count])
        delay_seconds = random.uniform(0.1, 1.0)

        # 调用通用文档创建函数
        doc_title = (
            "营销策略示例"
            + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            + f"({rand_char_count}字符切分, delay={delay_seconds:.2f}秒)"
        )
        await self.create_doc_with_incremental_content(
            user_id=user_id,
            conversation_id=conversation_id,
            content_list=content_list,
            doc_title=doc_title,
            delay_seconds=delay_seconds,
        )

    @staticmethod
    @exception_logger
    async def create_doc_with_incremental_content(
        user_id: int,
        conversation_id: str,
        content_list: List[str],
        doc_title: str,
        delay_seconds: float,
    ):
        """
        通用的增量创建文档函数

        参数:
            user_id: 用户ID
            conversation_id: 会话ID
            content_list: 要添加到文档中的内容列表
            doc_title: 文档标题
            delay_seconds: 每次添加内容后的延迟时间
        """
        # 创建一个初始聊天消息，告知用户处理中
        chat = chat_repository.insert_chat(
            user_id,
            SenderType.agent,
            f"正在生成《{doc_title}》文档，请稍等...",
            conversation_id,
            status=ChatStatus.ongoing,
        )

        # 初始化文档内容
        doc_content = f"# {doc_title}\n\n"
        doc_id = None
        doc_url = None
        total_items = len(content_list)

        # 逐步添加内容到文档
        for idx, content_item in enumerate(content_list):
            # 添加新的内容
            doc_content += content_item

            # 更新文档
            doc_url, workspace_id, doc_id = document_service.update_md_doc(
                user_id=user_id,
                doc_id=doc_id,  # 第一次为None时会创建文档，后续会更新
                markdown_text=doc_content,
                mark_new_version=False,
            )

            # 更新聊天消息，显示进度和文档链接
            progress = (idx + 1) / total_items
            chat_content = f"正在生成《{doc_title}》文档...\n\n[点击查看文档]({doc_url})\n\n已完成: {int(progress * 100)}% <loading-img-1></loading-img-1>"

            chat_repository.update_chat(
                user_id,
                conversation_id,
                chat.chat_id,
                content=chat_content,
                progress=progress,
                chat_type=ChatType.document,
                docs=[{"url": doc_url, "title": doc_title}],
            )

            # 延迟一段时间，模拟内容生成过程
            await asyncio.sleep(delay_seconds)

        # 最终更新，完成状态
        final_message = f"已生成《{doc_title}》文档:\n\n[点击查看完整文档]({doc_url})"
        chat_repository.update_chat(
            user_id,
            conversation_id,
            chat.chat_id,
            content=final_message,
            status=ChatStatus.done,
            progress=1.0,
        )

        logger.info(f"文档 '{doc_title}' 已创建完成，ID: {doc_id}, URL: {doc_url}")
        return doc_url, doc_id

    @exception_logger
    async def demo_talk(self, user_id, conversation_id):
        chat_history = self.get_history_chats()
        if self.content and "instant" in self.content:
            chat_repository.insert_chat(
                user_id,
                SenderType.agent,
                DemoProcessor.get_demo_line(),
                conversation_id,
            )
            return

        chat = chat_repository.insert_chat(
            user_id,
            SenderType.agent,
            "处理中，请稍等……",
            conversation_id,
            status=ChatStatus.ongoing,
        )
        content = ""
        SENTENCE_COUNT = 6
        for i in range(SENTENCE_COUNT):
            await asyncio.sleep(0.5)
            content += DemoProcessor.get_demo_line(i)
            chat_repository.update_chat(
                user_id,
                conversation_id,
                chat.chat_id,
                content=content,
                progress=0.1 * (i + 1),
            )
        chat_repository.update_chat(
            user_id,
            conversation_id,
            chat.chat_id,
            status=ChatStatus.done,
        )
