import json
from unittest.mock import Mock, patch

from langchain_core.messages import AIMessage, HumanMessage
import pytest

from app.db.vo import Chat as ChatVo
from app.conversation_processor.chat_convertor import (
    convert_choice_to_messages,
    convert_chats_to_messages,
)
from app.db.common_type import SenderType, TaskType, TaskStatus, ContentType


@pytest.fixture
def mock_task_repository():
    with patch("app.conversation_processor.chat_convertor.task_repository") as mock:
        yield mock


@pytest.fixture
def mock_tasks_utils():
    with patch("app.conversation_processor.chat_convertor.tasks_utils") as mock:
        yield mock


def test_convert_choice_to_messages():
    chat = ChatVo(
        chat_id="test_id",
        sender_type=SenderType.user,
        sender_id=1,
        create_time="2021-01-01 00:00:00",
        update_time="2021-01-01 00:00:00",
        content_json={
            "choice": {"question": "What is your choice?"},
            "decision": ["Option A", "Option B"],
        },
        content="",
    )
    result = convert_choice_to_messages(chat)
    assert result == [
        AIMessage(content="What is your choice?"),
        HumanMessage(content="Option A、Option B"),
    ]

    template = "some prefix.\n{user_content}"

    result = convert_choice_to_messages(chat, template)
    assert result == [
        AIMessage(content="What is your choice?"),
        HumanMessage(content="some prefix.\nOption A、Option B"),
    ]


def test_convert_continuous_choice_questions_to_messages():
    chat0 = ChatVo(
        chat_id="test_id1",
        sender_type=SenderType.user,
        sender_id=1,
        create_time="2021-01-01 00:00:00",
        update_time="2021-01-01 00:00:00",
        content="",
        content_type=ContentType.choice,
        content_json={
            "choice": {
                "items": ["option1", "option2", "option3"],
                "max_pick": 3,
                "question": "Question A",
            },
            "decision": ["option1", "option2"],
            "following_questions": [
                {
                    "options": ["option a", "option b", "option c"],
                    "question": "Question B",
                    "is_single_choice": True,
                }
            ],
        },
    )

    chat1 = ChatVo(
        chat_id="test_id2",
        sender_type=SenderType.user,
        sender_id=1,
        create_time="2021-01-01 00:00:00",
        update_time="2021-01-01 00:00:00",
        content="",
        content_json={
            "decision": ["option c"],
        },
    )

    chats = [chat0, chat1]

    result = convert_chats_to_messages(chats)

    assert len(result) == 2
    expected_content0 = [
        {
            "items": ["option1", "option2", "option3"],
            "max_pick": 3,
            "question": "Question A",
        },
        {
            "options": ["option a", "option b", "option c"],
            "question": "Question B",
            "is_single_choice": True,
        },
    ]
    assert isinstance(result[0].content, str)
    assert json.loads(result[0].content) == expected_content0

    expected_content1 = ["option1、option2", "option c"]
    assert isinstance(result[1].content, str)
    assert json.loads(result[1].content) == expected_content1
