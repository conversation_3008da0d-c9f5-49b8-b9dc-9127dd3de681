import shutil
import time
import uuid
from pathlib import Path

from fastapi import UploadFile, BackgroundTasks
from openai import NotGiven, NOT_GIVEN

from app.client.s3client import S3Client
from app.config import ASSISTANT_DATA, S3_URL_EXPIRATION_TIME
from app.conversation_processor.utils import upload_user_file_to_s3
from app.conversation_type import PLAN_ASSISTANT_INIT_CONTENT
from app.routers.common import get_user_path
from app.service import disassemble_service, video_service
from app.conversation_processor.base_processor import BaseProcessor
from app.conversation_processor.stateful_processor import StatefulProcessor
from app.conversation_processor.tool_functions import make_choice_questions_function
from app.client.openai_client_v2 import Too<PERSON><PERSON><PERSON>, openai_client
from app.client.prompts import TEMPLATES, FUNCTIONS
from app.db.common_type import ContentType, ChatStatus, TaskStatus, TaskType, SenderType
from app.db.models import ChatEntity, TasksEntity
from app.db.repository import chat_repository, task_repository
from app.logger import logger
from app.service.disassemble_service import get_disassemble_task_link, get_options
from app.utils import langfuse_utils
from app.utils.async_utils import to_sync


def cell_in_chat(content, editable=False, placeholder=""):
    return {
        "content": content,
        "editable": editable,
        "placeholder": placeholder,
    }


def make_derive_form_function(processor, response_chat_id):
    async def make_derive_form(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        preferences = kwargs["preferences"]
        adaptations = kwargs["adaptations"]

        response_chat.content_type = ContentType.derive_form
        response_chat.content = "请确认方案后提交表格："
        response_chat.content_json["video_path"] = processor.related_video_path
        response_chat.content_json["preferences"] = preferences
        response_chat.content_json["adaptations"] = adaptations

        rows = []
        for i in range(len(adaptations)):
            rows.append(
                [
                    cell_in_chat(adaptations[i]["element"]),
                    cell_in_chat(adaptations[i]["original"]),
                    cell_in_chat(adaptations[i]["adaptation"], editable=True),
                ]
            )

        response_chat.content_json["form"] = {
            "headers": ["可替换元素", "原作品", "改变（我的诉求）"],
            "rows": rows,
        }

        response_chat.content_json["conversation_state"] = "default"

        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content=response_chat.content,
            content_type=response_chat.content_type,
            content_json=response_chat.content_json,
            status=ChatStatus.done,
        )

    return make_derive_form


def update_response_chat_if_task_not_done(processor, response_chat_id, task):
    if not task or task.status == TaskStatus.error:
        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content="视频拆解失败，请重新上传",
            status=ChatStatus.error,
        )
        return True
    if task.status == TaskStatus.ongoing:
        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content="视频正在拆解中，请稍后再试",
            status=ChatStatus.done,
        )
        return True
    return False


def show_disassemble_report_function(processor, response_chat_id):
    async def show_disassemble_report(**kwargs):
        video_path = processor.related_video_path
        task = task_repository.get_disassemble_task_by_video_path(video_path)
        if update_response_chat_if_task_not_done(processor, response_chat_id, task):
            return

        content_json = {
            "link": get_disassemble_task_link(task),
            "video_path": video_path,
            "task_type": TaskType.select,
        }
        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            task_id=task.task_id,
            content='👉 视频拆解报告已生成：<span class="chat-btn">点击查看</span>',
            content_json=content_json,
            content_type=ContentType.disasm_report,
            options=get_options(ContentType.disasm_report, TaskType.select),
            status=ChatStatus.done,
        )

    return show_disassemble_report


def extract_subtitles_function(processor, response_chat_id):
    async def extract_subtitles(**kwargs):
        task = task_repository.get_disassemble_task_by_video_path(
            processor.related_video_path
        )
        if update_response_chat_if_task_not_done(processor, response_chat_id, task):
            return

        subtitles = [
            f"{x['start_time']}s - {x['end_time']}s：{x['content']}"
            for x in task.subtitles
        ]

        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content="\n".join(subtitles),
            status=ChatStatus.done,
        )

    return extract_subtitles


def start_derive_function(processor: BaseProcessor, response_chat_id):
    async def start_derive(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        response_chat.content_json["conversation_state"] = "derive"

        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content="我们将帮助您拍摄同款视频。接下来，我们会通过几个问题了解您的喜好，从而为您提供更合适的替换选项。请根据您的偏好回答问题，以便我们为您定制个性化的拍摄建议。让我们开始吧！",
            content_json=response_chat.content_json,
            status=ChatStatus.done,
        )
        # 直接进入 derive 状态，开始提问
        processor.conversation_state = "derive"
        processor.background_tasks.add_task(to_sync(processor.post_process))

    return start_derive


def estimate_by_report_function(processor, response_chat_id, task_type: TaskType):
    async def estimate_by_report(**kwargs):
        task = task_repository.get_disassemble_task_by_video_path(
            processor.related_video_path
        )
        if update_response_chat_if_task_not_done(processor, response_chat_id, task):
            return

        response = await video_service.estimate_by_report(
            task.report,
            task_type,
        )
        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content=response,
        )

    return estimate_by_report


def suggest_by_report_function(processor, response_chat_id, task_type: TaskType):
    async def suggest_by_report(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        task = task_repository.get_disassemble_task_by_video_path(
            processor.related_video_path
        )
        if update_response_chat_if_task_not_done(processor, response_chat_id, task):
            return

        await disassemble_service.suggest_by_report(
            processor.user_id,
            processor.conversation_id,
            task_type,
            {"task_id": task.task_id},
            response_chat,
        )

    return suggest_by_report


class PlanAssistantProcessor(StatefulProcessor):
    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        background_tasks: BackgroundTasks,
    ):
        super().__init__(
            user_id,
            conversation_id,
            content,
            content_type,
            content_json,
            ref_chat_id,
            upload_file,
            background_tasks,
        )
        self.related_video_path = ""
        self.file_path = None
        self.video_id = ""
        self.svideo_path = ""

    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return PLAN_ASSISTANT_INIT_CONTENT, disassemble_service.get_options(
            ContentType.init
        )

    def abort_user_message(self):
        if self.latest_chat and self.latest_chat.status == ChatStatus.ongoing:
            if self.latest_chat.content_type == ContentType.disasm_report:
                return True
        if self.conversation_state == "derive":
            if self.content_type not in [
                ContentType.decision,
                ContentType.derive_confirm,
            ]:
                return True
        return False

    def pre_process(self):
        if self.upload_file:
            svideo_path = f"{ASSISTANT_DATA}/{uuid.uuid4()}.mp4"
            url = upload_user_file_to_s3(
                self.user_id, svideo_path, self.upload_file.file
            )
            self.content_json["file_size"] = self.upload_file.size
            self.content_json["video_url"] = url
            self.content_json["video_path"] = svideo_path
            self.content_json["video_url_expiration_time"] = (
                time.time() + S3_URL_EXPIRATION_TIME
            )
            self.svideo_path = svideo_path
        # 优先检查video_id，后检查video_path。在重新回答时，video_path都存在。
        elif self.content_json and self.content_json.get("video_id"):
            self.video_id = self.content_json.get("video_id")
            self.svideo_path = f"{ASSISTANT_DATA}/{self.video_id}.mp4"
            self.content_json["video_path"] = self.svideo_path
            # 此时前端会同时在content_json中提供video_url，无需再查询ChClient
        elif self.content_json and self.content_json.get("video_path"):
            upload_file_path = self.content_json.get("video_path")
            s3client = S3Client()
            # max 1 week: https://stackoverflow.com/questions/24014306/aws-s3-pre-signed-url-without-expiry-date
            url = s3client.get_obj_download_url(
                upload_file_path,
                get_user_path(self.user_id),
                expiration=S3_URL_EXPIRATION_TIME,
                preview=True,
            )
            if not url:
                raise Exception(f"get s3 url {upload_file_path} failed")
            self.content_json["video_url"] = url
            self.content_json["video_path"] = upload_file_path
            self.content_json["video_url_expiration_time"] = (
                time.time() + S3_URL_EXPIRATION_TIME
            )

            # download video to local
            video_filepath = s3client.download_obj(
                filepath=upload_file_path, user_path=get_user_path(self.user_id)
            )
            if not video_filepath:
                raise Exception(f"download video error: {upload_file_path}")
            # 在拆解任务完成后，进行本地文件删除
            self.file_path = Path(video_filepath)
            self.svideo_path = upload_file_path

    async def do_reply(self):
        if self.content_type == ContentType.derive_confirm:
            await disassemble_service.disassemble_derive(
                self.user_id,
                self.conversation_id,
                self.chat,
                self.response_chat_id,
            )
            return
        if self.is_video_upload_chat(self.chat) and self.conversation_state != "derive":
            video_path = self.content_json["video_path"]
            file_path = Path("tmp", get_user_path(self.user_id), video_path)
            try:
                await disassemble_service.disassemble_file(
                    self.user_id,
                    self.conversation_id,
                    svideo_path=video_path,
                    chat=self.chat,
                    response_chat_id=self.response_chat_id,
                    file_path=file_path,
                )
            except Exception:
                chat_repository.update_chat(
                    self.user_id,
                    self.conversation_id,
                    self.response_chat_id,
                    content="视频分析出错，请重试上传",
                    content_type=ContentType.normal,
                    status=ChatStatus.error,
                )
                return
            if self.content.strip() == "":
                chat_repository.update_chat(
                    self.user_id,
                    self.conversation_id,
                    self.response_chat_id,
                    content="""😀 主人，我已经浏览完成并理解您给我的内容，现在您不但可以向我提问，我也可以：
1.总结视频内容：对上传的视频内容进行总结。
2.生成分镜脚本：对上传的视频进行深度拆解，包括视频结构、内容、标签等方面的分析。
3.视频拆解报告：提供全面的解析报告，帮助用户更好地理解和优化视频内容。
4.拍摄脚本创作：为短视频创作拍摄脚本，确保视频具有高质量的影像和叙事结构。
5.提取视频台词：从视频中提取文字内容，满足对台词脚本的撰写需求。
6.生成拍摄清单：提供详细的拍摄清单，帮助录像制作的各个环节。
7.预估拍摄成本：计算拍摄流程中的费用，帮助用户经济更好地管理。
8.推荐类似视频：基于现有视频风格和主题，推荐行业内的成功案例，供用户参考。""",
                    content_type=ContentType.normal,
                    status=ChatStatus.done,
                )
                return
        await super().do_reply()

    def get_response_processing_content(self) -> (str, ContentType):
        if self.content_type == ContentType.derive_confirm:
            return "正在生成分镜脚本报告...", ContentType.disasm_report
        if "video_path" in self.content_json:
            return "正在分析视频，请等待...", ContentType.disasm_report
        return super().get_response_processing_content()

    def get_system_message_content(self) -> str:
        if self.conversation_state == "derive":
            return langfuse_utils.get_prompt(
                "planner_derive_system",
                TEMPLATES["planner"]["derive_system_multiple_question_version"],
            )
        return langfuse_utils.get_prompt(
            "planner_system",
            TEMPLATES["planner"]["system"],
            video_path=self.related_video_path,
        )

    def get_latest_user_content_template(self) -> str | None:
        return f"""@@@@指令START@@@@
{"用户上传了视频：" + self.related_video_path if self.related_video_path is not None else "用户还没有上传视频"}
@@@@指令END@@@@

{{user_content}}
"""

    def get_functions(self, messages) -> list[ToolCall]:
        if self.conversation_state == "derive":
            return [
                ToolCall(
                    "make_derive_form",
                    make_derive_form_function(self, self.response_chat_id),
                    FUNCTIONS["make_derive_form"],
                ),
                ToolCall(
                    "make_choice_questions",
                    make_choice_questions_function(self, self.response_chat_id),
                    FUNCTIONS["make_choice_questions"],
                ),
            ]

        return [
            ToolCall(
                "show_disassemble_report",
                show_disassemble_report_function(self, self.response_chat_id),
                FUNCTIONS["show_disassemble_report"],
            ),
            ToolCall(
                "extract_subtitles",
                extract_subtitles_function(self, self.response_chat_id),
                FUNCTIONS["extract_subtitles"],
            ),
            ToolCall(
                "start_derive",
                start_derive_function(self, self.response_chat_id),
                FUNCTIONS["start_derive"],
            ),
            ToolCall(
                "make_checklist",
                estimate_by_report_function(
                    self, self.response_chat_id, TaskType.make_checklist
                ),
                FUNCTIONS["make_checklist"],
            ),
            ToolCall(
                "estimate_cost",
                estimate_by_report_function(
                    self, self.response_chat_id, TaskType.estimate_cost
                ),
                FUNCTIONS["estimate_cost"],
            ),
            ToolCall(
                "suggest_similar",
                suggest_by_report_function(
                    self, self.response_chat_id, TaskType.suggest_similar
                ),
                FUNCTIONS["suggest_similar"],
            ),
        ]

    def get_tool_choice(self) -> str | NotGiven:
        if self.conversation_state == "derive":
            return "required"
        return NOT_GIVEN

    def get_option_prompt_content(self) -> str | None:
        if self.conversation_state == "derive":
            return None
        return langfuse_utils.get_prompt(
            "default_option", TEMPLATES["option"]["default"]
        )

    # 置顶最近一条视频拆解报告或被引用的拆解报告
    def get_top_chat(self) -> ChatEntity | None:
        top_chat = None
        # 先找到最近一条包含拆解报告的消息
        task_chats = (
            chat_repository.get_chat_list_by_conversation_id_and_task_id_not_null(
                self.conversation_id
            )
        )
        for c in task_chats:
            if "task_type" not in c.content_json:
                continue
            if c.content_json["task_type"] == TaskType.select:
                top_chat = c
                self.related_video_path = c.content_json["video_path"]
                break
        if not top_chat:
            return None
        # 找一下这之后还有没有被引用的拆解报告
        ref_chats = chat_repository.get_chat_list_by_conversation_id_and_ref_chat_id_not_null_and_create_time_after(
            self.conversation_id,
            top_chat.create_time,
        )
        for c in ref_chats:
            if "task_type" in c.ref_chat and c.ref_chat["task_type"] == TaskType.select:
                top_chat = c
                self.related_video_path = c.ref_chat["video_path"]
                break
        return top_chat

    def get_max_history(self) -> int:
        return 20

    @staticmethod
    def is_video_upload_chat(chat) -> bool:
        return "video_path" in chat.content_json
