from abc import ABC

from fastapi import UploadFile, BackgroundTasks

from app.client.openai_client_v2 import ToolCall
from app.config import KNOWLEDGE_BASE_NAME
from app.conversation_processor.base_processor import BaseProcessor
from app.logger import logger


# 通过区分会话的状态，让助手更加 focus
# 所有功能都放在一个状态中，会使提示词变得冗长，AI 的响应也相对不稳定
# 需要提供状态转换的 tool call
# 一个典型的场景是策划助手拍同款流程
class StatefulProcessor(BaseProcessor, ABC):

    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        background_tasks: BackgroundTasks,
        knowledge_base_name=KNOWLEDGE_BASE_NAME,
    ):
        super().__init__(
            user_id,
            conversation_id,
            content,
            content_type,
            content_json,
            ref_chat_id,
            upload_file,
            background_tasks,
            knowledge_base_name,
        )
        self.conversation_state = "default"
        self.__init_state()

    def __init_state(self):
        # 根据最近的聊天记录判断会话的状态
        history = self.get_history_chats()
        for chat in history:
            if chat.content_json and "conversation_state" in chat.content_json:
                self.conversation_state = chat.content_json["conversation_state"]
        logger.info(
            f"conversation {self.conversation_state} state is {self.conversation_state}"
        )
