import shutil
import time
from pathlib import Path
from tempfile import NamedTemporaryFile
from fastapi import UploadFile, BackgroundTasks

from app.client.s3client import S3Client
from app.config import ASSISTANT_DATA, S3_URL_EXPIRATION_TIME
from app.db.common_type import ContentType, KnowledgeBaseType
from app.db.repository import chat_conversation_repository, knowledge_file_repository
from app.knowledge_base.kb_api import delete_docs
from app.logger import logger
from app.routers.common import get_user_path
from app.service import read_assistant_service
from .base_processor import BaseProcessor, ToolCall
from .tool_functions import read_one_book_function, search_knowledge_base_function
from ..client.prompts import TEMPLATES, FUNCTIONS
from ..utils import langfuse_utils


class KnowledgeBaseProcessor(BaseProcessor):
    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        background_tasks: BackgroundTasks,
    ):
        knowledge_base_name = chat_conversation_repository.get_knowledge_base_name(
            conversation_id, user_id
        )
        super().__init__(
            user_id,
            conversation_id,
            content,
            content_type,
            content_json,
            ref_chat_id,
            upload_file,
            background_tasks,
            knowledge_base_name=knowledge_base_name,
        )
        self.file_name = ""
        self.file_body = None
        self.file_path = ""
        self.file_url = ""
        self.doc_info_list = knowledge_file_repository.list_file_info_from_db(
            self.knowledge_base_name
        )

    def pre_process(self):
        if self.upload_file:
            try:
                logger.info(
                    f"create background task upload file: {self.upload_file.filename}"
                )
                assert self.upload_file.filename
                suffix = Path(self.upload_file.filename).suffix
                with NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
                    shutil.copyfileobj(self.upload_file.file, tmp)
                    tmp_path = Path(tmp.name)
                body = tmp_path.read_bytes()
                file_size = len(body)
                self.content_json["file_size"] = file_size
                file_path = f"{ASSISTANT_DATA}/{self.upload_file.filename}"
                s3client = S3Client()
                res = s3client.put_obj(file_path, body, get_user_path(self.user_id))
                if not res:
                    raise Exception(f"save file to s3 failed: {file_path}")
                url = s3client.get_obj_download_url(
                    file_path,
                    get_user_path(self.user_id),
                    expiration=S3_URL_EXPIRATION_TIME,
                    preview=True,
                )
                if not url:
                    raise Exception(f"get s3 url {file_path} failed")
                self.content_json["file_url"] = url
                self.content_json["file_path"] = file_path
                self.content_json["file_name"] = self.upload_file.filename
                self.content_json["file_url_expiration_time"] = (
                    time.time() + S3_URL_EXPIRATION_TIME
                )
                self.file_name = self.upload_file.filename
                self.file_body = body
                self.file_url = url

                # 直接传到后台任务过去再read的时候原来的Uploadfile已经close了，且无法再open，直接传文件内容到background task
            finally:
                self.upload_file.file.close()
        elif self.content_json and self.content_json.get("file_path"):
            upload_file_path = self.content_json.get("file_path")
            s3client = S3Client()
            url = s3client.get_obj_download_url(
                upload_file_path,
                get_user_path(self.user_id),
                expiration=S3_URL_EXPIRATION_TIME,
                preview=True,
            )
            if not url:
                raise Exception(f"get s3 url {upload_file_path} failed")
            self.content_json["file_url"] = url
            self.content_json["file_path"] = upload_file_path
            self.content_json["file_url_expiration_time"] = (
                time.time() + S3_URL_EXPIRATION_TIME
            )
            # download file to local
            filepath = s3client.download_obj(
                filepath=upload_file_path, user_path=get_user_path(self.user_id)
            )
            if not filepath:
                raise Exception(f"download file error: {upload_file_path}")
            self.file_path = filepath  # file_name和file_body从file_path取得
            self.file_url = url
            # 在拆解任务完成后，进行本地文件删除

    def do_clear_context(self):
        # 删除知识库文件
        files_to_delete = [file.file_name for file in self.doc_info_list]
        logger.info(
            f"delete knowledge base {self.knowledge_base_name} files: {files_to_delete}"
        )
        delete_docs(
            knowledge_base_name=self.knowledge_base_name,
            knowledge_base_type=KnowledgeBaseType.user,
            file_names=files_to_delete,
            delete_content=True,
        )

    def show_progress_default(self) -> bool:
        _, content_type = self.get_response_processing_content()
        return content_type == ContentType.disasm_report

    async def do_reply(self):
        if self.file_name or self.file_path:
            self.background_tasks.add_task(
                read_assistant_service.init_kb_by_file,
                self.user_id,
                self.conversation_id,
                self.file_name,
                self.file_body,
                self.file_path,
                self.response_chat_id,
                self.file_url,
            )
            return
        await super().do_reply()

    def get_response_processing_content(self) -> tuple[str, ContentType]:
        if "报告" in self.content:
            return "正在生成读书报告，请等待...", ContentType.disasm_report
        if self.file_name or self.file_path:
            return "正在全力阅读中，请等待...", ContentType.normal
        return super().get_response_processing_content()

    def get_system_message_content(self) -> str:
        doc_list = (
            "\n".join(f"{doc.file_id}  {doc.file_name}" for doc in self.doc_info_list)
            if self.doc_info_list
            else ""
        )

        return langfuse_utils.get_prompt(
            "document_system",
            TEMPLATES["document"]["system"],
            doc_list=doc_list,
        )

    def get_option_prompt_content(self):
        return langfuse_utils.get_prompt(
            "document_option", TEMPLATES["option"]["document"]
        )

    def get_functions(self, messages) -> list[ToolCall]:
        return [
            ToolCall(
                "search_knowledge_base",
                search_knowledge_base_function(self, self.response_chat_id),
                FUNCTIONS["search_knowledge_base"],
                priority=1,
            ),
            ToolCall(
                "read_one_book",
                read_one_book_function(self, self.response_chat_id),
                FUNCTIONS["read_one_book"],
                priority=0,
            ),
        ]
