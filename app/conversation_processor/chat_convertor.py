import json
import re
from typing import Any

from langchain_core.messages import AIMessage, HumanMessage, AnyMessage, BaseMessage
from langchain_core.messages.utils import convert_to_messages
from app.db.vo import Chat as ChatVo, TasksVo
from app.db.common_type import SenderType, ContentType, TaskStatus, TaskType
from app.db.models import ChatEntity, TasksEntity
from app.db.repository import task_repository
from app.service import document_service
from app.utils import tasks_utils


def _strip_ytext(text: str) -> str:
    """去除字符串中所有 `<yText>…</yText>` 标签，返回纯文本。"""

    if "<yText>" not in text:
        return text

    # 先定义解析单个 yText 序列化块的内部函数
    def _parse_single_ytext(ytext: str) -> str:
        if not ytext.startswith("<yText>") or not ytext.endswith("</yText>"):
            return ytext

        try:
            json_str = ytext[len("<yText>") : -len("</yText>")]
            segments = json.loads(json_str)
            if isinstance(segments, list):
                return "".join(
                    seg[0] for seg in segments if isinstance(seg, (list, tuple)) and seg
                )
        except Exception:
            # 解析失败保持原样
            pass
        return ytext

    # 使用正则替换文本中所有 yText 片段
    pattern = re.compile(r"<yText>.*?</yText>", re.DOTALL)
    return pattern.sub(lambda m: _parse_single_ytext(m.group(0)), text)


def convert_choice_to_messages(
    chat: ChatVo, template: str | None = None
) -> list[BaseMessage]:
    choice = chat.content_json["choice"]
    content: str = str(choice["question"])
    messages: list[BaseMessage] = [AIMessage(content)]
    if "decision" in chat.content_json:
        decision: list[str] = chat.content_json["decision"]  # type: ignore
        content = "、".join(decision)
        if template:
            content = template.format(user_content=content)
        messages.append(HumanMessage(content))
    return messages


def convert_chat_to_messages(
    chat: ChatVo, template: str | None = None
) -> list[BaseMessage]:
    """Processor 架构下要求使用 langchain 的 BaseMessage 类型"""
    role = "assistant" if chat.sender_type == SenderType.agent else "user"
    content: str = str(chat.content)
    if role == "user" and template:
        content = template.format(user_content=content)
    if (
        chat.content_type == ContentType.normal
        and "conversation_state" in chat.content_json
        and chat.content_json["conversation_state"] == "derive"
    ):
        # 这是个多出来的提示内容，要删除（拍同款相关）
        return []
    if chat.content_type == ContentType.choice:
        return convert_choice_to_messages(chat, template)
    if "task_type" in chat.content_json:
        if chat.content_json["task_type"] == TaskType.select:
            return convert_to_messages(
                [
                    {
                        "role": role,
                        "content": convert_task_to_message_content(
                            content, chat.task_id
                        ),
                    }
                ]
            )
    # 处理带有 liangzhidoc:// 链接的机器回答，用于将文档内容嵌入到上下文中
    if str(chat.sender_type) == str(SenderType.agent):
        # 匹配所有 liangzhidoc:// 链接
        matches = re.finditer(r"\[(.*?)\]\((liangzhidoc://.*?)\)", content)
        doc_contents = []
        for match in matches:
            link_title = match.group(1)
            link = match.group(2)
            # 获取文档内容
            doc_id = link.split("/")[-1]
            markdown = document_service.get_md_doc(chat.sender_id, doc_id)  # type: ignore
            markdown = _strip_ytext(markdown)
            doc_contents.append(f"《{link_title}》\n\n{markdown}")
        if doc_contents:
            # 替换所有链接为标题
            content = re.sub(r"\[(.*?)\]\((liangzhidoc://.*?)\)", r"《\1》", content)

            return convert_to_messages(
                [
                    {
                        "role": role,
                        "content": f"{content}\n\n===\n\n"
                        + "\n\n===\n\n".join(doc_contents),
                    }
                ]
            )
    if "imitate" in chat.content_json:
        imitate_report_url = chat.content_json["imitate"]["report_url"]
        report_doc_id = imitate_report_url.split("/")[-1]
        markdown = document_service.get_md_doc(chat.sender_id, report_doc_id)  # type: ignore
        return convert_to_messages(
            [
                {
                    "role": role,
                    "content": f"{content}\n\n===\n\n拍同款报告内容如下：\n\n```\n{markdown}\n```",
                }
            ]
        )

    return convert_to_messages(
        [
            {
                "role": role,
                "content": content,
            }
        ]
    )


def convert_chats_to_messages(
    chats: list[ChatVo], template: str | None = None
) -> list[BaseMessage]:
    if not chats:
        return []
    chat = chats[0]
    messages = []
    if chat.content_type == ContentType.choice:
        content1: list[Any] = [chat.content_json["choice"]]
        if "following_questions" in chat.content_json:
            content1 += chat.content_json["following_questions"]
        messages.append(AIMessage(json.dumps(content1)))
        user_decisions = []
        for i in range(len(content1)):
            chat = chats[0]
            chats = chats[1:]
            if "decision" in chat.content_json:
                user_decisions.append("、".join(chat.content_json["decision"]))
        messages.append(HumanMessage(json.dumps(user_decisions)))
    else:
        chats = chats[1:]
        # 处理消息引用
        if chat.ref_chat and not chat.ref_chat.get("ref_adjacent", False):
            message_type = (
                AIMessage
                if chat.ref_chat["sender_type"] == SenderType.agent
                else HumanMessage
            )
            content2: str = str(chat.ref_chat["content"])
            task_id = chat.ref_chat.get("task_id")
            if task_id:
                content2 = convert_task_to_message_content(
                    content=content2, task_id=task_id
                )
            messages.append(message_type(content2))
        # 最后一条用户消息可能会附带额外指令提示
        messages += convert_chat_to_messages(chat, None if chats else template)
    messages += convert_chats_to_messages(chats, template)
    return messages


def convert_task_to_message_content(
    content="", task_id: int | None = None, task: TasksVo | None = None
) -> str:
    if not task_id and not task:
        return ""
    if not task:
        assert task_id
        task = task_repository.get_task_by_id_without_user(task_id)
        if not task:
            return ""
    if task.task_type == TaskType.select:
        if task.status == TaskStatus.error:
            return f"内容拆解失败，请重新上传"
        if task.status == TaskStatus.ongoing:
            return f"内容正在拆解中，请稍后再试"
        assert task.report
        report = tasks_utils.convert_video_summarize_report_to_markdown(task.report)
        content = f"""{content or "用户未输入任何内容，请询问用户意图"}

===

用户上传的视频/图文的拆解报告如下：

```
{report}
```
若用户上传的是视频，下面是该视频的音频转文字，是一条json格式的列表，包含了每段文字对应的开始时间点（单位为秒）、结束时间点（单位为秒）及内容（注意你获得的文字可能是视频BGM中的歌词，注意分辨)。
若用户上传的为图文，下面为空。
```
        {task.subtitles}
        ```
        """
        return content
    if content:
        return f"{content}\n{task.report}"
    return f"{task.report}"
