from unittest.mock import Magic<PERSON><PERSON>, Mock, patch, AsyncMock, ANY

import pytest

from app.client.openai_client_v2 import <PERSON><PERSON><PERSON><PERSON>
from app.conversation_processor.base_processor import BaseProcessor
from app.conversation_processor.demo_processor import DemoProcessor
from app.conversation_processor.tool_functions import ChoiceQuestion
from app.db.common_type import TaskType, SenderType, ContentType, ChatStatus

background_tasks = MagicMock()


@pytest.fixture
def mock_chat_repository():
    with patch(
        "app.conversation_processor.base_processor.chat_repository"
    ) as mock_repo:
        yield mock_repo


@pytest.fixture
def mock_task_repository():
    with patch("app.conversation_processor.base_processor.task_repository") as mock:
        yield mock


def test_update_ref_chat(mock_chat_repository, mock_task_repository):
    mock_chat_repository.get_chat.return_value = Mock(
        chat_id="test_ref_chat_id",
        task_id="test_task_id",
        content="test_ref_chat_content",
        sender_type=SenderType.agent,
        content_json={},
    )
    mock_chat_repository.get_latest_messages.return_value = [
        Mock(chat_id="test_latest_chat_id")
    ]

    mock_task_repository.get_task.return_value = Mock(
        task_id="test_task_id",
        task_type=TaskType.select,
        svideo_path="/path/to/video",
    )
    processor = DemoProcessor(
        user_id=1,
        conversation_id="test_conversation",
        content="test_content",
        content_type=ContentType.normal,
        content_json={"test_key": "test_value"},
        ref_chat_id="test_ref_chat_id",
        upload_file=None,
        background_tasks=background_tasks,
    )

    assert processor.user_id == 1
    assert processor.content == "test_content"
    assert processor.conversation_id == "test_conversation"
    assert processor.content_type == ContentType.normal
    assert processor.content_json == {"test_key": "test_value"}
    assert processor.options == []
    assert processor.ref_chat_id == "test_ref_chat_id"
    assert processor.ref_chat == {
        "content": "test_ref_chat_content",
        "sender_type": SenderType.agent,
        "content_json": {},
        "ref_adjacent": False,
        "task_id": "test_task_id",
        "task_type": TaskType.select,
        "video_path": "/path/to/video",
    }


@pytest.fixture
def processor():
    return DemoProcessor(
        user_id=1,
        conversation_id="test_conversation",
        content="test_content",
        content_type=ContentType.normal,
        content_json={"test_key": "test_value"},
        ref_chat_id="",
        upload_file=None,
        background_tasks=Mock(),
    )


def test_process_message(processor, mock_chat_repository):
    mock_chat_repository.insert_chat.return_value = None
    processor.post_process = AsyncMock()
    processor.process_message()

    mock_chat_repository.insert_chat.assert_called_once_with(
        1,
        SenderType.user,
        "test_content",
        "test_conversation",
        content_type=ContentType.normal,
        content_json={"test_key": "test_value"},
        options=[],
        ref_chat_id="",
        ref_chat={},
        chat_id=ANY,
        trace_id=ANY,
    )


def test_post_process_decision(processor, mock_chat_repository):
    question_a = ChoiceQuestion(
        "Question A", ["option1", "option2", "option3"], False, True
    )
    question_b = ChoiceQuestion(
        "Question B", ["option a", "option b", "option c"], True, True
    )
    processor.chat = Mock(
        content_json={
            "choice": question_a.to_json(),
            "decision": ["option1", "option2"],
            "following_questions": [
                {
                    "options": ["option a", "option b", "option c"],
                    "question": "Question B",
                    "is_single_choice": True,
                }
            ],
        }
    )
    processor.response_chat_id = "test_response_chat_id"

    mock_chat_repository.update_chat.return_value = None
    assert processor._BaseProcessor__post_process_decision() == True
    mock_chat_repository.update_chat.assert_called_once_with(
        1,
        "test_conversation",
        "test_response_chat_id",
        content_type=ContentType.choice,
        content_json={
            "choice": question_b.to_json(),
            "following_questions": [],
            "subsequent": True,
        },
        status=ChatStatus.done,
        trace_id=ANY,
    )


def test_get_history_chats(processor, mock_chat_repository):
    processor.get_top_chat = Mock(return_value=Mock(chat_id=1))
    mock_chat_repository.to_chat_schema = Mock(return_value=Mock(chat_id=1))

    mock_chat_repository.get_latest_messages.return_value = [
        Mock(chat_id=2),
        Mock(chat_id=3),
        Mock(chat_id=4),
    ]

    history = processor.get_history_chats()
    assert [x.chat_id for x in history] == [1, 4, 3]

    mock_chat_repository.get_latest_messages.return_value = [
        Mock(chat_id=2),
        Mock(chat_id=3),
        Mock(chat_id=1),
        Mock(chat_id=4),
    ]

    history = processor.get_history_chats()
    assert [x.chat_id for x in history] == [1, 3]
