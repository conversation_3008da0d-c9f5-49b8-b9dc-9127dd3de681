import asyncio

from langfuse.decorators import langfuse_context, observe

from app.client.openai_client_v2 import <PERSON>lCall
from app.conversation_processor.base_processor import BaseProcessor
from app.db.common_type import ChatStatus, ContentType
from app.db.repository import chat_repository
from app.logger import logger
from app.service.search_agent.search_agent import new_search_agent_graph


class SearchAgentProcessor(BaseProcessor):

    async def do_reply(self):
        update_chat = chat_repository.updater(self.user_id, self.conversation_id)
        is_error = False
        try:
            await self._do_reply(update_chat)
            self.package_response_content()
        except Exception as e:
            logger.warning(e, exc_info=True)
            is_error = True
        finally:
            if is_error:
                chat_status = ChatStatus.error
            else:
                chat_status = ChatStatus.done
            update_chat(
                self.response_chat_id,
                progress=1,
                status=chat_status,
            )

    @observe(name="search_agent")
    async def _do_reply(self, update_chat):
        thread_id = self.response_chat_id
        graph_input = {
            "messages": [self.content],
            "conversation_id": self.conversation_id,
        }

        graph_config = {
            "callbacks": [],
            "configurable": {"thread_id": thread_id},
        }
        state = new_search_agent_graph().invoke(graph_input, config=graph_config)
        update_chat(
            self.response_chat_id,
            content_type=ContentType.normal,
            content=state["messages"][-1].content,
            docs=state.get("docs", []),
            videos=state.get("videos", []),
        )
