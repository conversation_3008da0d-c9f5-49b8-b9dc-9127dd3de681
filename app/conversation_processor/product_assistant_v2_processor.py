from langfuse.decorators import observe, langfuse_context

from app.client.openai_client_v2 import ToolCall
from app.client.prompts import TEMPLATES
from app.conversation_processor.base_processor import BaseProcessor
from app.conversation_processor.chat_convertor import (
    convert_chats_to_messages,
)
from app.db.common_type import Chat<PERSON>tatus, ContentType
from app.db.repository import chat_repository
from app.logger import logger
from app.conversation_type import PRODUCT_ASSISTANT_INIT_CONTENT
from app.service import document_service
from app.service.product_assistant_graph.assistant import graph
from app.utils import langfuse_utils, blocksuite_utils


class ProductAssistantV2Processor(BaseProcessor):
    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return PRODUCT_ASSISTANT_INIT_CONTENT, []

    @observe(name="product_assistant_v2")
    async def do_reply(self):
        history_chats = self.get_history_chats()
        messages = convert_chats_to_messages(history_chats)

        progress_content = ""

        def progress_updater(progress: str):
            nonlocal progress_content
            if progress_content:
                progress_content += "\t\U00002714\n"
            progress_content += progress
            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                content=progress_content,
            )

        try:
            response = await graph.ainvoke(
                {"messages": messages, "progress_updater": progress_updater},
                config={"callbacks": []},
            )
            content = response["messages"][-1].content
            content_json = {}
            if response.get("need_generate_report"):
                affine_url = document_service.import_markdown_doc(
                    content, user_id=self.user_id
                )
                content = '👉 报告已生成：<span class="chat-btn">点击查看</span>'
                # todo (GSQ) 封面图 先hardcode一张默认产品报告封面
                report_thumbnail_url = "https://gptcloud.oss-jiaxing.sihe.cloud/knowledge_base/image/product_report_cover.png"
                content_json = {
                    "product_report_cover": report_thumbnail_url,
                    "product_report_url": affine_url,
                }

            docs = response.get("docs", [])

            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                content=content,
                content_json=content_json,
                docs=docs,
                status=ChatStatus.done,
                content_type=ContentType.normal,
            )

            self.package_response_content()

            await self.generate_response_options(
                convert_chats_to_messages(history_chats)
            )
        except Exception as e:
            logger.error(f"ProductAssistantV2Processor error: ", exc_info=e)
            chat_repository.update_chat(
                self.user_id,
                self.conversation_id,
                self.response_chat_id,
                content="出错了，请稍后再试",
                status=ChatStatus.error,
                content_type=ContentType.normal,
            )
