import json

from pydantic import BaseModel

from app.ch.model import DateTimeEnumEncoder
from app.client.openai_client import OpenA<PERSON>lient
from app.config import PRODUCT_KB_NAME, KNOWLEDGE_BASE_NAME
from app.db.common_type import ContentType
from app.db.models import ChatEntity
from app.db.repository import chat_repository
from app.logger import logger, exception_logger
from app.utils.common_utils import extract_text_from_md_content

openai_client = OpenAIClient()


def add_videos_to_chat(chat: ChatEntity, videos):
    chat.videos = chat.videos or []
    chat.videos += [
        json.loads(json.dumps(v.__dict__, cls=DateTimeEnumEncoder)) for v in videos
    ]


def add_docs_to_chat(chat: ChatEntity, docs):
    chat.docs = chat.docs or []
    chat.docs += [
        json.loads(json.dumps(d.__dict__, cls=DateTimeEnumEncoder)) for d in docs
    ]


# FIXME: 原来代码逻辑中 get_hot_video_by_keyword 和 get_marketing_video 最多只会调用一个
def get_hot_video_by_keyword_function(processor, response_chat_id):
    @exception_logger
    async def get_hot_video_by_keyword(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        r_video_disassembles, r_videos = await openai_client.get_hot_video_by_keyword(
            kwargs["input"], conv_id=processor.conversation_id
        )
        video_content = ""
        video_i = len(response_chat.videos or [])
        for idx, video in enumerate(r_videos):
            video_content += "\n****相关视频START({})****\n".format(video_i + 1)
            video_content += f"\n{r_video_disassembles[idx]}\n"
            video_content += "\n****相关视频END({})****\n".format(video_i + 1)
            video_i += 1
        add_videos_to_chat(response_chat, r_videos)

        def update_chat_videos():
            chat_repository.update_chat(
                processor.user_id,
                processor.conversation_id,
                response_chat_id,
                videos=response_chat.videos,
            )

        processor.register_package_response_content_hook(update_chat_videos)
        return video_content

    return get_hot_video_by_keyword


def get_marketing_video_function(processor, response_chat_id):
    async def get_marketing_video(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        r_video_disassembles, r_videos = await openai_client.get_hot_video_by_keyword(
            input="营销", conv_id=processor.conversation_id
        )
        video_content = ""
        video_i = len(response_chat.videos or [])
        for idx, video in enumerate(r_videos):
            video_content += "\n****相关视频START({})****\n".format(video_i + 1)
            video_content += f"\n{r_video_disassembles[idx]}\n"
            video_content += "\n****相关视频END({})****\n".format(video_i + 1)
            video_i += 1
        add_videos_to_chat(response_chat, r_videos)

        def update_chat_videos():
            chat_repository.update_chat(
                processor.user_id,
                processor.conversation_id,
                response_chat_id,
                videos=response_chat.videos,
            )

        processor.register_package_response_content_hook(update_chat_videos)
        return video_content

    return get_marketing_video


def search_knowledge_base_function(processor, response_chat_id):
    async def search_knowledge_base(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        knowledge_base_name = (
            processor.knowledge_base_name
            if processor.knowledge_base_name
            else KNOWLEDGE_BASE_NAME
        )
        kb_content, r_docs = await openai_client.search_knowledge_base(
            kwargs["input"], knowledge_base_name, processor.user_id, "", []
        )
        doc_content = ""
        doc_i = len(response_chat.docs or [])
        for doc in r_docs:
            doc_content += "\n****相关书籍节选START({})****\n".format(doc_i + 1)
            doc_content += "\n引用id: \n" + str(doc_i + 1) + "\n"
            doc_content += "\n书籍名称: \n" + doc.filename + "\n"
            # 针对产品助手，需要移除page_content中的图标标签（affine文档元素，暂时无法直接展示）
            # 这里需要两次提取，因为有些标签可能会rag换行截断，第一次将换行拼接后再提取
            if doc.knowledge_base_name == PRODUCT_KB_NAME:
                doc.page_content = extract_text_from_md_content(
                    extract_text_from_md_content(doc.page_content)
                )
            doc_content += "\n节选内容: \n" + doc.page_content + "\n"
            doc_content += "\n****相关书籍节选END({})****\n".format(doc_i + 1)
            doc_i += 1
        add_docs_to_chat(response_chat, r_docs)

        def update_chat_docs():
            chat_repository.update_chat(
                processor.user_id,
                processor.conversation_id,
                response_chat_id,
                docs=response_chat.docs,
            )

        processor.register_package_response_content_hook(update_chat_docs)
        return doc_content

    return search_knowledge_base


def read_one_book_function(processor, response_chat_id):
    async def read_one_book(**kwargs):
        logger.info("需要调用read_one_book，总结一本书作为输出，直接忽略其他函数调用")
        # TODO(lxc): 目前还没有遇到过这种情况，如有遇到再具体分析，考虑一些默认值
        if (
            "file_name" not in kwargs
            or "file_id" not in kwargs
            or "book_type" not in kwargs
            or "result_type" not in kwargs
        ):
            logger.error("GPT调用read_one_book时参数出错: {}".format(kwargs))
            raise Exception("总结失败，请稍后再试")

        filename = kwargs["file_name"]
        file_id = kwargs["file_id"]
        book_type = kwargs["book_type"]
        result_type = kwargs["result_type"]
        # 如果需要生成读书报告，那么创建后台任务
        if result_type == "report":
            logger.info(
                "使用后台任务调用generate_book_report, kwargs为{}".format(kwargs)
            )
            logger.info(
                f"kb {processor.knowledge_base_name}, filename {filename}, file_id {file_id}, user_id {processor.user_id}, conversation_id {processor.conversation_id}"
            )
            await openai_client.generate_book_report(
                file_name=filename,
                file_id=file_id,
                book_type=book_type,
                result_type=result_type,
                doc_info_list=processor.doc_info_list,
                knowledge_base_name=processor.knowledge_base_name,
                chat_id=response_chat_id,
                user_id=processor.user_id,
                conversation_id=processor.conversation_id,
            )
        else:
            read_result = {}
            async for item in openai_client.read_one_book(
                file_name=filename,
                file_id=file_id,
                book_type=book_type,
                result_type=result_type,
                knowledge_base_name=processor.knowledge_base_name,
                doc_info_list=processor.doc_info_list,
                chat_response=[],
                yield_delta=False,
                read_result=read_result,
                chat_id=response_chat_id,
                conversation_id=processor.conversation_id,
                user_id=processor.user_id,
            ):
                yield item

    return read_one_book


def search_and_generate_product_report_function(processor, response_chat_id):
    async def search_and_generate_product_report(**kwargs):
        # 1.先搜索知识库
        if "input" not in kwargs:
            logger.error(
                "GPT调用search_and_generate_product_report时参数出错: {}".format(kwargs)
            )
            raise Exception("总结失败，请稍后再试")

        knowledge_base_name = (
            processor.knowledge_base_name
            if processor.knowledge_base_name
            else KNOWLEDGE_BASE_NAME
        )
        _, docs = await openai_client.search_knowledge_base(
            input=kwargs["input"],
            knowledge_base_name=knowledge_base_name,
            user_id=processor.user_id,
        )
        # 2. 创建生成产品报告任务
        logger.info("使用后台任务调用generate_product_report")
        await openai_client.generate_product_report(
            docs=docs,
            chat_id=response_chat_id,
            user_id=processor.user_id,
            conversation_id=processor.conversation_id,
        )

    return search_and_generate_product_report


def make_choice_question_function(processor, response_chat_id):
    async def make_choice_question(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        question = kwargs["question"]
        options = kwargs["options"]
        response_chat.content_type = ContentType.choice
        response_chat.content_json["choice"] = {"question": question, "items": options}

        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content_type=response_chat.content_type,
            content_json=response_chat.content_json,
        )

    return make_choice_question


class ChoiceQuestion(BaseModel):
    question: str
    options: list[str]
    is_single_choice: bool = True
    allow_other: bool = True
    max_pick: int = 1

    def __init__(
        self, question: str, options: list[str], is_single_choice=True, allow_other=True
    ):
        max_pick = 1 if is_single_choice else len(options) + (1 if allow_other else 0)
        super().__init__(
            question=question,
            options=options,
            is_single_choice=is_single_choice,
            allow_other=allow_other,
            max_pick=max_pick,
        )

    def to_json(self):
        return {
            "question": self.question,
            "options": self.options,
            "items": self.options,  # 目前前端是认这个字段的
            "is_single_choice": self.is_single_choice,
            "allow_other": self.allow_other,
            "max_pick": self.max_pick,
        }

    @staticmethod
    def from_json(json_data):
        return ChoiceQuestion(
            question=json_data["question"],
            options=json_data["options"],
            is_single_choice=json_data.get("is_single_choice", True),
            allow_other=json_data.get("allow_other", True),
        )


# 同时生成多个选择题
def make_choice_questions_function(processor, response_chat_id):
    async def make_choice_questions(**kwargs):
        response_chat = chat_repository.get_chat(response_chat_id)
        questions = kwargs["questions"]
        response_chat.content_type = ContentType.choice
        choice_question = ChoiceQuestion.from_json(questions[0])
        response_chat.content_json["choice"] = choice_question.to_json()
        response_chat.content_json["following_questions"] = questions[1:]

        chat_repository.update_chat(
            processor.user_id,
            processor.conversation_id,
            response_chat_id,
            content_type=response_chat.content_type,
            content_json=response_chat.content_json,
        )

    return make_choice_questions
