from unittest.mock import Mock, patch, MagicMock

import pytest

from app.conversation_processor import PlanAssistantProcessor
from app.conversation_processor.stateful_processor import StatefulProcessor
from app.db.common_type import SenderType, ChatStatus, ContentType, TaskType


@pytest.fixture
def mock_init_state():
    with patch.object(
        StatefulProcessor, "_StatefulProcessor__init_state", return_value=None
    ) as mock:
        yield mock


@pytest.fixture
def processor(mock_init_state):
    return PlanAssistantProcessor(
        user_id=1,
        conversation_id="",
        content="",
        content_type=ContentType.decision,
        content_json={},
        ref_chat_id="",
        upload_file=None,
        background_tasks=Mock(),
    )


@pytest.fixture
def mock_chat_repository():
    with patch(
        "app.conversation_processor.plan_assistant_processor.chat_repository"
    ) as mock:
        yield mock


def test_abort_user_message(processor, mock_chat_repository):
    processor.latest_chat = Mock(
        content_type=ContentType.disasm_report,
        status=ChatStatus.ongoing,
    )
    assert processor.abort_user_message() == True

    processor.latest_chat = None

    test_cases = [
        ("default", ContentType.normal, False),
        ("derive", ContentType.normal, True),
        ("derive", ContentType.decision, False),
    ]

    for state, content_type, expected in test_cases:
        processor.conversation_state = state
        processor.content_type = content_type
        assert processor.abort_user_message() is expected


def test_get_top_chat(processor, mock_chat_repository):
    mock_chat_repository.get_chat_list_by_conversation_id_and_task_id_not_null.return_value = [
        Mock(
            chat_id=1,
            content_json={
                "task_type": TaskType.derive,
                "video_path": "/path/to/video1",
            },
            create_time=None,
        ),
        Mock(
            chat_id=2,
            content_json={
                "task_type": TaskType.select,
                "video_path": "/path/to/video2",
            },
            create_time=None,
        ),
    ]
    mock_chat_repository.get_chat_list_by_conversation_id_and_ref_chat_id_not_null_and_create_time_after.return_value = (
        []
    )

    top_chat = processor.get_top_chat()
    assert top_chat.chat_id == 2
    assert processor.related_video_path == "/path/to/video2"

    mock_chat_repository.get_chat_list_by_conversation_id_and_ref_chat_id_not_null_and_create_time_after.return_value = [
        Mock(
            chat_id=3,
            ref_chat={
                "task_type": TaskType.derive,
                "video_path": "/path/to/video3",
            },
        ),
        Mock(
            chat_id=4,
            ref_chat={
                "task_type": TaskType.select,
                "video_path": "/path/to/video4",
            },
        ),
    ]

    top_chat = processor.get_top_chat()
    assert top_chat.chat_id == 4
    assert processor.related_video_path == "/path/to/video4"
