import shutil
import uuid
from pathlib import Path

from app.client.s3client import S3Client
from app.config import ASSISTANT_DATA, S3_URL_EXPIRATION_TIME
from app.routers.common import get_user_path


def upload_user_file_to_s3(user_id, file_path, file):
    try:
        s3client = S3Client()

        tmp_path = Path("tmp", get_user_path(user_id), file_path)
        tmp_path.parent.mkdir(parents=True, exist_ok=True)
        with open(tmp_path, "wb") as f:
            shutil.copyfileobj(file, f)

        body = tmp_path.read_bytes()
        file_size = len(body)
        res = s3client.put_obj(file_path, body, get_user_path(user_id))
        if not res:
            raise Exception(f"save file to s3 failed: {file_path}")
        # max 1 week: https://stackoverflow.com/questions/24014306/aws-s3-pre-signed-url-without-expiry-date
        url = s3client.get_obj_download_url(
            file_path,
            get_user_path(user_id),
            expiration=S3_URL_EXPIRATION_TIME,
            preview=True,
        )
        if not url:
            raise Exception(f"get s3 url {file_path} failed")
        return url
    finally:
        file.close()
