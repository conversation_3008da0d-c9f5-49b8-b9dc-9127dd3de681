import datetime

from langfuse.decorators import langfuse_context
from opentelemetry import trace

from app.client.openai_client import MyOpenAiLL<PERSON>lient, JsonConvertArgs
from app.client.prompts import TEMPLATES, FUNCTIONS
from app.config import PRODUCT_KB_NAME
from app.conversation_type import ConversationType
from app.conversation_processor.base_processor import (
    BaseProcessor,
    ToolCall,
    openai_client,
)
from app.conversation_processor.chat_convertor import (
    convert_chats_to_messages,
)
from app.conversation_processor.product_assistant_v2_processor import (
    ProductAssistantV2Processor,
)
from app.conversation_processor.tool_functions import (
    search_knowledge_base_function,
    get_hot_video_by_keyword_function,
    get_marketing_video_function,
)
from app.db.common_type import (
    AssistantType,
    ChatStatus,
    SenderType,
    ContentType,
)
from app.db.repository import chat_repository, chat_conversation_repository
from app.logger import logger
from app.conversation_type import COMPREHENSIVE_INIT_CONTENT, COMPREHENSIVE_INIT_OPTIONS
from app.utils import langfuse_utils


class ComprehensiveProcessor(BaseProcessor):
    @staticmethod
    def get_welcome_message_and_options() -> tuple[str, list[str]]:
        """
        获取欢迎消息和选项，供新建会话时使用

        Returns:
            tuple[str, list[str]]: (欢迎消息, 选项列表)
        """
        return COMPREHENSIVE_INIT_CONTENT, COMPREHENSIVE_INIT_OPTIONS

    def get_system_message_content(self) -> str:
        return langfuse_utils.get_prompt(
            "general_system", TEMPLATES[AssistantType.general]["system"]
        )

    def get_latest_user_content_template(self) -> str | None:
        return TEMPLATES[AssistantType.general]["default"]["chat"]

    def get_functions(self, messages) -> list[ToolCall]:
        return [
            ToolCall(
                "search_knowledge_base",
                search_knowledge_base_function(self, self.response_chat_id),
                FUNCTIONS["search_knowledge_base"],
                priority=1,
            ),
            ToolCall(
                "get_hot_video_by_keyword",
                get_hot_video_by_keyword_function(self, self.response_chat_id),
                FUNCTIONS["get_hot_video_by_keyword"],
                priority=1,
            ),
            ToolCall(
                "get_marketing_video",
                get_marketing_video_function(self, self.response_chat_id),
                FUNCTIONS["get_marketing_video"],
                priority=1,
            ),
        ]

    async def transfer_to_product_assistant(self):
        temp_conversation_id = None  # 生成产品助手回答用的临时conversation
        real_conversation_id = None  # 之后展示给用户的真实conversation
        full_response_content = None  # 之后展示给用户的完整回答

        try:
            temp_conversation_id, _ = (
                chat_conversation_repository.insert_chat_conversation(
                    user_id=self.user_id,
                    knowledge_file_names=[],
                    conversation_type=ConversationType.product_assistant,
                    knowledge_base_name=PRODUCT_KB_NAME,
                    conversation_avatar_name=None,
                    conversation_name="tmp",
                    is_hidden=1,
                )
            )

            real_processor = ProductAssistantV2Processor(
                self.user_id,
                temp_conversation_id,
                self.content,
                self.content_type,
                self.content_json,
                self.ref_chat_id,
                self.upload_file,
                self.background_tasks,
            )
            real_processor.chat = chat_repository.insert_chat(
                self.user_id,
                SenderType.user,
                self.content,
                temp_conversation_id,
                content_type=self.content_type,
                content_json=self.content_json,
                options=self.options,
                ref_chat_id=self.ref_chat_id,
                ref_chat=self.ref_chat,
                send_event=False,
            )
            current_span = trace.get_current_span()
            trace_id = format(current_span.get_span_context().trace_id, "032x")
            await real_processor.post_process(**{"langfuse_observation_id": trace_id})

            tmp_response_chat = chat_repository.get_chat(
                real_processor.response_chat_id
            )

            if not tmp_response_chat:
                return None, None
            full_response_content = tmp_response_chat.content

            real_conversation_id, _ = (
                chat_conversation_repository.insert_chat_conversation(
                    user_id=self.user_id,
                    knowledge_file_names=[],
                    conversation_type=ConversationType.product_assistant,
                    knowledge_base_name=PRODUCT_KB_NAME,
                    conversation_avatar_name=None,
                    conversation_name="default",
                    is_hidden=1,  # 一开始需要保持这个会话隐藏，用户点击跳转后才显示出来
                )
            )
            # 把产品助手的回答修正成特殊样式
            chat_repository.insert_chat(
                sender_id=self.user_id,
                type=SenderType.agent,
                content=f"欢迎使用产品助手\n\n已为您导入搜索问题：{self.content}\n\n已收集到的相关数据如下\n\n{full_response_content}",
                conversation_id=real_conversation_id,
                options=tmp_response_chat.options,
                content_type=tmp_response_chat.content_type,
                content_json=tmp_response_chat.content_json,
                docs=tmp_response_chat.docs,
                ref_chat_id=self.ref_chat_id,
                ref_chat=self.ref_chat,
                send_event=False,
            )

            return real_conversation_id, full_response_content
        finally:
            if temp_conversation_id:
                chat_repository.delete_chat_by_conversation_id(
                    conversation_id=temp_conversation_id
                )
                chat_conversation_repository.delete_conversation(
                    conversation_id=temp_conversation_id,
                    user_id=self.user_id,
                    send_event=False,
                )

    async def do_reply(self):
        try:
            open_ai_client = MyOpenAiLLMClient(
                openai_client.client_chat_async,
                model=openai_client.model_name,
                stream=False,
            )

            history_chats = self.get_history_chats()
            messages = convert_chats_to_messages(history_chats)

            # 先判断用户的意图，有这些可能
            # 1. 正常的问答，走BaseProcessor的逻辑就好
            # 2. 第一次问出产品助手相关的问题，需要转接产品助手
            # 3. 对产品助手相关的问题追问，需要不断精简回答
            real_conversation_type = ConversationType.comprehensive
            prompt = langfuse_utils.get_prompt(
                "comprehensive_assistant_detect_intend",
                "",
                product_assistant=ConversationType.product_assistant,
                plan_assistant=ConversationType.plan_assistant,
                comprehensive=ConversationType.comprehensive,
            )

            messages.insert(
                0,
                {
                    "role": "system",
                    "content": prompt,
                },
            )
            response = await open_ai_client(
                messages=messages, json_convert_args=JsonConvertArgs(expect_json=True)
            )
            messages = messages[1:]

            if response and response["need_transfer"] and response["transfer_target"]:
                real_conversation_type = response["transfer_target"]

            if real_conversation_type == ConversationType.product_assistant:
                follow_up_num = response.get("follow_up_num", 0)
                if follow_up_num == 0:
                    logger.info("流联搜索转接至产品助手")
                    real_conversation_id, full_response_content = (
                        await self.transfer_to_product_assistant()
                    )
                    logger.info(
                        f"流联搜索转接至产品助手完成，conversation_id={real_conversation_id}"
                    )

                    # 修正自己的回答
                    content = "如果您需要完整研报，建议使用产品助手，可提供：\n\n1.生成产品报告（市场报告，商业报告）\n\n2.进行产品商业分析（快速获取产品的商业模式）"
                    if not full_response_content:
                        content = f"搜索失败\n\n\n\n{content}"
                    else:
                        prompt = langfuse_utils.get_prompt(
                            "comprehensive_assistant_summary_transfer_response",
                            "",
                            question=self.content,
                            response=full_response_content,
                            today=datetime.datetime.now().strftime("%Y-%m-%d"),
                        )
                        response = await open_ai_client(
                            [
                                {
                                    "role": "user",
                                    "content": prompt,
                                }
                            ],
                            json_convert_args=JsonConvertArgs(expect_json=True),
                        )
                        if response and response["content"]:
                            content = f"{response['content']}\n\n\n\n{content}"
                        else:
                            content = f"搜索失败\n\n\n\n{content}"

                    chat_repository.update_chat(
                        self.user_id,
                        self.conversation_id,
                        chat_id=self.response_chat_id,
                        content_type=ContentType.normal,
                        content=content,
                        content_json={
                            "real_conversation_id": real_conversation_id,
                            "real_conversation_type": ConversationType.product_assistant,
                        },
                        status=ChatStatus.done,
                    )
                else:
                    logger.info(f"流联搜索中对产品助手相关问题第{follow_up_num}次追问")

                    prompt = langfuse_utils.get_prompt(
                        "comprehensive_assistant_transfer_follow_up",
                        "",
                        follow_up_num=follow_up_num,
                        today=datetime.datetime.now().strftime("%Y-%m-%d"),
                    )
                    messages.append(
                        {
                            "role": "user",
                            "content": prompt,
                        }
                    )
                    response = await open_ai_client(messages)
                    if follow_up_num == 1:
                        content = "友情提示：使用产品助手可获得更专业的分析服务，包括详细的数据图表和策略建议。"
                    elif follow_up_num == 2:
                        content = "注意：流联搜索专注于快速数据查询，若需要深度分析，强烈建议使用产品助手以获得：\n- 完整的数据可视化 \n- 详细的竞品分析\n- 可导出的报告格式"
                    else:
                        content = "由于搜索助手主要用于快速查询，深度分析功能（那个我们那个autogpt）已自动降级。\n需要完整功能？立即切换产品助手"
                    if not response:
                        content = f"搜索失败\n\n\n\n{content}"
                    else:
                        content = f"{response}\n\n\n\n{content}"

                    real_conversation_id = ""
                    for chat in history_chats[::-1]:
                        if "real_conversation_id" in chat.content_json:
                            real_conversation_id = chat.content_json[
                                "real_conversation_id"
                            ]
                            break

                    chat_repository.update_chat(
                        self.user_id,
                        self.conversation_id,
                        chat_id=self.response_chat_id,
                        content_type=ContentType.normal,
                        content=content,
                        content_json=(
                            {
                                "real_conversation_id": real_conversation_id,
                                "real_conversation_type": ConversationType.product_assistant,
                            }
                            if real_conversation_id
                            else None
                        ),
                        status=ChatStatus.done,
                    )
            else:
                await super().do_reply()
        except Exception as e:
            logger.error(f"{self.content}失败，error: {e}", exc_info=True)
            content = f"处理失败，请稍后再试。"
            chat_repository.update_chat(
                self.response_chat_id,
                status=ChatStatus.error,
                content=content,
                content_type=ContentType.normal,
            )
        finally:
            # 推测是由于转接，这里不再update下，langfuse里可能没user id和session id
            langfuse_context.update_current_trace(
                user_id=self.user_id, session_id=self.conversation_id
            )
