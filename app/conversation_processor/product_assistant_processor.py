from fastapi import UploadFile, BackgroundTasks

from app.logger import logger
from .base_processor import BaseProcessor, ToolCall
from .tool_functions import (
    search_knowledge_base_function,
    search_and_generate_product_report_function,
)
from ..client.prompts import TEMPLATES, FUNCTIONS
from ..config import PRODUCT_KB_NAME
from ..db.common_type import ContentType
from ..db.repository import chat_repository
from ..utils import langfuse_utils


class ProductAssistantProcessor(BaseProcessor):
    def __init__(
        self,
        user_id: int,
        conversation_id: str,
        content: str,
        content_type: str,
        content_json: dict,
        ref_chat_id: str,
        upload_file: UploadFile | None,
        background_tasks: BackgroundTasks,
    ):
        knowledge_base_name = PRODUCT_KB_NAME
        super().__init__(
            user_id,
            conversation_id,
            content,
            content_type,
            content_json,
            ref_chat_id,
            upload_file,
            background_tasks,
            knowledge_base_name=knowledge_base_name,
        )

    def get_response_processing_content(self) -> (str, ContentType):
        if "报告" in self.content:
            return "正在生成产品报告，请稍等...", ContentType.product_report
        return super().get_response_processing_content()

    def get_system_message_content(self) -> str:
        return langfuse_utils.get_prompt(
            "product_system", TEMPLATES["product"]["system"]
        )

    def get_functions(self, messages) -> list[ToolCall]:
        return [
            ToolCall(
                "search_knowledge_base",
                search_knowledge_base_function(self, self.response_chat_id),
                FUNCTIONS["search_knowledge_base"],
                priority=1,
            ),
            ToolCall(
                "search_and_generate_product_report",
                search_and_generate_product_report_function(
                    self, self.response_chat_id
                ),
                FUNCTIONS["search_and_generate_product_report"],
                priority=0,
            ),
        ]
