"""
用户活动监控API路由
提供前端事件上报接口
"""
from typing import Optional, Dict, Any
from fastapi import APIRouter, Request, HTTPException, Depends
from pydantic import BaseModel, Field

from app.service.activity.collector import collect_event
from app.middleware.middleware import get_current_user
from app.response import BaseResponse
from app.logger import logger


router = APIRouter(
    prefix="/activity",
    tags=["activity"],
    responses={404: {"description": "Not found"}},
)


class EventRequest(BaseModel):
    """事件上报请求模型"""
    event_name: str = Field(..., description="事件名称")
    event_args: Optional[Dict[str, Any]] = Field(default=None, description="事件参数JSON")


@router.post("/events", response_model=BaseResponse, summary="上报单个用户活动事件")
def collect_event(
    event_request: EventRequest,
    request: Request,
    user_id: int = Depends(get_current_user)
):
    """
    上报单个用户活动事件

    - **event_name**: 事件名称，必须是预定义的事件类型
    - **event_args**: 事件参数，JSON格式

    用户身份通过Authorization头中的JWT token验证获取，事件时间自动设置为当前时间
    """
    try:
        # 收集事件（函数内部会验证事件类型）
        collect_event(
            event_name=event_request.event_name,
            user_id=str(user_id),  # 转换为字符串作为用户标识
            event_args=event_request.event_args,
            request=request,
        )

        return BaseResponse()

    except ValueError as e:
        # 事件类型验证错误
        raise HTTPException(
            status_code=400,
            detail=str(e)
        ) from e
    except Exception as e:
        logger.error("Error collecting event: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        ) from e
