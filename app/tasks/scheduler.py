"""
定时任务调度器
管理用户活动监控系统的定时任务
"""
import asyncio
import schedule
import time
from datetime import datetime
from typing import Dict, Any

from app.tasks.sync_feishu_whitelist import sync_feishu_whitelist
from app.logger import logger


class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.is_running = False
        self.tasks_status = {}
    
    def setup_schedules(self):
        """设置定时任务"""
        # 每3分钟检查一次飞书白名单（增量同步，只有变更时才同步）
        schedule.every(3).minutes.do(self.run_feishu_whitelist_sync)

        logger.info("定时任务调度器已设置完成 - 飞书白名单每3分钟检查一次")
    
    def run_feishu_whitelist_sync(self):
        """执行飞书白名单同步任务"""
        task_name = "feishu_whitelist_sync"
        start_time = datetime.now()
        
        logger.info(f"开始执行定时任务: {task_name}")
        
        try:
            result = sync_feishu_whitelist()
            
            self.tasks_status[task_name] = {
                "last_run": start_time.isoformat(),
                "status": "success" if result.get("success") else "failed",
                "result": result,
                "duration": (datetime.now() - start_time).total_seconds()
            }
            
            if result.get("success"):
                logger.info(f"定时任务 {task_name} 执行成功")
            else:
                logger.error(f"定时任务 {task_name} 执行失败: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"定时任务 {task_name} 执行异常: {e}")
            self.tasks_status[task_name] = {
                "last_run": start_time.isoformat(),
                "status": "error",
                "error": str(e),
                "duration": (datetime.now() - start_time).total_seconds()
            }
    
    def get_tasks_status(self) -> Dict[str, Any]:
        """获取任务状态"""
        next_runs = {}
        for job in schedule.jobs:
            try:
                job_name = getattr(job.job_func, '__name__', str(job.job_func))
                next_runs[job_name] = job.next_run.isoformat() if job.next_run else None
            except Exception:
                next_runs[str(job.job_func)] = job.next_run.isoformat() if job.next_run else None

        return {
            "scheduler_running": self.is_running,
            "tasks": self.tasks_status,
            "next_runs": next_runs
        }
    
    def start(self):
        """启动调度器"""
        self.setup_schedules()
        self.is_running = True
        
        logger.info("定时任务调度器启动")
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭调度器...")
        finally:
            self.stop()
    
    def stop(self):
        """停止调度器"""
        self.is_running = False
        schedule.clear()
        logger.info("定时任务调度器已停止")


# 全局调度器实例
scheduler = TaskScheduler()


def start_scheduler():
    """启动调度器的入口函数"""
    scheduler.start()


def get_scheduler_status() -> Dict[str, Any]:
    """获取调度器状态的入口函数"""
    return scheduler.get_tasks_status()


if __name__ == "__main__":
    # 直接运行调度器
    start_scheduler()
