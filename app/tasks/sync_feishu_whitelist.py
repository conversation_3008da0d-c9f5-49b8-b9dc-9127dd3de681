"""
飞书白名单同步服务
从飞书表格同步用户白名单数据到 ClickHouse
支持增量同步，只有数据变更时才执行同步操作
内置后台线程，每3分钟自动检查并同步
"""

import json
import threading
import time
from datetime import datetime
from typing import List, Dict, Any, Optional

from app.client.feishu_client import FeishuClient, Spreadsheet, Sheet
from app.ch.orm import ChClient
from app.ch.model import UsersWhitelist
from app.logger import logger


class FeishuWhitelistSyncer:
    """飞书白名单同步器（内置后台线程）"""

    def __init__(self, auto_start: bool = False):
        self.feishu_client = FeishuClient()
        self.ch_client = ChClient()

        # 白名单表格配置（从 feishu_client.py 示例中获取）
        self.whitelist_spreadsheet = Spreadsheet(
            spreadsheet_token="XbeSs73eAhhjMvtUTHOcPr37nzb",
            sheets=[
                Sheet(
                    sheet_id="060505",
                    start_col="A",
                    end_col="E",
                    start_row=2,  # 跳过表头
                    end_row=3000,
                )
            ],
        )

        # 后台线程相关
        self._running = False
        self._thread = None
        self._sync_interval = 180  # 3分钟 = 180秒
        self._last_sync_result = None

        if auto_start:
            self.start_background_sync()

    def fetch_feishu_data(self) -> List[List[str]]:
        """从飞书表格获取白名单数据"""
        try:
            logger.info("开始从飞书表格获取白名单数据")
            data = self.feishu_client.get_data_from_sheet(self.whitelist_spreadsheet)
            logger.info("从飞书获取到 %d 条白名单记录", len(data))
            return data
        except Exception as e:
            logger.error("从飞书获取白名单数据失败: %s", str(e))
            raise

    def parse_feishu_row(self, row: List[str]) -> Optional[Dict[str, Any]]:
        """
        解析飞书表格行数据

        假设表格列结构：
        A: 手机号
        B: 客户名称
        C: 添加人
        D: 是否内部人员 (是/否)
        E: 扩展信息 (JSON格式或空)
        """
        if not row or len(row) < 4:
            return None

        phone = row[0].strip() if row[0] else ""
        customer_name = row[1].strip() if len(row) > 1 and row[1] else ""
        added_by = row[2].strip() if len(row) > 2 and row[2] else ""
        is_internal_str = row[3].strip() if len(row) > 3 and row[3] else "否"
        additional_str = row[4].strip() if len(row) > 4 and row[4] else "{}"

        # 验证手机号
        if not phone or len(phone) < 11:
            logger.warning("跳过无效手机号记录: %s", row)
            return None

        # 解析是否内部人员
        is_internal_user = is_internal_str in ("是", "True", "true", "1", "Y", "y")

        # 解析扩展信息
        try:
            if additional_str and additional_str != "{}":
                additional = json.loads(additional_str)
            else:
                additional = {}
        except json.JSONDecodeError:
            logger.warning("扩展信息JSON解析失败，使用空对象: %s", additional_str)
            additional = {}

        return {
            "phone": phone,
            "customer_name": customer_name,
            "added_by": added_by,
            "is_internal_user": is_internal_user,
            "additional": json.dumps(additional, ensure_ascii=False),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }

    def get_clickhouse_data(self) -> List[Dict[str, Any]]:
        """从 ClickHouse 获取现有的白名单数据（使用ORM）"""
        try:
            # 使用ORM查询所有白名单数据
            query_result = UsersWhitelist.objects_in(self.ch_client.dw_db).order_by(
                "phone"
            )

            ch_data = []
            for row in query_result:
                ch_data.append({
                    "phone": row.phone,
                    "customer_name": row.customer_name,
                    "added_by": row.added_by,
                    "is_internal_user": row.is_internal_user,
                    "additional": row.additional,
                })

            logger.info("从 ClickHouse 获取到 %d 条白名单记录", len(ch_data))
            return ch_data

        except Exception as e:
            logger.warning("从 ClickHouse 获取数据失败: %s", str(e))
            return []

    def compare_data(self, feishu_records: List[Dict[str, Any]], ch_records: List[Dict[str, Any]]) -> bool:
        """比较飞书数据和 ClickHouse 数据是否一致"""
        # 将数据转换为可比较的格式（排除时间字段）
        def normalize_record(record):
            return {
                "phone": record.get("phone", ""),
                "customer_name": record.get("customer_name", ""),
                "added_by": record.get("added_by", ""),
                "is_internal_user": record.get("is_internal_user", False),
                "additional": record.get("additional", "{}"),
            }

        # 按手机号排序并标准化
        feishu_normalized = sorted([normalize_record(r) for r in feishu_records], key=lambda x: x["phone"])
        ch_normalized = sorted([normalize_record(r) for r in ch_records], key=lambda x: x["phone"])

        # 比较数据
        is_same = feishu_normalized == ch_normalized

        if is_same:
            logger.info("飞书数据与 ClickHouse 数据一致，无需同步")
        else:
            logger.info("数据不一致 - 飞书: %d 条, ClickHouse: %d 条", len(feishu_normalized), len(ch_normalized))

        return is_same

    def sync_to_clickhouse(self, records: List[Dict[str, Any]]) -> int:
        """将记录同步到 ClickHouse"""
        if not records:
            logger.info("没有需要同步的记录")
            return 0

        try:
            # 创建 UsersWhitelist 对象列表
            whitelist_objects = []
            for record in records:
                whitelist_obj = UsersWhitelist(**record)
                whitelist_objects.append(whitelist_obj)

            # 批量插入到 ClickHouse 数据仓库
            self.ch_client.dw_db.insert(whitelist_objects)
            logger.info("成功同步 %d 条白名单记录到 ClickHouse", len(whitelist_objects))
            return len(whitelist_objects)

        except Exception as e:
            logger.error("同步数据到 ClickHouse 失败: %s", str(e))
            raise

    def sync_with_temp_table(self, records: List[Dict[str, Any]]) -> int:
        """使用临时表进行原子性同步"""
        if not records:
            logger.info("没有需要同步的记录")
            return 0

        temp_table_name = f"users_whitelist_temp_{int(datetime.now().timestamp())}"

        try:
            # 1. 创建临时表（与原表结构相同）
            create_temp_table_sql = f"""
            CREATE TABLE datawarehouse.{temp_table_name} AS datawarehouse.users_whitelist
            """
            self.ch_client.dw_db.raw(create_temp_table_sql)
            logger.info("创建临时表: %s", temp_table_name)

            # 2. 插入数据到临时表
            whitelist_objects = []
            for record in records:
                whitelist_obj = UsersWhitelist(**record)
                whitelist_objects.append(whitelist_obj)

            # 临时修改表名以插入到临时表
            original_table_name = UsersWhitelist.table_name()
            UsersWhitelist._table_name = temp_table_name

            try:
                self.ch_client.dw_db.insert(whitelist_objects)
                logger.info("成功插入 %d 条记录到临时表", len(whitelist_objects))
            finally:
                # 恢复原表名
                UsersWhitelist._table_name = original_table_name

            # 3. 原子性替换：重命名表
            # 先备份原表
            backup_table_name = f"users_whitelist_backup_{int(datetime.now().timestamp())}"

            # 重命名原表为备份表
            self.ch_client.dw_db.raw(f"RENAME TABLE datawarehouse.users_whitelist TO datawarehouse.{backup_table_name}")

            # 重命名临时表为正式表
            self.ch_client.dw_db.raw(f"RENAME TABLE datawarehouse.{temp_table_name} TO datawarehouse.users_whitelist")

            # 删除备份表
            self.ch_client.dw_db.raw(f"DROP TABLE datawarehouse.{backup_table_name}")

            logger.info("原子性同步完成，同步了 %d 条白名单记录", len(whitelist_objects))
            return len(whitelist_objects)

        except Exception as e:
            # 清理临时表
            try:
                self.ch_client.dw_db.raw(f"DROP TABLE IF EXISTS datawarehouse.{temp_table_name}")
            except:
                pass
            logger.error("原子性同步失败: %s", str(e))
            raise

    def sync_full(self) -> Dict[str, Any]:
        """执行全量同步（比较数据后决定是否同步）"""
        start_time = datetime.now()
        logger.info("开始执行飞书白名单同步检查")

        try:
            # 1. 获取飞书数据
            feishu_data = self.fetch_feishu_data()

            # 2. 解析飞书数据
            valid_records = []
            invalid_count = 0

            for row in feishu_data:
                parsed = self.parse_feishu_row(row)
                if parsed:
                    valid_records.append(parsed)
                else:
                    invalid_count += 1

            # 3. 获取 ClickHouse 现有数据
            ch_records = self.get_clickhouse_data()

            # 4. 比较数据是否一致
            if self.compare_data(valid_records, ch_records):
                # 数据一致，无需同步
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                result = {
                    "success": True,
                    "sync_needed": False,
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "total_feishu_records": len(feishu_data),
                    "valid_records": len(valid_records),
                    "invalid_records": invalid_count,
                    "synced_records": 0,
                    "message": "数据一致，无需同步"
                }

                logger.info("飞书白名单数据一致，跳过同步")
                return result

            # 5. 数据不一致，执行全量同步
            logger.info("检测到数据变更，开始全量同步...")

            # 清空现有数据
            self.clear_existing_data()

            # 同步新数据
            synced_count = self.sync_to_clickhouse(valid_records)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            result = {
                "success": True,
                "sync_needed": True,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "total_feishu_records": len(feishu_data),
                "valid_records": len(valid_records),
                "invalid_records": invalid_count,
                "synced_records": synced_count,
                "message": "检测到数据变更，全量同步完成",
            }

            logger.info("飞书白名单全量同步完成: %s", result)
            return result

        except Exception as e:
            logger.error("飞书白名单同步失败: %s", str(e))
            return {
                "success": False,
                "sync_needed": False,
                "error": str(e),
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
            }

    def start_background_sync(self):
        """启动后台同步线程"""
        if self._running:
            logger.warning("后台同步线程已在运行")
            return

        self._running = True
        self._thread = threading.Thread(target=self._background_sync_loop, daemon=True)
        self._thread.start()
        logger.info(f"飞书白名单后台同步线程已启动，每{self._sync_interval}秒检查一次")

    def stop_background_sync(self):
        """停止后台同步线程"""
        if not self._running:
            return

        self._running = False
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
        logger.info("飞书白名单后台同步线程已停止")

    def _background_sync_loop(self):
        """后台同步循环"""
        while self._running:
            try:
                logger.debug("执行后台飞书白名单同步检查")
                result = self.sync_full()
                self._last_sync_result = result

                if result.get("success"):
                    if result.get("sync_needed"):
                        logger.info(
                            "后台同步完成: 同步了 %d 条记录",
                            result.get("synced_records", 0),
                        )
                    else:
                        logger.debug("后台检查完成: 数据无变更")
                else:
                    logger.error("后台同步失败: %s", result.get("error"))

            except Exception as e:
                logger.error("后台同步线程异常: %s", str(e))
                self._last_sync_result = {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

            # 等待下次检查
            for _ in range(self._sync_interval):
                if not self._running:
                    break
                time.sleep(1)

    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "running": self._running,
            "sync_interval_seconds": self._sync_interval,
            "thread_alive": self._thread.is_alive() if self._thread else False,
            "last_sync_result": self._last_sync_result
        }

    def __del__(self):
        """析构函数，确保线程正确停止"""
        self.stop_background_sync()


# 全局同步器实例
_global_syncer = None


def sync_feishu_whitelist() -> Dict[str, Any]:
    """同步飞书白名单的入口函数（全量同步）"""
    syncer = FeishuWhitelistSyncer()
    return syncer.sync_full()


def start_feishu_whitelist_background_sync():
    """启动飞书白名单后台同步服务"""
    global _global_syncer
    if _global_syncer is None:
        _global_syncer = FeishuWhitelistSyncer()
    _global_syncer.start_background_sync()
    return _global_syncer


def stop_feishu_whitelist_background_sync():
    """停止飞书白名单后台同步服务"""
    global _global_syncer
    if _global_syncer:
        _global_syncer.stop_background_sync()


def get_feishu_whitelist_sync_status() -> Dict[str, Any]:
    """获取飞书白名单同步状态"""
    global _global_syncer
    if _global_syncer:
        return _global_syncer.get_sync_status()
    return {"running": False, "message": "同步服务未启动"}


if __name__ == "__main__":
    # 测试同步
    print("=== 手动同步测试 ===")
    result = sync_feishu_whitelist()
    print(json.dumps(result, indent=2, ensure_ascii=False))

    print("\n=== 后台同步测试 ===")
    syncer = start_feishu_whitelist_background_sync()
    print("后台同步已启动，运行10秒后停止...")

    try:
        time.sleep(10)
        status = get_feishu_whitelist_sync_status()
        print("同步状态:", json.dumps(status, indent=2, ensure_ascii=False))
    finally:
        stop_feishu_whitelist_background_sync()
        print("后台同步已停止")
