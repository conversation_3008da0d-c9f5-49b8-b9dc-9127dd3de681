import logging
from logging.config import fileConfig
from typing import Optional

from alembic import context
from alembic.autogenerate.api import AutogenContext
from alembic.autogenerate.compare import comparators
from alembic.operations.ops import AlterColumnOp
from sqlalchemy import Enum as SQLAEnum
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from sqlalchemy.dialects.mysql import ENUM as MySQLEnum
from sqlalchemy.sql.schema import Column

from app.config import GL<PERSON><PERSON>L_CONF
from app.db.models import Base


logger: logging.Logger = logging.getLogger(__name__)


@comparators.dispatch_for("column")
def compare_column(
    autogen_context: AutogenContext,
    modify_ops: AlterColumnOp,
    schema: Optional[str],
    table_name: str,
    column_name: str,
    db_column: Column[MySQLEnum],
    model_column: Column[SQLAEnum],
) -> None:
    """Compare a column's model definition with its database state and modify the AlterColumnOp accordingly."""

    if isinstance(model_column.type, SQLAEnum) and isinstance(
        db_column.type, MySQLEnum
    ):
        model_values: set[str] = set(model_column.type.enums)
        db_values: set[str] = set(db_column.type.enums)

        logger.debug(
            f"Comparing {table_name}.{column_name} db_values={db_values}, model_values={model_values}"
        )

        if model_values ^ db_values:
            if new_values := model_values - db_values:
                logger.info(
                    f"New values detected for Enum {table_name}.{column_name}: {', '.join(new_values)}"
                )

            if removed_values := db_values - model_values:
                logger.info(
                    f"Removed values detected for Enum {table_name}.{column_name}: {', '.join(removed_values)}"
                )

            modify_ops.modify_type = model_column.type
            modify_ops.existing_type = SQLAEnum(
                *db_values, name=db_column.type.name or model_column.type.name
            )
            modify_ops.existing_nullable = db_column.nullable


host = GLOBAL_CONF.MYSQL_HOST
port = GLOBAL_CONF.MYSQL_PORT
user = GLOBAL_CONF.MYSQL_USER
password = GLOBAL_CONF.MYSQL_PASSWORD
db_name = GLOBAL_CONF.MYSQL_DB_NAME
SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{user}:{password}@{host}:{port}/{db_name}"

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

config.set_main_option("sqlalchemy.url", SQLALCHEMY_DATABASE_URL)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
