"""chat task foreign key

Revision ID: fc5c5d21025e
Revises: e64a41165410
Create Date: 2024-09-29 14:32:40.333713

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "fc5c5d21025e"
down_revision: Union[str, None] = "e64a41165410"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(None, "chat", "disassemble_tasks", ["task_id"], ["task_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "chat", type_="foreignkey")
    # ### end Alembic commands ###
