"""description

Revision ID: 20efcb6972c1
Revises: 1715fd0f6e00
Create Date: 2024-12-31 12:18:59.936720

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "20efcb6972c1"
down_revision: Union[str, None] = "1715fd0f6e00"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "knowledge_file",
        sa.Column(
            "liangzhi_doc_url",
            sa.String(length=256),
            nullable=True,
            comment="良智文档链接",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("knowledge_file", "liangzhi_doc_url")
    # ### end Alembic commands ###
