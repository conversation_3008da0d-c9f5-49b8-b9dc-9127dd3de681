"""修改KnowledgeFile时区

Revision ID: 7f908e1184ed
Revises: 600fada82cbd
Create Date: 2025-02-19 20:20:31.389783

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "7f908e1184ed"
down_revision: Union[str, None] = "600fada82cbd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "knowledge_file",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "knowledge_file",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    # ### end Alembic commands ###
