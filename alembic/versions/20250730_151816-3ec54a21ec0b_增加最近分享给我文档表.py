"""增加最近分享给我文档表

Revision ID: 3ec54a21ec0b
Revises: 3a711f70e8db
Create Date: 2025-07-30 15:18:16.581072

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "3ec54a21ec0b"
down_revision: Union[str, None] = "3a711f70e8db"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "recently_opened_document",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("workspace_id", sa.String(length=255), nullable=False),
        sa.Column("doc_id", sa.String(length=255), nullable=False),
        sa.Column("last_accessed_at", mysql.DATETIME(fsp=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_user_id_last_accessed_at",
        "recently_opened_document",
        ["user_id", "last_accessed_at"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_user_id_last_accessed_at", table_name="recently_opened_document")
    op.drop_table("recently_opened_document")
    # ### end Alembic commands ###
