"""extend phone_number size

Revision ID: ba096984757d
Revises: 43478fca2bb4
Create Date: 2025-05-18 17:31:12.774840

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "ba096984757d"
down_revision: Union[str, None] = "43478fca2bb4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "phone_number",
        existing_type=mysql.VARCHAR(length=20),
        type_=sa.String(length=64),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "phone_number",
        existing_type=sa.String(length=64),
        type_=mysql.VARCHAR(length=20),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
