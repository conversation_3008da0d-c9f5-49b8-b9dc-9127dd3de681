"""add chat_type to chat

Revision ID: 5a01aa9200d8
Revises: 20efcb6972c1
Create Date: 2025-01-09 11:36:26.063719

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5a01aa9200d8"
down_revision: Union[str, None] = "0ceef4dca87f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat",
        sa.Column(
            "chat_type",
            sa.Enum("plain", "document", "video", "image", "link", name="chattype"),
            server_default="plain",
            nullable=False,
            comment="消息类型",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat", "chat_type")
    # ### end Alembic commands ###
