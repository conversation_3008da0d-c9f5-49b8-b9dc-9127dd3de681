"""increase length for document_attachment.oss_url

Revision ID: 600fada82cbd
Revises: 9b738019d898
Create Date: 2025-02-14 21:03:08.338609

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "600fada82cbd"
down_revision: Union[str, None] = "9b738019d898"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "document_attachment",
        "oss_url",
        existing_type=mysql.VARCHAR(length=256),
        type_=sa.String(length=1024),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "document_attachment",
        "oss_url",
        existing_type=sa.String(length=1024),
        type_=mysql.VARCHAR(length=256),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
