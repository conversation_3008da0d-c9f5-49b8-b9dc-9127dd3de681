"""remove choice in chat and add more content type

Revision ID: ae69cc0cd188
Revises: b82351595224
Create Date: 2024-10-18 17:10:56.253396

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "ae69cc0cd188"
down_revision: Union[str, None] = "b82351595224"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat", "decision")
    op.drop_column("chat", "choice")
    op.alter_column(
        "chat",
        "content_type",
        existing_type=mysql.ENUM(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
        ),
        type_=sa.Enum(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            name="contenttype",
        ),
        type_=mysql.ENUM(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    op.add_column(
        "chat",
        sa.Column(
            "choice",
            mysql.JSON(),
            nullable=True,
            comment="消息选择项，包括items和max_pick等子信息",
        ),
    )
    op.add_column(
        "chat",
        sa.Column(
            "decision", mysql.JSON(), nullable=True, comment="选择结果，可能包含多条"
        ),
    )
    # ### end Alembic commands ###
