"""knowledgeFile表增加json_info

Revision ID: 3816ffd4e95d
Revises: 6788e7ccba89
Create Date: 2025-02-08 08:50:20.892100

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3816ffd4e95d"
down_revision: Union[str, None] = "6788e7ccba89"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "knowledge_file",
        sa.Column(
            "json_info", sa.JSON(), nullable=True, comment="保存其他任何业务数据"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("knowledge_file", "json_info")
    # ### end Alembic commands ###
