"""add chanmama account

Revision ID: 8befb37367d4
Revises: d338f1f6862a
Create Date: 2024-11-28 10:12:21.772508

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "8befb37367d4"
down_revision: Union[str, None] = "d338f1f6862a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

old_platforms = mysql.ENUM(
    "douhot",
    "luopan",
    "douyin",
)
new_platforms = sa.Enum(
    "douhot",
    "luopan",
    "douyin",
    "chanmama",
    name="platformtype",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.add_column(
        "cookie_pool",
        sa.Column("weight", sa.Integer(), server_default="1", nullable=False),
    )
    op.drop_index("phone_number", "cookie_pool")
    op.create_index(
        "idx_platform_phone_number",
        "cookie_pool",
        ["platform", "phone_number"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_platform_phone_number", table_name="cookie_pool")
    op.create_index("phone_number", "cookie_pool", ["phone_number"], unique=True)
    op.drop_column("cookie_pool", "weight")
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
