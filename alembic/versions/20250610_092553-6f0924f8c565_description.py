"""description

Revision ID: 6f0924f8c565
Revises: a8f18d7af934
Create Date: 2025-06-10 09:25:53.118816

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6f0924f8c565"
down_revision: Union[str, None] = "a8f18d7af934"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_doc",
        sa.Column(
            "icon_url", sa.String(length=1024), nullable=True, comment="文档图标URL"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("document_doc", "icon_url")
    # ### end Alembic commands ###
