"""doc snapshot version

Revision ID: 267766421155
Revises: 9ccd3f61eb95
Create Date: 2025-03-20 11:44:53.102477

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "267766421155"
down_revision: Union[str, None] = "9ccd3f61eb95"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document_doc_snapshot_history",
        sa.Column("version", sa.String(length=32), nullable=True),
    )
    op.add_column(
        "document_doc_snapshot_history",
        sa.Column("version_time", mysql.DATETIME(fsp=6), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("document_doc_snapshot_history", "version_time")
    op.drop_column("document_doc_snapshot_history", "version")
    # ### end Alembic commands ###
