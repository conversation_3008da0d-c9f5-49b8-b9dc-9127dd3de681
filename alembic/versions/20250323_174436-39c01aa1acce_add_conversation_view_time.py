"""add conversation view time

Revision ID: 39c01aa1acce
Revises: 267766421155
Create Date: 2025-03-23 17:44:36.437355

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "39c01aa1acce"
down_revision: Union[str, None] = "267766421155"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat_conversation",
        sa.Column(
            "view_time",
            mysql.DATETIME(fsp=6),
            nullable=True,
            comment="会话查看时间",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat_conversation", "view_time")
    # ### end Alembic commands ###
