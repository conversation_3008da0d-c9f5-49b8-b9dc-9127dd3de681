"""add content_assistant conversation type

Revision ID: 6788e7ccba89
Revises: 97fd11c3d347
Create Date: 2025-01-23 20:23:30.844789

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "6788e7ccba89"
down_revision: Union[str, None] = "97fd11c3d347"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
        ),
        type_=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
            "content_assistant",
            name="conversationtype",
        ),
        nullable=False,
        comment="会话类型",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
            "content_assistant",
            name="conversationtype",
        ),
        type_=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
        ),
        nullable=True,
        comment=None,
        existing_comment="会话类型",
    )
    # ### end Alembic commands ###
