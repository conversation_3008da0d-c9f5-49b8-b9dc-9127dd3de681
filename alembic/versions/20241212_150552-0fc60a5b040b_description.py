"""description

Revision ID: 0fc60a5b040b
Revises: c498f7bfeb35
Create Date: 2024-12-12 15:05:52.505456

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "0fc60a5b040b"
down_revision: Union[str, None] = "c498f7bfeb35"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "knowledge_file",
        sa.Column("file_hash", mysql.VARCHAR(length=64), nullable=True),
    )

    op.add_column(
        "knowledge_file",
        sa.Column("report_seo", mysql.JSON(), nullable=True),
    )

    op.add_column(
        "knowledge_file",
        sa.Column("oss_url", mysql.VARCHAR(length=256), nullable=True),
    )
    op.add_column(
        "knowledge_file",
        sa.Column(
            "knowledge_base_type",
            mysql.ENUM("user", "report", "research"),
            nullable=True,
        ),
    )

    op.create_index("idx_file_hash", "knowledge_file", ["file_hash"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_file_hash", table_name="knowledge_file")
    op.drop_column("knowledge_file", "file_hash")
    op.drop_column("knowledge_file", "report_seo")
    op.drop_column("knowledge_file", "oss_url")
    op.drop_column("knowledge_file", "knowledge_base_type")
    # ### end Alembic commands ###
