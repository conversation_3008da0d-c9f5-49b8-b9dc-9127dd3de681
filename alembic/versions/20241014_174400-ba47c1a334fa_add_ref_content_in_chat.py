"""add ref_content in chat

Revision ID: ba47c1a334fa
Revises: be4096171cea
Create Date: 2024-10-14 17:44:00.944338

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "ba47c1a334fa"
down_revision: Union[str, None] = "be4096171cea"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat",
        sa.Column("ref_chat", sa.JSON(), nullable=True, comment="引用的消息信息"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat", "ref_chat")
    # ### end Alembic commands ###
