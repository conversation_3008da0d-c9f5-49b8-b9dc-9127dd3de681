"""add trace_id for chat

Revision ID: fe9ef35f75b8
Revises: 7f908e1184ed
Create Date: 2025-02-27 15:32:16.349649

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "fe9ef35f75b8"
down_revision: Union[str, None] = "7f908e1184ed"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat",
        sa.Column(
            "trace_id", sa.String(length=64), nullable=True, comment="langfuse trace id"
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat", "trace_id")
    # ### end Alembic commands ###
