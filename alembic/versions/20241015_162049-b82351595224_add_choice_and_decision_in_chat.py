"""add choice and decision in chat

Revision ID: b82351595224
Revises: ba47c1a334fa
Create Date: 2024-10-15 16:20:49.487752

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "b82351595224"
down_revision: Union[str, None] = "ba47c1a334fa"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat",
        sa.Column(
            "choice",
            sa.JSON(),
            nullable=True,
            comment="消息选择项，包括items和max_pick等子信息",
        ),
    )
    op.add_column(
        "chat",
        sa.Column(
            "decision", sa.JSON(), nullable=True, comment="选择结果，可能包含多条"
        ),
    )
    op.alter_column(
        "chat",
        "content_type",
        existing_type=mysql.ENUM(
            "init",
            "normal",
            "video_info",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
        ),
        type_=sa.Enum(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "init",
            "normal",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            name="contenttype",
        ),
        type_=mysql.ENUM(
            "init",
            "normal",
            "video_info",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    op.drop_column("chat", "decision")
    op.drop_column("chat", "choice")
    # ### end Alembic commands ###
