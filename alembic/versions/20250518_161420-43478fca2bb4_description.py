"""description

Revision ID: 43478fca2bb4
Revises: ecb239fe6849
Create Date: 2025-05-18 16:14:20.130059

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "43478fca2bb4"
down_revision: Union[str, None] = "ecb239fe6849"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat_conversation",
        sa.Column(
            "related_doc_ids", sa.JSON(), nullable=True, comment="会话关联的文档ID列表"
        ),
    )
    op.add_column(
        "document_doc",
        sa.Column(
            "related_conversation_id",
            sa.String(length=64),
            nullable=True,
            comment="关联的会话ID，如果有，则表示为聊天笔记",
        ),
    )
    op.add_column(
        "document_doc",
        sa.<PERSON>umn(
            "hidden_in_doc_list",
            sa.<PERSON>(),
            server_default="0",
            nullable=False,
            comment="是否在文档列表中隐藏，未转存的会话关联文档设置为1",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("document_doc", "hidden_in_doc_list")
    op.drop_column("document_doc", "related_conversation_id")
    op.drop_column("chat_conversation", "related_doc_ids")
    # ### end Alembic commands ###
