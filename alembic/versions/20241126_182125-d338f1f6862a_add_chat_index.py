"""add chat index

Revision ID: d338f1f6862a
Revises: 1d52983edf71
Create Date: 2024-11-26 18:21:25.859288

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "d338f1f6862a"
down_revision: Union[str, None] = "1d52983edf71"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 对 create_time 创建索引
    op.create_index("idx_chat_create_time", "chat", ["create_time"])

    # 对 sender_id, create_time 创建联合索引
    op.create_index("idx_chat_sender_create_time", "chat", ["sender_id", "create_time"])

    # 对 sender_id, conversation_id, create_time 创建联合索引
    op.create_index(
        "idx_chat_sender_conversation_create_time",
        "chat",
        ["sender_id", "conversation_id", "create_time"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 删除索引
    op.drop_index("idx_chat_sender_conversation_create_time", "chat")
    op.drop_index("idx_chat_sender_create_time", "chat")
    op.drop_index("idx_chat_create_time", "chat")
    # ### end Alembic commands ###
