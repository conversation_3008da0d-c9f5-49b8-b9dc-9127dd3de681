"""chat_status abort

Revision ID: 48b705078a97
Revises: ca37842d4abe
Create Date: 2024-12-22 14:49:08.347641

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "48b705078a97"
down_revision: Union[str, None] = "ca37842d4abe"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


chat_status_old = mysql.ENUM(
    "done",
    "ongoing",
    "error",
)

chat_status_new = sa.Enum(
    "done",
    "ongoing",
    "error",
    "abort",
    name="chatstatus",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "status",
        existing_type=chat_status_old,
        type_=chat_status_new,
        existing_comment="消息状态",
        existing_nullable=False,
        existing_server_default=sa.text("'done'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "status",
        existing_type=chat_status_new,
        type_=chat_status_old,
        existing_comment="消息状态",
        existing_nullable=False,
        existing_server_default=sa.text("'done'"),
    )
    # ### end Alembic commands ###
