"""init

Revision ID: e64a41165410
Revises:
Create Date: 2024-09-27 10:10:59.435270

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "e64a41165410"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "cookie_id",
        existing_type=mysql.BIGINT(),
        comment=None,
        existing_comment="cookie id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "file_doc",
        "id",
        existing_type=mysql.INTEGER(),
        comment="ID",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "file_doc",
        "kb_name",
        existing_type=mysql.VARCHAR(length=50),
        comment="知识库名称",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "file_name",
        existing_type=mysql.VARCHAR(length=255),
        comment="文件名称",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "doc_id",
        existing_type=mysql.VARCHAR(length=50),
        comment="向量库文档ID",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "meta_data",
        existing_type=mysql.TEXT(),
        type_=sa.JSON(),
        existing_nullable=True,
    )
    op.alter_column(
        "ip_pool",
        "ip_id",
        existing_type=mysql.BIGINT(),
        comment=None,
        existing_comment="cookie id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "ip_pool",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "ip_pool",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "knowledge_file",
        "id",
        existing_type=mysql.INTEGER(),
        comment="知识文件ID",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "knowledge_file",
        "file_name",
        existing_type=mysql.VARCHAR(length=255),
        comment="文件名",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "file_ext",
        existing_type=mysql.VARCHAR(length=10),
        comment="文件扩展名",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "kb_name",
        existing_type=mysql.VARCHAR(length=50),
        comment="所属知识库名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "document_loader_name",
        existing_type=mysql.VARCHAR(length=50),
        comment="文档加载器名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "text_splitter_name",
        existing_type=mysql.VARCHAR(length=50),
        comment="文本分割器名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "file_version",
        existing_type=mysql.INTEGER(),
        comment="文件版本",
        existing_nullable=False,
        existing_server_default=sa.text("'1'"),
    )
    op.alter_column(
        "knowledge_file",
        "file_mtime",
        existing_type=mysql.FLOAT(),
        comment="文件修改时间",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "file_size",
        existing_type=mysql.INTEGER(),
        comment="文件大小",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "custom_docs",
        existing_type=mysql.TINYINT(display_width=1),
        comment="是否自定义docs",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "docs_count",
        existing_type=mysql.INTEGER(),
        comment="切分文档数量",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "tags",
        existing_type=mysql.VARCHAR(length=256),
        comment="文件标签",
        existing_nullable=True,
        existing_server_default=sa.text("''"),
    )
    op.alter_column(
        "knowledge_file",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "knowledge_file",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "model_adjust_data",
        "path",
        existing_type=mysql.VARCHAR(length=255),
        comment=None,
        existing_comment="视频解析图路径",
        existing_nullable=False,
    )
    op.alter_column(
        "model_adjust_data",
        "content",
        existing_type=mysql.TEXT(),
        comment=None,
        existing_comment="微调内容（问答json）",
        existing_nullable=False,
    )
    op.alter_column(
        "model_adjust_data",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
    )
    op.alter_column(
        "read_assistant_tasks",
        "task_id",
        existing_type=mysql.BIGINT(),
        comment=None,
        existing_comment="任务id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "task_type",
        existing_type=mysql.ENUM("read_report"),
        comment=None,
        existing_comment="任务类型",
        existing_nullable=False,
        existing_server_default=sa.text("'read_report'"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "cover_file_path",
        existing_type=mysql.VARCHAR(length=256),
        comment=None,
        existing_comment="报告封面图片路径",
        existing_nullable=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "report",
        existing_type=mysql.JSON(),
        comment=None,
        existing_comment="读书报告",
        existing_nullable=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "status",
        existing_type=mysql.ENUM("done", "ongoing", "error"),
        comment=None,
        existing_comment="消息状态",
        existing_nullable=False,
        existing_server_default=sa.text("'done'"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "user_id",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="用户id",
        existing_nullable=False,
    )
    op.alter_column(
        "read_assistant_tasks",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "task_assignment",
        "assign_id",
        existing_type=mysql.BIGINT(),
        comment=None,
        existing_comment="assignment id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=mysql.ENUM("douhot", "luopan", "douyin"),
        comment=None,
        existing_comment="平台",
        existing_nullable=False,
        existing_server_default=sa.text("'douhot'"),
    )
    op.alter_column(
        "task_assignment",
        "replicas",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="任务副本数",
        existing_nullable=False,
        existing_server_default=sa.text("'1'"),
    )
    op.alter_column(
        "task_assignment",
        "tasks",
        existing_type=mysql.VARCHAR(length=4096),
        comment=None,
        existing_comment="任务列表，逗号分隔的列表",
        existing_nullable=False,
        existing_server_default=sa.text("''"),
    )
    op.alter_column(
        "task_assignment",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "task_assignment",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "user",
        "user_id",
        existing_type=mysql.INTEGER(),
        comment="用户ID(来自思核云)",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "user",
        "nickname",
        existing_type=mysql.VARCHAR(length=64),
        comment="昵称",
        existing_nullable=False,
    )
    op.alter_column(
        "user",
        "phone",
        existing_type=mysql.VARCHAR(length=64),
        comment="手机号",
        existing_nullable=False,
    )
    op.alter_column(
        "user",
        "user_type",
        existing_type=mysql.ENUM("normal", "admin"),
        comment="用户类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    op.alter_column(
        "user",
        "name",
        existing_type=mysql.VARCHAR(length=64),
        comment="姓名",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "birthday",
        existing_type=mysql.DATETIME(),
        comment="生日",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "vip_level",
        existing_type=mysql.INTEGER(),
        comment="会员等级",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "user",
        "address",
        existing_type=mysql.VARCHAR(length=255),
        comment="地点",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "code",
        existing_type=mysql.TEXT(),
        comment="抖音授权码（临时生效）",
        existing_nullable=True,
    )
    op.create_unique_constraint(None, "user", ["phone"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "user", type_="unique")
    op.alter_column(
        "user",
        "code",
        existing_type=mysql.TEXT(),
        comment=None,
        existing_comment="抖音授权码（临时生效）",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "address",
        existing_type=mysql.VARCHAR(length=255),
        comment=None,
        existing_comment="地点",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "vip_level",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="会员等级",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "user",
        "birthday",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="生日",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "name",
        existing_type=mysql.VARCHAR(length=64),
        comment=None,
        existing_comment="姓名",
        existing_nullable=True,
    )
    op.alter_column(
        "user",
        "user_type",
        existing_type=mysql.ENUM("normal", "admin"),
        comment=None,
        existing_comment="用户类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    op.alter_column(
        "user",
        "phone",
        existing_type=mysql.VARCHAR(length=64),
        comment=None,
        existing_comment="手机号",
        existing_nullable=False,
    )
    op.alter_column(
        "user",
        "nickname",
        existing_type=mysql.VARCHAR(length=64),
        comment=None,
        existing_comment="昵称",
        existing_nullable=False,
    )
    op.alter_column(
        "user",
        "user_id",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="用户ID(来自思核云)",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "task_assignment",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "task_assignment",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "task_assignment",
        "tasks",
        existing_type=mysql.VARCHAR(length=4096),
        comment="任务列表，逗号分隔的列表",
        existing_nullable=False,
        existing_server_default=sa.text("''"),
    )
    op.alter_column(
        "task_assignment",
        "replicas",
        existing_type=mysql.INTEGER(),
        comment="任务副本数",
        existing_nullable=False,
        existing_server_default=sa.text("'1'"),
    )
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=mysql.ENUM("douhot", "luopan", "douyin"),
        comment="平台",
        existing_nullable=False,
        existing_server_default=sa.text("'douhot'"),
    )
    op.alter_column(
        "task_assignment",
        "assign_id",
        existing_type=mysql.BIGINT(),
        comment="assignment id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "read_assistant_tasks",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "user_id",
        existing_type=mysql.INTEGER(),
        comment="用户id",
        existing_nullable=False,
    )
    op.alter_column(
        "read_assistant_tasks",
        "status",
        existing_type=mysql.ENUM("done", "ongoing", "error"),
        comment="消息状态",
        existing_nullable=False,
        existing_server_default=sa.text("'done'"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "report",
        existing_type=mysql.JSON(),
        comment="读书报告",
        existing_nullable=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "cover_file_path",
        existing_type=mysql.VARCHAR(length=256),
        comment="报告封面图片路径",
        existing_nullable=True,
    )
    op.alter_column(
        "read_assistant_tasks",
        "task_type",
        existing_type=mysql.ENUM("read_report"),
        comment="任务类型",
        existing_nullable=False,
        existing_server_default=sa.text("'read_report'"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "task_id",
        existing_type=mysql.BIGINT(),
        comment="任务id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "model_adjust_data",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
    )
    op.alter_column(
        "model_adjust_data",
        "content",
        existing_type=mysql.TEXT(),
        comment="微调内容（问答json）",
        existing_nullable=False,
    )
    op.alter_column(
        "model_adjust_data",
        "path",
        existing_type=mysql.VARCHAR(length=255),
        comment="视频解析图路径",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "knowledge_file",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment=None,
        existing_comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "knowledge_file",
        "tags",
        existing_type=mysql.VARCHAR(length=256),
        comment=None,
        existing_comment="文件标签",
        existing_nullable=True,
        existing_server_default=sa.text("''"),
    )
    op.alter_column(
        "knowledge_file",
        "docs_count",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="切分文档数量",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "custom_docs",
        existing_type=mysql.TINYINT(display_width=1),
        comment=None,
        existing_comment="是否自定义docs",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "file_size",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="文件大小",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "file_mtime",
        existing_type=mysql.FLOAT(),
        comment=None,
        existing_comment="文件修改时间",
        existing_nullable=False,
        existing_server_default=sa.text("'0'"),
    )
    op.alter_column(
        "knowledge_file",
        "file_version",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="文件版本",
        existing_nullable=False,
        existing_server_default=sa.text("'1'"),
    )
    op.alter_column(
        "knowledge_file",
        "text_splitter_name",
        existing_type=mysql.VARCHAR(length=50),
        comment=None,
        existing_comment="文本分割器名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "document_loader_name",
        existing_type=mysql.VARCHAR(length=50),
        comment=None,
        existing_comment="文档加载器名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "kb_name",
        existing_type=mysql.VARCHAR(length=50),
        comment=None,
        existing_comment="所属知识库名称",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "file_ext",
        existing_type=mysql.VARCHAR(length=10),
        comment=None,
        existing_comment="文件扩展名",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "file_name",
        existing_type=mysql.VARCHAR(length=255),
        comment=None,
        existing_comment="文件名",
        existing_nullable=False,
    )
    op.alter_column(
        "knowledge_file",
        "id",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="知识文件ID",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "ip_pool",
        "update_time",
        existing_type=mysql.DATETIME(),
        comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text(
            "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ),
    )
    op.alter_column(
        "ip_pool",
        "create_time",
        existing_type=mysql.DATETIME(),
        comment="创建时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "ip_pool",
        "ip_id",
        existing_type=mysql.BIGINT(),
        comment="cookie id",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "file_doc",
        "meta_data",
        existing_type=sa.JSON(),
        type_=mysql.TEXT(),
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "doc_id",
        existing_type=mysql.VARCHAR(length=50),
        comment=None,
        existing_comment="向量库文档ID",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "file_name",
        existing_type=mysql.VARCHAR(length=255),
        comment=None,
        existing_comment="文件名称",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "kb_name",
        existing_type=mysql.VARCHAR(length=50),
        comment=None,
        existing_comment="知识库名称",
        existing_nullable=True,
    )
    op.alter_column(
        "file_doc",
        "id",
        existing_type=mysql.INTEGER(),
        comment=None,
        existing_comment="ID",
        existing_nullable=False,
        autoincrement=True,
    )
    op.alter_column(
        "cookie_pool",
        "cookie_id",
        existing_type=mysql.BIGINT(),
        comment="cookie id",
        existing_nullable=False,
        autoincrement=True,
    )
    # ### end Alembic commands ###
