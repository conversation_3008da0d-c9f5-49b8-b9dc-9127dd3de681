"""add nox platform

Revision ID: ecb239fe6849
Revises: 4365850196cf
Create Date: 2025-04-23 14:27:18.026969

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "ecb239fe6849"
down_revision: Union[str, None] = "4365850196cf"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


old_platforms = mysql.ENUM(
    "douhot",
    "luopan",
    "luopan_qita",
    "douyin",
    "chanmama",
    "xinhong",
    "xiaohongshu",
    "trendinsight",
    "temp1",
    "temp2",
    "temp3",
)
new_platforms = sa.Enum(
    "douhot",
    "luopan",
    "luopan_qita",
    "douyin",
    "chanmama",
    "xinhong",
    "xiaohongshu",
    "trendinsight",
    "nox",
    "temp1",
    "temp2",
    "temp3",
    name="platformtype",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
