"""扩展knowledge file 表oss url 长度

Revision ID: 509bbf6bfbb1
Revises: 6788e7ccba89
Create Date: 2025-02-05 08:38:32.886618

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "509bbf6bfbb1"
down_revision: Union[str, None] = "6788e7ccba89"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "oss_url",
        existing_type=mysql.VARCHAR(length=256),
        type_=sa.String(length=1024),
        existing_comment="OSS文件链接",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "oss_url",
        existing_type=sa.String(length=1024),
        type_=mysql.VARCHAR(length=256),
        existing_comment="OSS文件链接",
        existing_nullable=True,
    )
    # ### end Alembic commands ###
