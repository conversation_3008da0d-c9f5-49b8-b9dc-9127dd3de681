"""add conversation_id foreign key

Revision ID: 0ceef4dca87f
Revises: 20efcb6972c1
Create Date: 2025-01-10 15:43:13.409532

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "0ceef4dca87f"
down_revision: Union[str, None] = "20efcb6972c1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        None, "chat", "chat_conversation", ["conversation_id"], ["conversation_id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "chat", type_="foreignkey")
    # ### end Alembic commands ###
