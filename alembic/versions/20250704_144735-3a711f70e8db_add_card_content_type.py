"""add_card_content_type

Revision ID: 3a711f70e8db
Revises: 73381f994d2c
Create Date: 2025-07-04 14:47:35.733938

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3a711f70e8db"
down_revision: Union[str, None] = "73381f994d2c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "disasm_report",
            "knowledge_base",
            "clear_context",
            "decision",
            "disasm_request",
            "derive_confirm",
            "normal",
            "product_report",
            "choice",
            "derive_form",
            "command",
            "init",
            name="contenttype",
        ),
        type_=sa.Enum(
            "init",
            "normal",
            "command",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            "clear_context",
            "card",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=sa.Enum(
            "init",
            "normal",
            "command",
            "decision",
            "disasm_request",
            "disasm_report",
            "knowledge_base",
            "product_report",
            "choice",
            "derive_form",
            "derive_confirm",
            "clear_context",
            "card",
            name="contenttype",
        ),
        type_=sa.Enum(
            "disasm_report",
            "knowledge_base",
            "clear_context",
            "decision",
            "disasm_request",
            "derive_confirm",
            "normal",
            "product_report",
            "choice",
            "derive_form",
            "command",
            "init",
            name="contenttype",
        ),
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###
