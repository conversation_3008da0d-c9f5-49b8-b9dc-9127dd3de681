"""merge 509bbf6bfbb1 3816ffd4e95d

Revision ID: b9d12ed69c48
Revises: 509bbf6bfbb1, 3816ffd4e95d
Create Date: 2025-02-11 14:14:46.737167

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b9d12ed69c48"
down_revision: Union[str, None] = ("509bbf6bfbb1", "3816ffd4e95d")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
