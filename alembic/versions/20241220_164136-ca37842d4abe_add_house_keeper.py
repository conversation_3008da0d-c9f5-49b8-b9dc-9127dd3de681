"""add_house_keeper

Revision ID: ca37842d4abe
Revises: 3e35652fa095
Create Date: 2024-12-20 16:41:36.110496

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "ca37842d4abe"
down_revision: Union[str, None] = "3e35652fa095"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "internal",
        ),
        type_=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            name="conversationtype",
        ),
        nullable=False,
        comment="会话类型",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            name="conversationtype",
        ),
        type_=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "internal",
        ),
        nullable=True,
        comment=None,
        existing_comment="会话类型",
    )
    # ### end Alembic commands ###
