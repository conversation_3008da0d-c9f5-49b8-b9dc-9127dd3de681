"""description

Revision ID: 9b9551bcd20b
Revises: fc5c5d21025e
Create Date: 2024-10-11 10:08:21.067907

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "9b9551bcd20b"
down_revision: Union[str, None] = "fc5c5d21025e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("reference_id", table_name="affine_block_reference")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        "reference_id", "affine_block_reference", ["reference_id"], unique=True
    )
    # ### end Alembic commands ###
