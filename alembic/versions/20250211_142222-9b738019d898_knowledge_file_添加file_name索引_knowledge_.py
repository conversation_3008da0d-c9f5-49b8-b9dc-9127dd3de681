"""knowledge_file 添加file_name索引，knowledge_base_type枚举添加text

Revision ID: 9b738019d898
Revises: b9d12ed69c48
Create Date: 2025-02-11 14:22:22.905332

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "9b738019d898"
down_revision: Union[str, None] = "b9d12ed69c48"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM("user", "report", "research", "video", "text"),
        comment="知识库类型",
        existing_nullable=True,
    )
    op.create_index("idx_file_name", "knowledge_file", ["file_name"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_file_name", table_name="knowledge_file")
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM("user", "report", "research", "video", "text"),
        comment=None,
        existing_comment="知识库类型",
        existing_nullable=True,
    )
    # ### end Alembic commands ###
