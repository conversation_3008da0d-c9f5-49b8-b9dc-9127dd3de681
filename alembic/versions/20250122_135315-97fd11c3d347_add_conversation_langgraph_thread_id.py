"""add conversation langgraph thread id

Revision ID: 97fd11c3d347
Revises: 5a01aa9200d8
Create Date: 2025-01-22 13:53:15.527369

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "97fd11c3d347"
down_revision: Union[str, None] = "5a01aa9200d8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat_conversation",
        sa.Column("langgraph_thread_id", sa.String(length=64), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat_conversation", "langgraph_thread_id")
    # ### end Alembic commands ###
