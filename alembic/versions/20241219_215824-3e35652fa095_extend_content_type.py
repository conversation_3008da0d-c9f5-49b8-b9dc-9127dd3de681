"""extend content_type

Revision ID: 3e35652fa095
Revises: 0fc60a5b040b
Create Date: 2024-12-19 21:58:24.816675

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "3e35652fa095"
down_revision: Union[str, None] = "0fc60a5b040b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

content_type_old = mysql.ENUM(
    "init",
    "normal",
    "decision",
    "disasm_request",
    "disasm_report",
    "knowledge_base",
    "product_report",
    "choice",
    "derive_form",
    "derive_confirm",
)

content_type_new = sa.Enum(
    "init",
    "normal",
    "command",
    "decision",
    "disasm_request",
    "disasm_report",
    "knowledge_base",
    "product_report",
    "choice",
    "derive_form",
    "derive_confirm",
    name="contenttype",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=content_type_old,
        type_=content_type_new,
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=content_type_new,
        type_=content_type_old,
        existing_comment="文本类型",
        existing_nullable=False,
        existing_server_default=sa.text("'normal'"),
    )
    # ### end Alembic commands ###
