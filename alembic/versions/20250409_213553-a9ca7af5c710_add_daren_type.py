"""add daren type

Revision ID: a9ca7af5c710
Revises: 60713ec2edcc
Create Date: 2025-04-09 21:35:53.548205

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "a9ca7af5c710"
down_revision: Union[str, None] = "60713ec2edcc"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM(
            "user", "report", "research", "video", "text", "daren"
        ),
        comment="知识库类型",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM("user", "report", "research", "video", "text"),
        comment="知识库类型",
        existing_nullable=True,
    )
    # ### end Alembic commands ###
