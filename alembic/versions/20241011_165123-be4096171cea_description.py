"""description

Revision ID: be4096171cea
Revises: 9b9551bcd20b
Create Date: 2024-10-11 16:51:23.114636

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "be4096171cea"
down_revision: Union[str, None] = "9b9551bcd20b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("chat_ibfk_1", "chat", type_="foreignkey")
    op.execute("ALTER TABLE disassemble_tasks RENAME TO tasks")
    op.create_foreign_key(None, "chat", "tasks", ["task_id"], ["task_id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("chat_ibfk_1", "chat", type_="foreignkey")
    op.execute("ALTER TABLE tasks RENAME TO disassemble_tasks")
    op.create_foreign_key(None, "chat", "disassemble_tasks", ["task_id"], ["task_id"])
    # ### end Alembic commands ###
