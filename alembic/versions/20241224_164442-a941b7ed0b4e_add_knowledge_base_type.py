"""add_knowledge_base_type

Revision ID: a941b7ed0b4e
Revises: 48b705078a97
Create Date: 2024-12-19 13:44:42.081726

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "a941b7ed0b4e"
down_revision: Union[str, None] = "5cf6af60d6bb"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM(
            "user",
            "report",
            "research",
            "video",
        ),
        nullable=True,
        comment="知识库类型",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "knowledge_file",
        "knowledge_base_type",
        existing_type=mysql.ENUM(
            "user",
            "report",
            "research",
        ),
        nullable=True,
        comment="知识库类型",
    )
    # ### end Alembic commands ###
