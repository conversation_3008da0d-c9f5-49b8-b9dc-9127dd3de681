"""update chat content_type and conversation last_clear_time

Revision ID: 9ccd3f61eb95
Revises: fe9ef35f75b8
Create Date: 2025-03-18 15:50:05.058931

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "9ccd3f61eb95"
down_revision: Union[str, None] = "fe9ef35f75b8"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

content_type_old = sa.Enum(
    "init",
    "normal",
    "command",
    "decision",
    "disasm_request",
    "disasm_report",
    "knowledge_base",
    "product_report",
    "choice",
    "derive_form",
    "derive_confirm",
    name="content_type",
)

content_type_new = sa.Enum(
    "init",
    "normal",
    "command",
    "decision",
    "disasm_request",
    "disasm_report",
    "knowledge_base",
    "product_report",
    "choice",
    "derive_form",
    "derive_confirm",
    "clear_context",
    name="content_type",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "content_type",
        existing_type=content_type_old,
        type_=content_type_new,
        existing_nullable=False,
        nullable=False,
        existing_server_default=sa.text("'normal'"),
        server_default=sa.text("'normal'"),
        existing_comment="文本类型",
        comment="文本类型",
    )
    op.add_column(
        "chat_conversation",
        sa.Column(
            "last_clear_time",
            mysql.DATETIME(fsp=6),
            nullable=True,
            comment="最近一次清除上下文的时间",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat_conversation", "last_clear_time")
    op.alter_column(
        "chat",
        "content_type",
        existing_type=content_type_new,
        type_=content_type_old,
        existing_nullable=False,
        nullable=False,
        existing_server_default=sa.text("'normal'"),
        server_default=sa.text("'normal'"),
        existing_comment="文本类型",
        comment="文本类型",
    )
    # ### end Alembic commands ###
