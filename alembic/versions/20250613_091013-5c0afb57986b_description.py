"""description

Revision ID: 5c0afb57986b
Revises: 6f0924f8c565
Create Date: 2025-06-13 09:10:13.256188

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "5c0afb57986b"
down_revision: Union[str, None] = "6f0924f8c565"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "chat",
        sa.Column("is_deleted", sa.Integer(), nullable=False, comment="是否已删除"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("chat", "is_deleted")
    # ### end Alembic commands ###
