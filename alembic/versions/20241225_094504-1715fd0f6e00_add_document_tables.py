"""add_document_tables

Revision ID: 1715fd0f6e00
Revises: a941b7ed0b4e
Create Date: 2024-12-13 09:45:04.735526

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "1715fd0f6e00"
down_revision: Union[str, None] = "a941b7ed0b4e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_attachment",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("workspace_id", sa.String(length=64), nullable=False),
        sa.Column("name", sa.String(length=128), nullable=False),
        sa.Column("md5", sa.String(length=32), nullable=False),
        sa.Column("oss_url", sa.String(length=256), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_md5", "document_attachment", ["md5"], unique=False)
    op.create_index(
        "idx_workspace_id", "document_attachment", ["workspace_id"], unique=False
    )
    op.create_table(
        "document_doc",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("workspace_id", sa.String(length=64), nullable=False),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("folder_id", sa.String(length=64), server_default="", nullable=False),
        sa.Column("parent_id", sa.String(length=64), server_default="", nullable=False),
        sa.Column("tag_ids", sa.JSON(), nullable=False),
        sa.Column("trash", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("trash_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_folder_id", "document_doc", ["folder_id"], unique=False)
    op.create_index("idx_trash", "document_doc", ["trash"], unique=False)
    op.create_index("idx_workspace_id", "document_doc", ["workspace_id"], unique=False)
    op.create_table(
        "document_doc_favorite",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("doc_id", sa.String(length=64), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_doc_user", "document_doc_favorite", ["user_id", "doc_id"], unique=True
    )
    op.create_table(
        "document_doc_permission",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("doc_id", sa.String(length=64), nullable=False),
        sa.Column("type", sa.Integer(), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_doc_user", "document_doc_permission", ["user_id", "doc_id"], unique=True
    )
    op.create_table(
        "document_doc_snapshot",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("doc_id", sa.String(length=64), nullable=False),
        sa.Column("blob", sa.LargeBinary(1000000000), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("create_by", sa.Integer(), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_by", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_doc_id", "document_doc_snapshot", ["doc_id"], unique=True)
    op.create_table(
        "document_doc_snapshot_history",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("doc_id", sa.String(length=64), nullable=False),
        sa.Column("blob", sa.LargeBinary(1000000000), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("create_by", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_doc_id", "document_doc_snapshot_history", ["doc_id"], unique=False
    )
    op.create_table(
        "document_doc_update",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("doc_id", sa.String(length=64), nullable=False),
        sa.Column("blob", sa.LargeBinary(1000000000), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("create_by", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_doc_id", "document_doc_update", ["doc_id"], unique=False)
    op.create_table(
        "document_workspace",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "document_workspace_folder",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("workspace_id", sa.String(length=64), nullable=False),
        sa.Column("parent_id", sa.String(length=64), server_default="", nullable=False),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("index", sa.String(length=64), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_parent_id", "document_workspace_folder", ["parent_id"], unique=False
    )
    op.create_index(
        "idx_workspace_id", "document_workspace_folder", ["workspace_id"], unique=False
    )
    op.create_table(
        "document_workspace_permission",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("workspace_id", sa.String(length=64), nullable=False),
        sa.Column("type", sa.Integer(), nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_workspace_user",
        "document_workspace_permission",
        ["user_id", "workspace_id"],
        unique=True,
    )
    op.create_table(
        "document_workspace_tag",
        sa.Column("id", sa.String(length=64), nullable=False),
        sa.Column("workspace_id", sa.String(length=64), nullable=False),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("color", sa.String(length=64), server_default="red", nullable=False),
        sa.Column("create_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(timezone=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_workspace_id", "document_workspace_tag", ["workspace_id"], unique=False
    )
    op.create_index(
        "idx_workspace_tag",
        "document_workspace_tag",
        ["workspace_id", "name"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_workspace_tag", table_name="document_workspace_tag")
    op.drop_index("idx_workspace_id", table_name="document_workspace_tag")
    op.drop_table("document_workspace_tag")
    op.drop_index("idx_workspace_user", table_name="document_workspace_permission")
    op.drop_table("document_workspace_permission")
    op.drop_index("idx_workspace_id", table_name="document_workspace_folder")
    op.drop_index("idx_parent_id", table_name="document_workspace_folder")
    op.drop_table("document_workspace_folder")
    op.drop_table("document_workspace")
    op.drop_index("idx_doc_id", table_name="document_doc_update")
    op.drop_table("document_doc_update")
    op.drop_index("idx_doc_id", table_name="document_doc_snapshot_history")
    op.drop_table("document_doc_snapshot_history")
    op.drop_index("idx_doc_id", table_name="document_doc_snapshot")
    op.drop_table("document_doc_snapshot")
    op.drop_index("idx_doc_user", table_name="document_doc_permission")
    op.drop_table("document_doc_permission")
    op.drop_index("idx_doc_user", table_name="document_doc_favorite")
    op.drop_table("document_doc_favorite")
    op.drop_index("idx_workspace_id", table_name="document_doc")
    op.drop_index("idx_trash", table_name="document_doc")
    op.drop_index("idx_folder_id", table_name="document_doc")
    op.drop_table("document_doc")
    op.drop_index("idx_workspace_id", table_name="document_attachment")
    op.drop_index("idx_md5", table_name="document_attachment")
    op.drop_table("document_attachment")
    # ### end Alembic commands ###
