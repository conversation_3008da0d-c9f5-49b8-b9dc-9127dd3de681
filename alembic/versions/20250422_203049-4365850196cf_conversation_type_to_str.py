"""conversation type to str

Revision ID: 4365850196cf
Revises: a9ca7af5c710
Create Date: 2025-04-22 20:30:49.370949

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "4365850196cf"
down_revision: Union[str, None] = "a9ca7af5c710"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "content_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
        ),
        type_=sa.String(length=64),
        existing_comment="会话类型",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=sa.String(length=64),
        type_=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "content_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
        ),
        existing_comment="会话类型",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
