"""add search agent

Revision ID: 5cf6af60d6bb
Revises: 48b705078a97
Create Date: 2024-12-23 16:44:47.894533

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "5cf6af60d6bb"
down_revision: Union[str, None] = "48b705078a97"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
        ),
        type_=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
            name="conversationtype",
        ),
        nullable=False,
        comment="会话类型",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
            "search_agent",
            name="conversationtype",
        ),
        type_=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "house_keeper",
            "internal",
        ),
        nullable=True,
        comment=None,
        existing_comment="会话类型",
    )
    # ### end Alembic commands ###
