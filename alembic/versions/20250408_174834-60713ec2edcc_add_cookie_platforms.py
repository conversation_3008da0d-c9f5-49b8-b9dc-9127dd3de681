"""add cookie platforms

Revision ID: 60713ec2edcc
Revises: ced8ffb5be36
Create Date: 2025-04-08 17:48:34.945347

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "60713ec2edcc"
down_revision: Union[str, None] = "ced8ffb5be36"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

old_platforms = mysql.ENUM(
    "douhot",
    "luopan",
    "douyin",
    "chanmama",
)
new_platforms = sa.Enum(
    "douhot",
    "luopan",
    "luopan_qita",
    "douyin",
    "chanmama",
    "xinhong",
    "xiaohongshu",
    "trendinsight",
    "temp1",
    "temp2",
    "temp3",
    name="platformtype",
)


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=old_platforms,
        type_=new_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "task_assignment",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    op.alter_column(
        "cookie_pool",
        "platform",
        existing_type=new_platforms,
        type_=old_platforms,
        existing_comment="账户平台",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
