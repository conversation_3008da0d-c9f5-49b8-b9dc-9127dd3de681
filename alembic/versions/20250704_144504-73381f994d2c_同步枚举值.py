"""同步枚举值

Revision ID: 73381f994d2c
Revises: d7156050e010
Create Date: 2025-07-04 14:45:04.525792

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "73381f994d2c"
down_revision: Union[str, None] = "d7156050e010"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "read_assistant_tasks",
        "task_type",
        existing_type=sa.Enum("read_report", name="tasktype"),
        type_=sa.Enum(
            "select",
            "derive",
            "make_checklist",
            "estimate_cost",
            "derive_outline",
            "derive_audience",
            "derive_detail",
            "suggest_similar",
            "success_case",
            "ai_chat",
            "read_report",
            "product_report",
            name="tasktype",
        ),
        existing_nullable=False,
        existing_server_default=sa.text("'read_report'"),
    )
    op.alter_column(
        "tasks",
        "task_type",
        existing_type=sa.Enum(
            "derive",
            "read_report",
            "product_report",
            "select",
            "video_analysis",
            name="tasktype",
        ),
        type_=sa.Enum(
            "select",
            "derive",
            "make_checklist",
            "estimate_cost",
            "derive_outline",
            "derive_audience",
            "derive_detail",
            "suggest_similar",
            "success_case",
            "ai_chat",
            "read_report",
            "product_report",
            name="tasktype",
        ),
        existing_comment="任务类型",
        existing_nullable=False,
        existing_server_default=sa.text("'select'"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "tasks",
        "task_type",
        existing_type=sa.Enum(
            "select",
            "derive",
            "make_checklist",
            "estimate_cost",
            "derive_outline",
            "derive_audience",
            "derive_detail",
            "suggest_similar",
            "success_case",
            "ai_chat",
            "read_report",
            "product_report",
            name="tasktype",
        ),
        type_=sa.Enum(
            "derive",
            "read_report",
            "product_report",
            "select",
            "video_analysis",
            name="tasktype",
        ),
        existing_comment="任务类型",
        existing_nullable=False,
        existing_server_default=sa.text("'select'"),
    )
    op.alter_column(
        "read_assistant_tasks",
        "task_type",
        existing_type=sa.Enum(
            "select",
            "derive",
            "make_checklist",
            "estimate_cost",
            "derive_outline",
            "derive_audience",
            "derive_detail",
            "suggest_similar",
            "success_case",
            "ai_chat",
            "read_report",
            "product_report",
            name="tasktype",
        ),
        type_=sa.Enum("read_report", name="tasktype"),
        existing_nullable=False,
        existing_server_default=sa.text("'read_report'"),
    )
    # ### end Alembic commands ###
