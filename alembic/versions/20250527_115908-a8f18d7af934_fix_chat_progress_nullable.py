"""fix chat progress nullable

Revision ID: a8f18d7af934
Revises: ba096984757d
Create Date: 2025-05-27 11:59:08.126663

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "a8f18d7af934"
down_revision: Union[str, None] = "ba096984757d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "progress",
        existing_type=mysql.FLOAT(),
        nullable=False,
        existing_comment="处理进度",
        existing_server_default="1",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat",
        "progress",
        existing_type=mysql.FLOAT(),
        nullable=True,
        existing_comment="处理进度",
        existing_server_default="1",
    )
    # ### end Alembic commands ###
