"""增加视频推荐反馈表

Revision ID: 39c8154aa307
Revises: 3ec54a21ec0b
Create Date: 2025-07-31 13:19:08.949285

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "39c8154aa307"
down_revision: Union[str, None] = "3ec54a21ec0b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "entity_recommend_feedback",
        sa.Column("id", sa.<PERSON>nteger(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("entity_type", sa.String(length=32), nullable=False),
        sa.Column("entity_id", sa.String(length=128), nullable=True),
        sa.Column("entity_name", sa.String(length=255), nullable=True),
        sa.Column("entity_link", sa.String(length=512), nullable=True),
        sa.Column("chat_id", sa.String(length=64), nullable=False),
        sa.Column("conversation_id", sa.String(length=64), nullable=False),
        sa.Column("langfuse_trace_id", sa.String(length=64), nullable=True),
        sa.Column("history_messages", sa.JSON(), nullable=True),
        sa.Column(
            "feedback_type",
            sa.String(length=128),
            server_default="不准确",
            nullable=False,
        ),
        sa.Column("additional_info", sa.Text(), nullable=True),
        sa.Column("is_processed", sa.Boolean(), server_default="0", nullable=False),
        sa.Column("create_time", mysql.DATETIME(fsp=6), nullable=False),
        sa.Column("update_time", mysql.DATETIME(fsp=6), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_is_processed", "entity_recommend_feedback", ["is_processed"], unique=False
    )
    op.create_index(
        "idx_user_entity",
        "entity_recommend_feedback",
        ["user_id", "entity_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_entity_recommend_feedback_entity_type"),
        "entity_recommend_feedback",
        ["entity_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_entity_recommend_feedback_user_id"),
        "entity_recommend_feedback",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_entity_recommend_feedback_user_id"),
        table_name="entity_recommend_feedback",
    )
    op.drop_index(
        op.f("ix_entity_recommend_feedback_entity_type"),
        table_name="entity_recommend_feedback",
    )
    op.drop_index("idx_user_entity", table_name="entity_recommend_feedback")
    op.drop_index("idx_is_processed", table_name="entity_recommend_feedback")
    op.drop_table("entity_recommend_feedback")
    # ### end Alembic commands ###
