"""add internal_conversation_type

Revision ID: 1d52983edf71
Revises: ae69cc0cd188
Create Date: 2024-11-04 12:08:03.542047

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "1d52983edf71"
down_revision: Union[str, None] = "ae69cc0cd188"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
        ),
        type_=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "internal",
            name="conversationtype",
        ),
        existing_comment="会话类型",
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "conversation_type",
        existing_type=sa.Enum(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
            "internal",
            name="conversationtype",
        ),
        type_=mysql.ENUM(
            "demo",
            "comprehensive",
            "assistant",
            "knowledge_base",
            "plan_assistant",
            "product_assistant",
            "notification",
        ),
        existing_comment="会话类型",
        existing_nullable=False,
    )
    # ### end Alembic commands ###
