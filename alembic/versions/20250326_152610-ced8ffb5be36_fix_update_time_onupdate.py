"""fix update_time onupdate

Revision ID: ced8ffb5be36
Revises: 39c01aa1acce
Create Date: 2025-03-26 15:26:10.620644

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = "ced8ffb5be36"
down_revision: Union[str, None] = "39c01aa1acce"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "chat_conversation",
        "update_time",
        existing_type=mysql.DATETIME(),
        existing_comment=None,
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    op.alter_column(
        "cookie_pool",
        "update_time",
        existing_type=mysql.DATETIME(),
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
    )
    op.alter_column(
        "ip_pool",
        "update_time",
        existing_type=mysql.DATETIME(),
        existing_comment=None,
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    op.alter_column(
        "task_assignment",
        "update_time",
        existing_type=mysql.DATETIME(),
        existing_comment=None,
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        comment="更新时间",
    )
    op.alter_column(
        "ip_cookie_binding",
        "update_time",
        existing_type=mysql.DATETIME(),
        existing_comment="更新时间",
        existing_nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
        server_default=sa.text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
